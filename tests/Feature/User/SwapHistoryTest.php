<?php

namespace Tests\Feature\User;

use App\Models\Coin;
use App\Models\Transaction;
use App\Models\UsdPrice;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class SwapHistoryTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_view_swap_history()
    {
        // Create a user
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create USD price
        $usdPrice = UsdPrice::create([
            'buy_price' => 50000, // 50,000 تومان
            'sell_price' => 49000, // 49,000 تومان
            'is_active' => true
        ]);

        // Create two coins
        $fromCoin = Coin::create([
            'name' => 'Tron',
            'coin_type' => 'TRX',
            'coin_price' => 0.1, // 0.1 USD
            'status' => true
        ]);

        $toCoin = Coin::create([
            'name' => 'Ethereum',
            'coin_type' => 'ETH',
            'coin_price' => 2000, // 2000 USD
            'status' => true
        ]);

        // Create wallets
        $fromWallet = Wallet::create([
            'user_id' => $user->id,
            'coin_id' => $fromCoin->id,
            'name' => $fromCoin->name,
            'balance' => 900, // After swap
            'status' => 'active'
        ]);

        $toWallet = Wallet::create([
            'user_id' => $user->id,
            'coin_id' => $toCoin->id,
            'name' => $toCoin->name,
            'balance' => 0.00497, // After swap
            'status' => 'active'
        ]);

        // Create swap transactions
        $swapOutTransaction = Transaction::create([
            'type' => 'swap_out',
            'amount' => 100,
            'price' => 10, // USD value
            'wallet_id' => $fromWallet->id,
            'currency_id' => $fromCoin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'تبدیل ارز',
            'details' => json_encode([
                'from_currency' => $fromCoin->coin_type,
                'to_currency' => $toCoin->coin_type,
                'from_amount' => 100,
                'to_amount' => 0.00497,
                'usd_value' => 10,
                'fee_percentage' => '0.5%',
                'fee_amount' => 0.000025
            ]),
            'balance_before' => 1000,
            'balance_after' => 900
        ]);

        $swapInTransaction = Transaction::create([
            'type' => 'swap_in',
            'amount' => 0.00497,
            'price' => 10, // USD value
            'wallet_id' => $toWallet->id,
            'currency_id' => $toCoin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'دریافت ارز از تبدیل',
            'details' => json_encode([
                'from_currency' => $fromCoin->coin_type,
                'to_currency' => $toCoin->coin_type,
                'from_amount' => 100,
                'to_amount' => 0.00497,
                'usd_value' => 10,
                'fee_percentage' => '0.5%',
                'fee_amount' => 0.000025
            ]),
            'balance_before' => 0,
            'balance_after' => 0.00497
        ]);

        // Make the request to get swap history
        $response = $this->getJson('/api/user/trading/swap-history');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'data' => [
                        [
                            'id' => $swapOutTransaction->id,
                            'type' => 'swap_out',
                            'amount' => 100,
                            'from_amount' => 100,
                            'to_amount' => 0.00497,
                            'fee_percentage' => '0.5%',
                            'fee_amount' => 0.000025,
                            'usd_value' => 10
                        ]
                    ]
                ]
            ]);
    }

    public function test_user_can_filter_swap_history_by_date()
    {
        // Create a user
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create coins
        $fromCoin = Coin::create([
            'name' => 'Tron',
            'coin_type' => 'TRX',
            'coin_price' => 0.1,
            'status' => true
        ]);

        $toCoin = Coin::create([
            'name' => 'Ethereum',
            'coin_type' => 'ETH',
            'coin_price' => 2000,
            'status' => true
        ]);

        // Create wallets
        $fromWallet = Wallet::create([
            'user_id' => $user->id,
            'coin_id' => $fromCoin->id,
            'name' => $fromCoin->name,
            'balance' => 900,
            'status' => 'active'
        ]);

        $toWallet = Wallet::create([
            'user_id' => $user->id,
            'coin_id' => $toCoin->id,
            'name' => $toCoin->name,
            'balance' => 0.00497,
            'status' => 'active'
        ]);

        // Create swap transactions with different dates
        $oldTransaction = Transaction::create([
            'type' => 'swap_out',
            'amount' => 100,
            'price' => 10,
            'wallet_id' => $fromWallet->id,
            'currency_id' => $fromCoin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'تبدیل ارز',
            'details' => json_encode([
                'from_currency' => $fromCoin->coin_type,
                'to_currency' => $toCoin->coin_type,
                'from_amount' => 100,
                'to_amount' => 0.00497,
                'usd_value' => 10,
                'fee_percentage' => '0.5%',
                'fee_amount' => 0.000025
            ]),
            'balance_before' => 1000,
            'balance_after' => 900,
            'created_at' => now()->subDays(10)
        ]);

        $newTransaction = Transaction::create([
            'type' => 'swap_out',
            'amount' => 50,
            'price' => 5,
            'wallet_id' => $fromWallet->id,
            'currency_id' => $fromCoin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'تبدیل ارز',
            'details' => json_encode([
                'from_currency' => $fromCoin->coin_type,
                'to_currency' => $toCoin->coin_type,
                'from_amount' => 50,
                'to_amount' => 0.00248,
                'usd_value' => 5,
                'fee_percentage' => '0.5%',
                'fee_amount' => 0.000012
            ]),
            'balance_before' => 950,
            'balance_after' => 900,
            'created_at' => now()->subDay()
        ]);

        // Filter by date range that includes only the new transaction
        $response = $this->getJson('/api/user/trading/swap-history?from_date=' . now()->subDays(5)->format('Y-m-d'));

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'data' => [
                        [
                            'id' => $newTransaction->id,
                            'amount' => 50
                        ]
                    ]
                ]
            ])
            ->assertJsonMissing([
                'data' => [
                    'data' => [
                        [
                            'id' => $oldTransaction->id,
                            'amount' => 100
                        ]
                    ]
                ]
            ]);
    }
}
