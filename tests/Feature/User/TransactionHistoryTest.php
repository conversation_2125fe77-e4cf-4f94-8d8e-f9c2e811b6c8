<?php

namespace Tests\Feature\User;

use App\Models\Coin;
use App\Models\Transaction;
use App\Models\UsdPrice;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class TransactionHistoryTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_view_all_transactions()
    {
        // Create a user
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create USD price
        $usdPrice = UsdPrice::create([
            'buy_price' => 50000, // 50,000 تومان
            'sell_price' => 49000, // 49,000 تومان
            'is_active' => true
        ]);

        // Create a coin
        $coin = Coin::create([
            'name' => 'Bitcoin',
            'coin_type' => 'BTC',
            'coin_price' => 50000, // 50,000 USD
            'status' => true
        ]);

        // Create a wallet
        $wallet = Wallet::create([
            'user_id' => $user->id,
            'coin_id' => $coin->id,
            'name' => $coin->name,
            'balance' => 1.5, // 1.5 BTC
            'status' => 'active'
        ]);

        // Create different types of transactions
        // 1. Buy transaction
        $buyTransaction = Transaction::create([
            'type' => 'buy',
            'amount' => 0.5, // 0.5 BTC
            'price' => 25000000, // 25,000,000 تومان
            'wallet_id' => $wallet->id,
            'currency_id' => $coin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'خرید ارز',
            'details' => json_encode([
                'toman_amount' => 25000000,
                'usd_amount' => 500,
                'usd_rate' => 50000,
                'crypto_amount' => 0.5
            ]),
            'balance_before' => 1.0,
            'balance_after' => 1.5
        ]);

        // 2. Sell transaction
        $sellTransaction = Transaction::create([
            'type' => 'sell',
            'amount' => 0.2, // 0.2 BTC
            'price' => 10000000, // 10,000,000 تومان
            'wallet_id' => $wallet->id,
            'currency_id' => $coin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'فروش ارز',
            'details' => json_encode([
                'crypto_amount' => 0.2,
                'usd_amount' => 200,
                'usd_rate' => 49000,
                'toman_amount' => 10000000
            ]),
            'balance_before' => 1.7,
            'balance_after' => 1.5
        ]);

        // 3. Deposit transaction
        $depositTransaction = Transaction::create([
            'type' => 'deposit',
            'amount' => 0.3, // 0.3 BTC
            'price' => 15000, // USD value
            'wallet_id' => $wallet->id,
            'currency_id' => $coin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'واریز ارز',
            'details' => json_encode([
                'transaction_id' => 'tx_123456789',
                'network' => 'Bitcoin',
                'from_address' => '**********************************',
                'to_address' => '**********************************'
            ]),
            'balance_before' => 1.2,
            'balance_after' => 1.5
        ]);

        // Make the request to get all transactions
        $response = $this->getJson('/api/user/trading/transactions');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'data' => [
                        [
                            'id' => $depositTransaction->id,
                            'type' => 'deposit',
                            'amount' => 0.3,
                            'type_description' => 'واریز'
                        ],
                        [
                            'id' => $sellTransaction->id,
                            'type' => 'sell',
                            'amount' => 0.2,
                            'type_description' => 'فروش'
                        ],
                        [
                            'id' => $buyTransaction->id,
                            'type' => 'buy',
                            'amount' => 0.5,
                            'type_description' => 'خرید'
                        ]
                    ]
                ]
            ]);
    }

    public function test_user_can_filter_transactions_by_type()
    {
        // Create a user
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create a coin
        $coin = Coin::create([
            'name' => 'Bitcoin',
            'coin_type' => 'BTC',
            'coin_price' => 50000,
            'status' => true
        ]);

        // Create a wallet
        $wallet = Wallet::create([
            'user_id' => $user->id,
            'coin_id' => $coin->id,
            'name' => $coin->name,
            'balance' => 1.5,
            'status' => 'active'
        ]);

        // Create different types of transactions
        $buyTransaction = Transaction::create([
            'type' => 'buy',
            'amount' => 0.5,
            'price' => 25000000,
            'wallet_id' => $wallet->id,
            'currency_id' => $coin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'خرید ارز',
            'details' => json_encode([
                'toman_amount' => 25000000,
                'usd_amount' => 500,
                'usd_rate' => 50000,
                'crypto_amount' => 0.5
            ]),
            'balance_before' => 1.0,
            'balance_after' => 1.5
        ]);

        $sellTransaction = Transaction::create([
            'type' => 'sell',
            'amount' => 0.2,
            'price' => 10000000,
            'wallet_id' => $wallet->id,
            'currency_id' => $coin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'فروش ارز',
            'details' => json_encode([
                'crypto_amount' => 0.2,
                'usd_amount' => 200,
                'usd_rate' => 49000,
                'toman_amount' => 10000000
            ]),
            'balance_before' => 1.7,
            'balance_after' => 1.5
        ]);

        // Filter by 'buy' type
        $response = $this->getJson('/api/user/trading/transactions?type=buy');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'data' => [
                        [
                            'id' => $buyTransaction->id,
                            'type' => 'buy',
                            'amount' => 0.5
                        ]
                    ]
                ]
            ])
            ->assertJsonMissing([
                'data' => [
                    'data' => [
                        [
                            'id' => $sellTransaction->id,
                            'type' => 'sell'
                        ]
                    ]
                ]
            ]);
    }

    public function test_user_can_filter_transactions_by_date_range()
    {
        // Create a user
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create a coin
        $coin = Coin::create([
            'name' => 'Bitcoin',
            'coin_type' => 'BTC',
            'coin_price' => 50000,
            'status' => true
        ]);

        // Create a wallet
        $wallet = Wallet::create([
            'user_id' => $user->id,
            'coin_id' => $coin->id,
            'name' => $coin->name,
            'balance' => 1.5,
            'status' => 'active'
        ]);

        // Create transactions with different dates
        $oldTransaction = Transaction::create([
            'type' => 'buy',
            'amount' => 0.5,
            'price' => 25000000,
            'wallet_id' => $wallet->id,
            'currency_id' => $coin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'خرید ارز',
            'details' => json_encode([
                'toman_amount' => 25000000,
                'usd_amount' => 500,
                'usd_rate' => 50000,
                'crypto_amount' => 0.5
            ]),
            'balance_before' => 1.0,
            'balance_after' => 1.5,
            'created_at' => now()->subDays(30)
        ]);

        $newTransaction = Transaction::create([
            'type' => 'buy',
            'amount' => 0.3,
            'price' => 15000000,
            'wallet_id' => $wallet->id,
            'currency_id' => $coin->id,
            'user_id' => $user->id,
            'registrar' => $user->id,
            'status' => 'done',
            'description' => 'خرید ارز',
            'details' => json_encode([
                'toman_amount' => 15000000,
                'usd_amount' => 300,
                'usd_rate' => 50000,
                'crypto_amount' => 0.3
            ]),
            'balance_before' => 1.2,
            'balance_after' => 1.5,
            'created_at' => now()->subDays(5)
        ]);

        // Filter by date range that includes only the new transaction
        $response = $this->getJson('/api/user/trading/transactions?from_date=' . now()->subDays(10)->format('Y-m-d'));

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'data' => [
                        [
                            'id' => $newTransaction->id,
                            'amount' => 0.3
                        ]
                    ]
                ]
            ])
            ->assertJsonMissing([
                'data' => [
                    'data' => [
                        [
                            'id' => $oldTransaction->id,
                            'amount' => 0.5
                        ]
                    ]
                ]
            ]);
    }
}
