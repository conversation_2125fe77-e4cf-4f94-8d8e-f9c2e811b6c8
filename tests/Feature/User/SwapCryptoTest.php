<?php

namespace Tests\Feature\User;

use App\Models\Coin;
use App\Models\UsdPrice;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class SwapCryptoTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_swap_cryptocurrency()
    {
        // Create a user
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create USD price
        $usdPrice = UsdPrice::create([
            'buy_price' => 50000, // 50,000 تومان
            'sell_price' => 49000, // 49,000 تومان
            'is_active' => true
        ]);

        // Create two coins
        $fromCoin = Coin::create([
            'name' => 'Tron',
            'coin_type' => 'TRX',
            'coin_price' => 0.1, // 0.1 USD
            'status' => true
        ]);

        $toCoin = Coin::create([
            'name' => 'Ethereum',
            'coin_type' => 'ETH',
            'coin_price' => 2000, // 2000 USD
            'status' => true
        ]);

        // Create a wallet for the source coin with some balance
        $fromWallet = Wallet::create([
            'user_id' => $user->id,
            'coin_id' => $fromCoin->id,
            'name' => $fromCoin->name,
            'balance' => 1000, // 1000 TRX
            'status' => 'active'
        ]);

        // Make the swap request
        $response = $this->postJson('/api/user/trading/swap', [
            'from_currency_id' => $fromCoin->id,
            'to_currency_id' => $toCoin->id,
            'amount' => 100 // Swap 100 TRX
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ]);

        // Check that the source wallet balance was reduced
        $this->assertDatabaseHas('wallets', [
            'id' => $fromWallet->id,
            'balance' => 900 // 1000 - 100
        ]);

        // Check that a destination wallet was created with the correct balance
        // 100 TRX = 10 USD, which is 0.005 ETH (10/2000), minus 0.5% fee = 0.004975 ETH
        $this->assertDatabaseHas('wallets', [
            'user_id' => $user->id,
            'coin_id' => $toCoin->id,
        ]);

        // Check that two transactions were created
        $this->assertDatabaseHas('transactions', [
            'user_id' => $user->id,
            'type' => 'swap_out',
            'amount' => 100,
            'currency_id' => $fromCoin->id
        ]);

        $this->assertDatabaseHas('transactions', [
            'user_id' => $user->id,
            'type' => 'swap_in',
            'currency_id' => $toCoin->id
        ]);
    }

    public function test_user_cannot_swap_with_insufficient_balance()
    {
        // Create a user
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create USD price
        $usdPrice = UsdPrice::create([
            'buy_price' => 50000,
            'sell_price' => 49000,
            'is_active' => true
        ]);

        // Create two coins
        $fromCoin = Coin::create([
            'name' => 'Tron',
            'coin_type' => 'TRX',
            'coin_price' => 0.1,
            'status' => true
        ]);

        $toCoin = Coin::create([
            'name' => 'Ethereum',
            'coin_type' => 'ETH',
            'coin_price' => 2000,
            'status' => true
        ]);

        // Create a wallet for the source coin with insufficient balance
        $fromWallet = Wallet::create([
            'user_id' => $user->id,
            'coin_id' => $fromCoin->id,
            'name' => $fromCoin->name,
            'balance' => 50, // Only 50 TRX
            'status' => 'active'
        ]);

        // Make the swap request for more than available
        $response = $this->postJson('/api/user/trading/swap', [
            'from_currency_id' => $fromCoin->id,
            'to_currency_id' => $toCoin->id,
            'amount' => 100 // Try to swap 100 TRX
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => false,
                'message' => 'موجودی ارز کافی نیست'
            ]);

        // Check that the wallet balance was not changed
        $this->assertDatabaseHas('wallets', [
            'id' => $fromWallet->id,
            'balance' => 50
        ]);
    }
}
