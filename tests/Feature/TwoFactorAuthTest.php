<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class TwoFactorAuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test getting 2FA status.
     */
    public function test_get_2fa_status(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->getJson('/api/user/2fa/status');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'enabled',
                    'has_secret'
                ]
            ]);
    }

    /**
     * Test generating a new 2FA secret.
     */
    public function test_generate_2fa_secret(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->getJson('/api/user/2fa/generate');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'secret',
                    'qr_code_url'
                ],
                'message'
            ]);
    }

    /**
     * Test enabling 2FA with invalid code.
     */
    public function test_enable_2fa_with_invalid_code(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->postJson('/api/user/2fa/enable', [
                'code' => '123456',
                'secret' => 'FAKESECRET'
            ]);

        $response->assertStatus(422);
    }

    /**
     * Test disabling 2FA with invalid code.
     */
    public function test_disable_2fa_with_invalid_code(): void
    {
        $user = User::factory()->create([
            'g2f_enabled' => true,
            'google2fa_secret' => 'FAKESECRET'
        ]);

        $response = $this->actingAs($user)
            ->postJson('/api/user/2fa/disable', [
                'code' => '123456'
            ]);

        $response->assertStatus(422);
    }
}
