<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Card;
use App\Models\Bank;
use App\Models\JibitPayment;
use App\Services\JibitPaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Lara<PERSON>\Sanctum\Sanctum;

class JibitPaymentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $jibitService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->jibitService = app(JibitPaymentService::class);
        
        // Create a test user
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'phone' => '***********',
            'email' => '<EMAIL>'
        ]);

        // Create a test bank
        $bank = Bank::create([
            'name' => 'Test Bank',
            'prefix' => '123456'
        ]);

        // Create approved card with IBAN for the user
        Card::create([
            'user_id' => $this->user->id,
            'bank_id' => $bank->id,
            'number' => '****************',
            'iban' => '**************************',
            'status' => 'approved'
        ]);
    }

    /** @test */
    public function user_can_create_jibit_payment_identifier()
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/user/jibit-payment/create', [
            'description' => 'Test payment'
        ]);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'payment_id',
                        'payment_identifier',
                        'merchant_reference_number',
                        'currency',
                        'user_ibans'
                    ],
                    'message'
                ]);

        $this->assertDatabaseHas('jibit_payments', [
            'user_id' => $this->user->id,
            'status' => 'pending',
            'amount' => 0
        ]);
    }

    /** @test */
    public function user_cannot_create_payment_without_approved_cards()
    {
        // Create user without approved cards
        $userWithoutCards = User::factory()->create([
            'name' => 'User Without Cards',
            'phone' => '09987654321'
        ]);

        Sanctum::actingAs($userWithoutCards);

        $response = $this->postJson('/api/user/jibit-payment/create', [
            'description' => 'Test payment'
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'لطفاً ابتدا حداقل یک کارت بانکی با شماره شبا (IBAN) را در پروفایل خود ثبت و تایید کنید'
                ]);
    }

    /** @test */
    public function user_can_get_payment_status()
    {
        $payment = JibitPayment::create([
            'user_id' => $this->user->id,
            'payment_identifier' => 'TEST_PAYMENT_123',
            'amount' => 50000,
            'currency' => 'T',
            'description' => 'Test payment',
            'status' => 'pending'
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->getJson("/api/user/jibit-payment/{$payment->payment_identifier}/status");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'payment_id' => $payment->id,
                        'payment_identifier' => 'TEST_PAYMENT_123',
                        'amount' => 50000,
                        'status' => 'pending'
                    ]
                ]);
    }

    /** @test */
    public function user_can_get_payment_history()
    {
        // Create multiple payments for the user
        JibitPayment::factory()->count(3)->create([
            'user_id' => $this->user->id
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/user/jibit-payment/history');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'payment_identifier',
                            'amount',
                            'status',
                            'created_at'
                        ]
                    ],
                    'pagination'
                ]);
    }

    /** @test */
    public function user_can_get_payment_statistics()
    {
        // Create payments with different statuses
        JibitPayment::create([
            'user_id' => $this->user->id,
            'payment_identifier' => 'PAID_1',
            'amount' => 100000,
            'status' => 'paid'
        ]);

        JibitPayment::create([
            'user_id' => $this->user->id,
            'payment_identifier' => 'PENDING_1',
            'amount' => 50000,
            'status' => 'pending'
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/user/jibit-payment/stats');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'total_payments' => 2,
                        'successful_payments' => 1,
                        'pending_payments' => 1,
                        'failed_payments' => 0,
                        'total_amount_paid' => 100000
                    ]
                ]);
    }

    /** @test */
    public function user_can_cancel_pending_payment()
    {
        $payment = JibitPayment::create([
            'user_id' => $this->user->id,
            'payment_identifier' => 'CANCEL_TEST_123',
            'amount' => 25000,
            'status' => 'pending'
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->postJson("/api/user/jibit-payment/{$payment->payment_identifier}/cancel");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'پرداخت با موفقیت لغو شد'
                ]);

        $this->assertDatabaseHas('jibit_payments', [
            'id' => $payment->id,
            'status' => 'cancelled'
        ]);
    }

    /** @test */
    public function user_cannot_access_other_users_payments()
    {
        $otherUser = User::factory()->create();
        $otherPayment = JibitPayment::create([
            'user_id' => $otherUser->id,
            'payment_identifier' => 'OTHER_USER_PAYMENT',
            'amount' => 75000,
            'status' => 'pending'
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->getJson("/api/user/jibit-payment/{$otherPayment->payment_identifier}/status");

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'message' => 'شناسه پرداخت یافت نشد'
                ]);
    }
}
