<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class TradingTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test buying cryptocurrency with <PERSON><PERSON> balance.
     */
    public function test_user_can_buy_crypto_with_toman_balance(): void
    {
        // Create a user with Toman balance
        $user = User::factory()->create([
            'toman_balance' => 1000000 // 1,000,000 Toman
        ]);

        // Create a currency
        $currency = Currency::factory()->create([
            'name' => 'Bitcoin',
            'code' => 'BTC',
            'buy' => 100000, // 100,000 Toman per unit
            'sell' => 95000, // 95,000 Toman per unit
        ]);

        // Authenticate as the user
        $this->actingAs($user);

        // Make a buy request
        $response = $this->postJson('/api/user/trading/buy', [
            'currency_id' => $currency->id,
            'amount' => 0.01 // Buy 0.01 BTC
        ]);

        // Assert the response is successful
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'خرید با موفقیت انجام شد'
            ]);

        // Assert the user's Toman balance was deducted
        $this->assertEquals(999000, $user->fresh()->toman_balance);

        // Assert a wallet was created with the correct balance
        $wallet = Wallet::where('user_id', $user->id)
            ->where('coin_id', $currency->id)
            ->first();
        $this->assertNotNull($wallet);
        $this->assertEquals(0.01, $wallet->balance);

        // Assert a transaction was created
        $this->assertDatabaseHas('transactions', [
            'user_id' => $user->id,
            'currency_id' => $currency->id,
            'type' => 'buy',
            'amount' => 0.01,
            'price' => 1000, // 0.01 * 100000
            'status' => 'done'
        ]);
    }

    /**
     * Test selling cryptocurrency for Toman.
     */
    public function test_user_can_sell_crypto_for_toman(): void
    {
        // Create a user
        $user = User::factory()->create([
            'toman_balance' => 500000 // 500,000 Toman
        ]);

        // Create a currency
        $currency = Currency::factory()->create([
            'name' => 'Bitcoin',
            'code' => 'BTC',
            'buy' => 100000, // 100,000 Toman per unit
            'sell' => 95000, // 95,000 Toman per unit
        ]);

        // Create a wallet with some balance
        $wallet = Wallet::factory()->create([
            'user_id' => $user->id,
            'coin_id' => $currency->id,
            'balance' => 0.05 // 0.05 BTC
        ]);

        // Authenticate as the user
        $this->actingAs($user);

        // Make a sell request
        $response = $this->postJson('/api/user/trading/sell', [
            'currency_id' => $currency->id,
            'amount' => 0.02 // Sell 0.02 BTC
        ]);

        // Assert the response is successful
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'فروش با موفقیت انجام شد'
            ]);

        // Assert the user's Toman balance was increased
        $this->assertEquals(501900, $user->fresh()->toman_balance); // 500000 + (0.02 * 95000)

        // Assert the wallet balance was decreased
        $this->assertEquals(0.03, $wallet->fresh()->balance); // 0.05 - 0.02

        // Assert a transaction was created
        $this->assertDatabaseHas('transactions', [
            'user_id' => $user->id,
            'currency_id' => $currency->id,
            'type' => 'sell',
            'amount' => 0.02,
            'price' => 1900, // 0.02 * 95000
            'status' => 'done'
        ]);
    }

    /**
     * Test buying with insufficient Toman balance.
     */
    public function test_cannot_buy_with_insufficient_toman_balance(): void
    {
        // Create a user with low Toman balance
        $user = User::factory()->create([
            'toman_balance' => 1000 // 1,000 Toman
        ]);

        // Create a currency
        $currency = Currency::factory()->create([
            'name' => 'Bitcoin',
            'code' => 'BTC',
            'buy' => 100000, // 100,000 Toman per unit
            'sell' => 95000, // 95,000 Toman per unit
        ]);

        // Authenticate as the user
        $this->actingAs($user);

        // Make a buy request that exceeds balance
        $response = $this->postJson('/api/user/trading/buy', [
            'currency_id' => $currency->id,
            'amount' => 0.02 // Try to buy 0.02 BTC (costs 2,000 Toman)
        ]);

        // Assert the response indicates failure
        $response->assertStatus(200)
            ->assertJson([
                'success' => false,
                'message' => 'موجودی تومانی کافی نیست'
            ]);

        // Assert the user's Toman balance was not changed
        $this->assertEquals(1000, $user->fresh()->toman_balance);
    }

    /**
     * Test selling with insufficient crypto balance.
     */
    public function test_cannot_sell_with_insufficient_crypto_balance(): void
    {
        // Create a user
        $user = User::factory()->create([
            'toman_balance' => 500000 // 500,000 Toman
        ]);

        // Create a currency
        $currency = Currency::factory()->create([
            'name' => 'Bitcoin',
            'code' => 'BTC',
            'buy' => 100000, // 100,000 Toman per unit
            'sell' => 95000, // 95,000 Toman per unit
        ]);

        // Create a wallet with low balance
        $wallet = Wallet::factory()->create([
            'user_id' => $user->id,
            'coin_id' => $currency->id,
            'balance' => 0.01 // 0.01 BTC
        ]);

        // Authenticate as the user
        $this->actingAs($user);

        // Make a sell request that exceeds balance
        $response = $this->postJson('/api/user/trading/sell', [
            'currency_id' => $currency->id,
            'amount' => 0.02 // Try to sell 0.02 BTC
        ]);

        // Assert the response indicates failure
        $response->assertStatus(200)
            ->assertJson([
                'success' => false,
                'message' => 'موجودی ارز کافی نیست'
            ]);

        // Assert the user's wallet balance was not changed
        $this->assertEquals(0.01, $wallet->fresh()->balance);
    }
}
