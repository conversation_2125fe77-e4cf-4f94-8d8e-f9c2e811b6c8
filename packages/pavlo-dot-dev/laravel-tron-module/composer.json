{"name": "pavlo-dot-dev/laravel-tron-module", "description": "<PERSON><PERSON> T<PERSON> Module", "homepage": "https://github.com/pavlo-dot-dev/laravel-tron-module", "license": "MIT", "authors": [{"name": "PAVLO POPOV", "email": "<EMAIL>"}], "keywords": ["pavlodotdev", "laravel", "tron"], "require": {"php": "^8.1", "ext-gmp": "*", "ext-decimal": "*", "illuminate/contracts": "^10.0", "spatie/laravel-package-tools": "^1.15", "furqansiddiqui/bip39-mnemonic-php": "^0.1.7", "minter/minter-php-bip-44": "^1.2", "web3p/web3.php": "^0.1.6", "kornrunner/secp256k1": "^0.2.0", "simplito/elliptic-php": "^1.0", "ext-ctype": "*"}, "autoload": {"psr-4": {"PavloDotDev\\LaravelTronModule\\": "src"}}, "extra": {"laravel": {"providers": ["PavloDotDev\\LaravelTronModule\\TronServiceProvider"], "aliases": {"Tron": "PavloDotDev\\LaravelTronModule\\Facades\\Tron"}}}, "minimum-stability": "dev", "prefer-stable": true}