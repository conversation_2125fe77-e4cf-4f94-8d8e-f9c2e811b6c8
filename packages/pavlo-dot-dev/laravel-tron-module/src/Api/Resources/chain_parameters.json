{"chainParameter": [{"key": "getMaintenanceTimeInterval", "value": ********}, {"key": "getAccountUpgradeCost", "value": **********}, {"key": "getCreateAccountFee", "value": 100000}, {"key": "getTransactionFee", "value": 1000}, {"key": "getAssetIssueFee", "value": **********}, {"key": "getWitnessPayPerBlock", "value": ********}, {"key": "getWitnessStandbyAllowance", "value": ************}, {"key": "getCreateNewAccountFeeInSystemContract", "value": 1000000}, {"key": "getCreateNewAccountBandwidthRate", "value": 1}, {"key": "getAllowCreationOfContracts", "value": 1}, {"key": "getRemoveThePowerOfTheGr", "value": -1}, {"key": "getEnergyFee", "value": 420}, {"key": "getExchangeCreateFee", "value": **********}, {"key": "getMaxCpuTimeOfOneTx", "value": 80}, {"key": "getAllowUpdateAccountName"}, {"key": "getAllowSameTokenName", "value": 1}, {"key": "getAllowDelegateResource", "value": 1}, {"key": "getTotalEnergyLimit", "value": ***********}, {"key": "getAllowTvmTransferTrc10", "value": 1}, {"key": "getTotalEnergyCurrentLimit", "value": ***********}, {"key": "getAllowMultiSign", "value": 1}, {"key": "getAllowAdaptiveEnergy"}, {"key": "getTotalEnergyTargetLimit", "value": 6250000}, {"key": "getTotalEnergyAverageUsage"}, {"key": "getUpdateAccountPermissionFee", "value": *********}, {"key": "getMultiSignFee", "value": 1000000}, {"key": "getAllowAccountStateRoot"}, {"key": "getAllowProtoFilterNum"}, {"key": "getAllowTvmConstantinople", "value": 1}, {"key": "getAllowTvmSolidity059", "value": 1}, {"key": "getAllowTvmIstanbul", "value": 1}, {"key": "getAllowShieldedTRC20Transaction", "value": 1}, {"key": "getForbidTransferToContract"}, {"key": "getAdaptiveResourceLimitTargetRatio", "value": 10}, {"key": "getAdaptiveResourceLimitMultiplier", "value": 1000}, {"key": "getChangeDelegation", "value": 1}, {"key": "getWitness127PayPerBlock", "value": ********0}, {"key": "getAllowMarketTransaction"}, {"key": "getMarketSellFee"}, {"key": "getMarketCancelFee"}, {"key": "getAllowPBFT"}, {"key": "getAllowTransactionFeePool"}, {"key": "getMaxFeeLimit", "value": ***********}, {"key": "getAllowOptimizeBlackHole", "value": 1}, {"key": "getAllowNewResourceModel"}, {"key": "getAllowTvmFreeze"}, {"key": "getAllowTvmVote", "value": 1}, {"key": "getAllowTvmLondon", "value": 1}, {"key": "getAllowTvmCompatibleEvm"}, {"key": "getAllowAccountAssetOptimization"}, {"key": "getFreeNetLimit", "value": 600}, {"key": "getTotalNetLimit", "value": ***********}, {"key": "getAllowHigherLimitForMaxCpuTimeOfOneTx", "value": 1}, {"key": "getAllowAssetOptimization", "value": 1}, {"key": "getAllowNewReward", "value": 1}, {"key": "getMemoFee", "value": 1000000}, {"key": "getAllowDelegateOptimization", "value": 1}, {"key": "getUnfreezeDelayDays", "value": 14}, {"key": "getAllowOptimizedReturnValueOfChainId", "value": 1}, {"key": "getAllowDynamicEnergy", "value": 1}, {"key": "getDynamicEnergyThreshold", "value": **********}, {"key": "getDynamicEnergyIncreaseFactor", "value": 2000}, {"key": "getDynamicEnergyMaxFactor", "value": 12000}, {"key": "getAllowTvmShangHai"}, {"key": "getAllowCancelAllUnfreezeV2", "value": 1}, {"key": "getMaxDelegateLockPeriod", "value": 864000}]}