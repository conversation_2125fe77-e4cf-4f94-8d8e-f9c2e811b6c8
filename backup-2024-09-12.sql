-- MySQL dump 10.13  Distrib 8.4.2, for macos14 (arm64)
--
-- Host: 127.0.0.1    Database: exchange
-- ------------------------------------------------------
-- Server version	9.0.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `banks`
--

DROP TABLE IF EXISTS `banks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `banks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `prefix` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `banks_prefix_unique` (`prefix`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `banks`
--

LOCK TABLES `banks` WRITE;
/*!40000 ALTER TABLE `banks` DISABLE KEYS */;
INSERT INTO `banks` VALUES (1,'دی','/storage/bank-icons/mellat.png','502938'),(2,'ایران زمین','/storage/bank-icons/mellat.png','505785'),(3,'توسعه تعاون','/storage/bank-icons/mellat.png','502908'),(4,'ملی ایران','/storage/bank-icons/mellat.png','603799'),(5,'رفاه کارگران','/storage/bank-icons/mellat.png','589463'),(6,'سامان','/storage/bank-icons/mellat.png','621986'),(7,'سپه','/storage/bank-icons/mellat.png','589210'),(8,'تجارت','/storage/bank-icons/mellat.png','627353'),(9,'انصار (سپه)','/storage/bank-icons/mellat.png','627381'),(10,'شهر','/storage/bank-icons/mellat.png','502806'),(11,'اقتصاد نوین','/storage/bank-icons/mellat.png','627412'),(12,'صادرات ایران','/storage/bank-icons/mellat.png','603769'),(13,'توسعه صادرات ایران','/storage/bank-icons/mellat.png','627648'),(14,'قرض الحسنه مهر ایران','/storage/bank-icons/mellat.png','606373'),(15,'صنعت و معدن','/storage/bank-icons/mellat.png','627961'),(16,'کارآفرین','/storage/bank-icons/mellat.png','502910'),(17,'آینده','/storage/bank-icons/mellat.png','636214'),(18,'گردشگری','/storage/bank-icons/mellat.png','505416'),(19,'مرکزی','/storage/bank-icons/mellat.png','636795'),(20,'حکمت ایرانیان (سپه)','/storage/bank-icons/mellat.png','636949'),(21,'مسکن','/storage/bank-icons/mellat.png','628023'),(22,'پارسیان','/storage/bank-icons/mellat.png','639194'),(23,'کشاورزی','/storage/bank-icons/mellat.png','639217'),(24,'سینا','/storage/bank-icons/mellat.png','639346'),(25,'پاسارگاد','/storage/bank-icons/mellat.png','639347'),(26,'مهر اقتصاد (سپه)','/storage/bank-icons/mellat.png','639370'),(27,'پست ایران','/storage/bank-icons/mellat.png','627760'),(28,'قوامین (سپه)','/storage/bank-icons/mellat.png','639599'),(29,'موسسه اعتباری توسعه','/storage/bank-icons/mellat.png','628157'),(30,'سرمایه','/storage/bank-icons/mellat.png','639607'),(31,'ملت','/storage/bank-icons/mellat.png','610433');
/*!40000 ALTER TABLE `banks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `by_bit_sub_users`
--

DROP TABLE IF EXISTS `by_bit_sub_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `by_bit_sub_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `userId` bigint unsigned NOT NULL,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `memberType` int NOT NULL,
  `switch` int NOT NULL DEFAULT '0',
  `isUta` tinyint(1) NOT NULL DEFAULT '0',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` int NOT NULL COMMENT '1: normal 2: login banned 4: frozen',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `by_bit_sub_users_userid_foreign` (`userId`),
  CONSTRAINT `by_bit_sub_users_userid_foreign` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `by_bit_sub_users`
--

LOCK TABLES `by_bit_sub_users` WRITE;
/*!40000 ALTER TABLE `by_bit_sub_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `by_bit_sub_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `by_bit_wallet_addresses`
--

DROP TABLE IF EXISTS `by_bit_wallet_addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `by_bit_wallet_addresses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `test` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `by_bit_wallet_addresses`
--

LOCK TABLES `by_bit_wallet_addresses` WRITE;
/*!40000 ALTER TABLE `by_bit_wallet_addresses` DISABLE KEYS */;
/*!40000 ALTER TABLE `by_bit_wallet_addresses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cache`
--

DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cache`
--

LOCK TABLES `cache` WRITE;
/*!40000 ALTER TABLE `cache` DISABLE KEYS */;
/*!40000 ALTER TABLE `cache` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cache_locks`
--

DROP TABLE IF EXISTS `cache_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache_locks` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cache_locks`
--

LOCK TABLES `cache_locks` WRITE;
/*!40000 ALTER TABLE `cache_locks` DISABLE KEYS */;
/*!40000 ALTER TABLE `cache_locks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cards`
--

DROP TABLE IF EXISTS `cards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cards` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sheba` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `iban` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `bank_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cards_number_unique` (`number`),
  UNIQUE KEY `cards_sheba_unique` (`sheba`),
  UNIQUE KEY `cards_iban_unique` (`iban`),
  KEY `cards_user_id_foreign` (`user_id`),
  KEY `cards_bank_id_foreign` (`bank_id`),
  CONSTRAINT `cards_bank_id_foreign` FOREIGN KEY (`bank_id`) REFERENCES `banks` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `cards_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cards`
--

LOCK TABLES `cards` WRITE;
/*!40000 ALTER TABLE `cards` DISABLE KEYS */;
INSERT INTO `cards` VALUES (1,1,'****************','001184902384902384239048',NULL,'approved','2024-05-14 06:19:19','2024-05-14 06:19:19',7),(2,1,'****************','**************************','**************************','rejected','2024-05-14 06:19:19','2024-09-09 20:08:30',7),(3,1,'****************',NULL,NULL,'pending','2024-05-14 06:19:19','2024-05-14 06:19:19',7);
/*!40000 ALTER TABLE `cards` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `currencies`
--

DROP TABLE IF EXISTS `currencies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currencies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `network` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `apiKey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `crypto` tinyint(1) DEFAULT NULL,
  `needs_approval` tinyint(1) NOT NULL DEFAULT '0',
  `icon` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `buy` decimal(36,18) DEFAULT NULL,
  `sell` decimal(36,18) DEFAULT NULL,
  `active` enum('no','yes','buy','sell') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'yes',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `currencies_code_crypto_unique` (`code`,`crypto`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `currencies`
--

LOCK TABLES `currencies` WRITE;
/*!40000 ALTER TABLE `currencies` DISABLE KEYS */;
INSERT INTO `currencies` VALUES (1,'ووچر پرفکت مانی','pmv',NULL,'not',0,0,'/images/pmVoucher.svg','currencies.pmv',600000.000000000000000000,575000.000000000000000000,'yes','2024-09-04 11:23:09','2024-09-04 11:23:09'),(2,'پرفکت مانی','pm',NULL,'not',0,0,'/images/pmVoucher.svg',NULL,600000.000000000000000000,575000.000000000000000000,'yes','2024-09-04 11:23:09','2024-09-04 11:23:09'),(3,'ریال','irr',NULL,'not',0,1,'/images/irt.svg',NULL,1.000000000000000000,1.000000000000000000,'no','2024-09-04 11:23:09','2024-09-04 11:23:09'),(4,'تتر','usdt',NULL,'not',1,1,'/images/irt.svg','currencies.kucoin',595000.000000000000000000,575000.000000000000000000,'yes','2024-09-04 11:23:09','2024-09-04 11:23:09'),(5,'ترون','trx',NULL,'not',1,1,'/images/irt.svg','currencies.kucoin',81450.000000000000000000,81200.000000000000000000,'yes','2024-09-04 11:23:09','2024-09-04 11:23:09');
/*!40000 ALTER TABLE `currencies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `documents`
--

DROP TABLE IF EXISTS `documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `documents` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `type` enum('video','image','audio','document') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `documents_name_unique` (`name`),
  KEY `documents_user_id_index` (`user_id`),
  KEY `documents_name_index` (`name`),
  CONSTRAINT `documents_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `documents`
--

LOCK TABLES `documents` WRITE;
/*!40000 ALTER TABLE `documents` DISABLE KEYS */;
INSERT INTO `documents` VALUES (1,1,'image','consent','approved','2024-09-07 21:32:34','2024-09-07 22:43:10',NULL);
/*!40000 ALTER TABLE `documents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `failed_jobs`
--

DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `failed_jobs`
--

LOCK TABLES `failed_jobs` WRITE;
/*!40000 ALTER TABLE `failed_jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `failed_jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `files`
--

DROP TABLE IF EXISTS `files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `files` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `fileable_id` int NOT NULL,
  `fileable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `files`
--

LOCK TABLES `files` WRITE;
/*!40000 ALTER TABLE `files` DISABLE KEYS */;
INSERT INTO `files` VALUES (1,'files/Document-1-1725741154.webp',1,'App\\Models\\Document');
/*!40000 ALTER TABLE `files` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gift_cards`
--

DROP TABLE IF EXISTS `gift_cards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gift_cards` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ownerId` bigint unsigned NOT NULL,
  `price` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gift_cards`
--

LOCK TABLES `gift_cards` WRITE;
/*!40000 ALTER TABLE `gift_cards` DISABLE KEYS */;
/*!40000 ALTER TABLE `gift_cards` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `job_batches`
--

DROP TABLE IF EXISTS `job_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `job_batches` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `job_batches`
--

LOCK TABLES `job_batches` WRITE;
/*!40000 ALTER TABLE `job_batches` DISABLE KEYS */;
/*!40000 ALTER TABLE `job_batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `jobs`
--

DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint unsigned NOT NULL,
  `reserved_at` int unsigned DEFAULT NULL,
  `available_at` int unsigned NOT NULL,
  `created_at` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `jobs`
--

LOCK TABLES `jobs` WRITE;
/*!40000 ALTER TABLE `jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `logins`
--

DROP TABLE IF EXISTS `logins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `logins` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_agent_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `logins_user_id_foreign` (`user_id`),
  KEY `logins_user_agent_id_foreign` (`user_agent_id`),
  CONSTRAINT `logins_user_agent_id_foreign` FOREIGN KEY (`user_agent_id`) REFERENCES `user_agents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `logins_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `logins`
--

LOCK TABLES `logins` WRITE;
/*!40000 ALTER TABLE `logins` DISABLE KEYS */;
INSERT INTO `logins` VALUES (1,1,'127.0.0.1',1,NULL,NULL),(2,1,'127.0.0.1',1,NULL,NULL),(3,1,'127.0.0.1',1,NULL,NULL),(4,1,'127.0.0.1',2,NULL,NULL),(5,1,'127.0.0.1',2,NULL,NULL),(6,1,'127.0.0.1',2,'2024-09-10 14:49:15','2024-09-10 14:49:15');
/*!40000 ALTER TABLE `logins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'0001_01_01_000000_create_users_table',1),(2,'0001_01_01_000001_create_cache_table',1),(3,'0001_01_01_000002_create_jobs_table',1),(4,'2023_06_22_131554_create_files_table',1),(5,'2024_05_09_172528_create_personal_access_tokens_table',1),(6,'2024_05_11_222201_create_documents_table',1),(7,'2024_05_13_102102_create_cards_table',1),(8,'2024_05_13_161351_create_banks_table',1),(9,'2024_05_17_152412_create_currencies_table',1),(10,'2024_05_17_157523_create_wallets_table',1),(11,'2024_05_18_160914_create_transactions_table',1),(12,'2024_05_24_094757_create_permission_tables',1),(13,'2024_07_28_214751_create_gift_cards_table',1),(14,'2024_08_10_200946_create_sub_accounts_table',1),(15,'2024_08_12_112415_create_random_strings_table',1),(16,'2024_08_12_114213_create_sub_account_apis_table',1),(17,'2024_08_20_094555_create_by_bit_sub_users_table',1),(18,'2024_08_21_123849_create_by_bit_wallet_addresses_table',1),(19,'2024_08_22_104420_create_by_bit_user_wallets_table',1),(20,'2024_08_28_200516_create_support_units_table',1),(21,'2024_08_28_200602_create_support_levels_table',1),(22,'2024_08_28_200828_create_supports_table',1),(23,'2024_08_28_201708_create_support_tickets_table',1),(26,'2024_09_07_172455_create_user_agents_table',2),(27,'2024_09_07_173045_create_logins_table',2),(34,'2024_09_12_124638_create_tron_nodes_table',3),(35,'2024_09_12_124639_create_tron_wallets_table',3),(36,'2024_09_12_124640_create_tron_trc20_table',3),(37,'2024_09_12_124641_create_tron_addresses_table',4),(38,'2024_09_12_124642_create_tron_transactions_table',4),(39,'2024_09_12_124643_create_tron_deposits_table',4);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `model_has_permissions`
--

DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `model_has_permissions`
--

LOCK TABLES `model_has_permissions` WRITE;
/*!40000 ALTER TABLE `model_has_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `model_has_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `model_has_roles`
--

DROP TABLE IF EXISTS `model_has_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `model_has_roles` (
  `role_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `model_has_roles`
--

LOCK TABLES `model_has_roles` WRITE;
/*!40000 ALTER TABLE `model_has_roles` DISABLE KEYS */;
INSERT INTO `model_has_roles` VALUES (1,'App\\Models\\User',1);
/*!40000 ALTER TABLE `model_has_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `password_reset_tokens`
--

LOCK TABLES `password_reset_tokens` WRITE;
/*!40000 ALTER TABLE `password_reset_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `password_reset_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'users.index','api',NULL,NULL);
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `personal_access_tokens`
--

DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `personal_access_tokens`
--

LOCK TABLES `personal_access_tokens` WRITE;
/*!40000 ALTER TABLE `personal_access_tokens` DISABLE KEYS */;
INSERT INTO `personal_access_tokens` VALUES (1,'App\\Models\\User',1,'phone.','085afd97dfb97d37254f874e00375f39434f698aab2bd1401166be096988e5e1','[\"*\"]','2024-09-04 12:46:05',NULL,'2024-09-04 11:23:18','2024-09-04 12:46:05'),(2,'App\\Models\\User',1,'phone.','a878aba0a1978152af3ef53b5ae170af9e3d34009e3aac87462c1e491930ffeb','[\"*\"]',NULL,NULL,'2024-09-07 17:29:49','2024-09-07 17:29:49'),(3,'App\\Models\\User',1,'phone.','33850eec6c22989a9155d0df97a8d7fd033e2aec616d67be8c218fb613656c15','[\"*\"]','2024-09-07 18:04:30',NULL,'2024-09-07 17:45:02','2024-09-07 18:04:30'),(4,'App\\Models\\User',1,'phone.','2f29fda4f46476aa19144db422f4c3f0f20fe221dff4f79740246ee8e5417833','[\"*\"]',NULL,NULL,'2024-09-07 18:04:33','2024-09-07 18:04:33'),(5,'App\\Models\\User',1,'phone.','d61044c008aa3a64a78a4d126a8ee84fc20d445f6a6b5a2f83e3e07bdcce49e9','[\"*\"]','2024-09-07 18:05:26',NULL,'2024-09-07 18:04:34','2024-09-07 18:05:26'),(6,'App\\Models\\User',1,'phone.','12951992dc3d7694623f602d7cee6a23b01e2e2cd2f5b982e030b57e3c440522','[\"*\"]','2024-09-09 21:06:52',NULL,'2024-09-07 18:05:45','2024-09-09 21:06:52'),(7,'App\\Models\\User',1,'phone.','251ac71afaf34fcf8b1a0d6f51996044d814fd58799b54a9619ab55cce0600a7','[\"*\"]','2024-09-10 10:47:51',NULL,'2024-09-10 10:47:39','2024-09-10 10:47:51'),(8,'App\\Models\\User',1,'phone.','f4c34fb1dc224ae226fbf4ac0eefb81b814cf9278b330708f8cc46532168a912','[\"*\"]','2024-09-11 18:43:36',NULL,'2024-09-10 14:49:15','2024-09-11 18:43:36');
/*!40000 ALTER TABLE `personal_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `random_strings`
--

DROP TABLE IF EXISTS `random_strings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `random_strings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `string` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `random_strings_string_unique` (`string`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `random_strings`
--

LOCK TABLES `random_strings` WRITE;
/*!40000 ALTER TABLE `random_strings` DISABLE KEYS */;
INSERT INTO `random_strings` VALUES (1,'AbolfazlAndAbolfazl'),(2,'dKgHEoFdWQ'),(6,'qCj7toAooU'),(3,'saman1234'),(4,'saman12344'),(5,'Yc3oA14bs2');
/*!40000 ALTER TABLE `random_strings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_has_permissions`
--

DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_has_permissions`
--

LOCK TABLES `role_has_permissions` WRITE;
/*!40000 ALTER TABLE `role_has_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `role_has_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'admin','api',NULL,NULL);
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sub_account_apis`
--

DROP TABLE IF EXISTS `sub_account_apis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sub_account_apis` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `userId` bigint unsigned NOT NULL,
  `subName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `apiKey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `apiSecret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `apiVersion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `passphrase` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `permission` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `createdAt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sub_account_apis_userid_foreign` (`userId`),
  CONSTRAINT `sub_account_apis_userid_foreign` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sub_account_apis`
--

LOCK TABLES `sub_account_apis` WRITE;
/*!40000 ALTER TABLE `sub_account_apis` DISABLE KEYS */;
/*!40000 ALTER TABLE `sub_account_apis` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sub_accounts`
--

DROP TABLE IF EXISTS `sub_accounts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sub_accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uid` bigint NOT NULL,
  `userId` bigint unsigned NOT NULL,
  `subName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `access` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sub_accounts_subname_unique` (`subName`),
  UNIQUE KEY `sub_accounts_password_unique` (`password`),
  KEY `sub_accounts_userid_foreign` (`userId`),
  CONSTRAINT `sub_accounts_userid_foreign` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sub_accounts`
--

LOCK TABLES `sub_accounts` WRITE;
/*!40000 ALTER TABLE `sub_accounts` DISABLE KEYS */;
/*!40000 ALTER TABLE `sub_accounts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `support_levels`
--

DROP TABLE IF EXISTS `support_levels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `support_levels` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `support_levels`
--

LOCK TABLES `support_levels` WRITE;
/*!40000 ALTER TABLE `support_levels` DISABLE KEYS */;
INSERT INTO `support_levels` VALUES (1,'عادی'),(2,'متوسط'),(3,'فوری');
/*!40000 ALTER TABLE `support_levels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `support_tickets`
--

DROP TABLE IF EXISTS `support_tickets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `support_tickets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `support_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `support_tickets_support_id_foreign` (`support_id`),
  KEY `support_tickets_user_id_foreign` (`user_id`),
  CONSTRAINT `support_tickets_support_id_foreign` FOREIGN KEY (`support_id`) REFERENCES `supports` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `support_tickets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `support_tickets`
--

LOCK TABLES `support_tickets` WRITE;
/*!40000 ALTER TABLE `support_tickets` DISABLE KEYS */;
INSERT INTO `support_tickets` VALUES (1,4,1,'Ad non nesciunt voluptatem rerum sit. Error repudiandae et expedita error quae. Perspiciatis soluta itaque ea aut accusantium dolorem eos. Recusandae enim nulla sint veritatis temporibus repudiandae.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(2,6,3,'Sed molestiae numquam et qui eos qui. Veritatis omnis sed exercitationem magni velit temporibus.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(3,7,3,'Voluptatum explicabo repellendus ex ut amet magnam. Quam commodi quasi perspiciatis quia. Aut excepturi nisi placeat illum sunt inventore reprehenderit. Tenetur ea quam veniam eligendi animi.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(4,1,2,'Nihil qui quas ullam deserunt libero veritatis. Doloribus officiis aut eaque tempora autem. Nostrum debitis et accusantium quis voluptatem fugiat.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(5,8,2,'Provident commodi id soluta nam. Placeat id quam eum necessitatibus voluptas dolore. Excepturi blanditiis sint in.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(6,9,3,'Nisi ipsa harum est. Laborum aut et suscipit aliquam totam voluptate repudiandae. Officiis minus ut autem omnis atque. Voluptatibus labore enim doloremque rem numquam dolores.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(7,2,3,'Architecto enim eos rerum ad dolorem aut nemo. Voluptatem molestiae omnis et pariatur esse autem. Et consequatur dolore pariatur ab magni minima.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(8,4,1,'Occaecati quia quo voluptatem eos. Dolores eaque eum animi at dolorem consequatur dolores quasi. Quidem quis aut et voluptatem voluptatem veritatis quae.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(9,4,1,'Eaque tenetur reprehenderit enim et suscipit. Voluptatem quam qui et beatae doloremque. Rerum in rerum occaecati facere sint.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(10,4,1,'Perferendis aliquam accusamus at deleniti. Aut ipsam qui consequuntur aut sit quas earum. Maiores numquam natus dolorem. Neque possimus laudantium commodi voluptas eum.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(11,1,2,'Corporis nam eius sit dolorem et totam aut. Perspiciatis sit numquam sequi omnis enim praesentium quos. Ipsum et voluptatem aut ut nam.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(12,1,2,'Recusandae ut consequatur voluptas est. Adipisci autem reiciendis delectus eos est ad quod. Recusandae voluptates nemo soluta voluptate sunt.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(13,4,1,'Nihil eaque quo tempore ut et laudantium. Aliquam earum nostrum quia quam voluptas est. Sed repudiandae sapiente ut officiis laboriosam voluptate. Amet in et suscipit eius animi quo repellat.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(14,10,1,'Voluptas exercitationem illo et nihil impedit ut. Et omnis nobis provident at quia natus placeat. Ut similique amet neque exercitationem. Autem eos sed praesentium omnis harum.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(15,4,1,'Laboriosam aspernatur veritatis assumenda recusandae. Sapiente autem et deleniti voluptatem sit eveniet fugiat est. Suscipit voluptas maiores incidunt quae architecto.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(16,6,3,'Facilis tempore nihil vero odit sed. Dolorem eum vitae quam minus ut. In eaque pariatur quia voluptatem et rerum. Sit cumque quae possimus quas.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(17,7,3,'Enim et ipsum laudantium et. Cumque dolorem modi illo necessitatibus ut non alias. Omnis laudantium optio ullam et.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(18,3,1,'Facere non nemo voluptatem et necessitatibus vel velit. Iste dolores nihil est et.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(19,8,2,'Dolorum enim quisquam totam commodi. Alias et voluptas nostrum reiciendis omnis nihil illo sunt. Rerum perspiciatis molestiae iste dicta ea odio. Et aut cumque aliquam nam.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(20,7,3,'Error ut facere ut itaque. A provident porro velit est. Aspernatur saepe quisquam rerum vel dolorem culpa eos. Ut eum provident qui rerum dicta non laborum.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(21,3,1,'Eum quasi id pariatur minus velit recusandae. Quasi omnis temporibus at non vero. Quia aliquid iure vitae architecto illum nulla. Cupiditate nemo ut aut temporibus perspiciatis in.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(22,8,2,'Ex amet magni sapiente. Architecto doloribus at eos saepe. Unde explicabo mollitia eos et dolor rerum iure. Maxime accusantium eligendi illo.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(23,10,1,'Incidunt beatae numquam et nesciunt. Vitae voluptas impedit consequuntur ut tempore ut. Aut adipisci voluptatem aliquid. Aut vitae explicabo excepturi officiis aut provident quia.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(24,4,1,'Aut sapiente voluptas corrupti amet. Dignissimos quis accusamus voluptatem et quia asperiores nisi. Numquam dicta impedit necessitatibus qui.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(25,8,2,'Ea quia aut omnis quo quae iusto mollitia. Rerum ullam sit sunt autem velit illo sit. Quo non occaecati dolores et. Vero ut quia harum omnis et.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(26,9,3,'Voluptates tempore dolores occaecati doloribus aut sit. Voluptatem non ad nam id possimus. Aliquid cupiditate suscipit necessitatibus optio debitis ea.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(27,1,2,'Explicabo nulla minima nisi repellendus. Totam repellat harum necessitatibus sit voluptas dolorum commodi. Corporis ut numquam iusto assumenda at consequatur dolores.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(28,3,1,'Omnis sequi minima odit cumque eveniet nemo repellat eum. Libero ex ea numquam ut animi est. Ea ipsam quod ratione corporis magnam nobis fuga. Id ipsum quos voluptas eum quis.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(29,8,2,'Quae ut sed minus molestiae omnis quis vel ratione. Itaque suscipit minus est quod. Doloribus qui suscipit similique provident qui.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(30,1,2,'Excepturi sint qui esse aut. Illo voluptatem et nisi magnam. Libero facilis consequatur quam voluptate. Sit eos fuga et veniam ut ducimus id.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(31,5,3,'Reprehenderit impedit aut est sapiente inventore fuga in. Perspiciatis deserunt vitae dolorum molestias ullam. Ipsa nihil quo id qui sint sed. Nostrum dignissimos dolorem sed et earum quo.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(32,4,1,'Nesciunt facere repudiandae sit labore explicabo. Id placeat qui quia nihil totam pariatur. Impedit voluptas fugit voluptatem qui natus sed ut eos. Aut rerum aut commodi.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(33,5,3,'Similique voluptas placeat et neque quod. Cumque id odit est in vel officiis ut. Doloremque in consequatur dolor laborum. Autem ut sequi neque a placeat sint rerum et.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(34,9,3,'Qui eum vitae ipsa molestiae. Necessitatibus est est quis asperiores nihil dolore dolor magnam. Rerum ab praesentium atque voluptate et animi rerum.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(35,5,3,'Aut sunt minus eum suscipit nobis voluptates id. Nam dolorem inventore incidunt rerum. Maiores accusantium enim odio adipisci quia quasi.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(36,7,3,'Ut voluptatem qui optio accusamus ut perspiciatis eos. Aspernatur magnam quaerat enim amet et. Rerum aspernatur aperiam suscipit maxime explicabo.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(37,2,3,'Facere incidunt odio perferendis enim. In necessitatibus molestiae non non. Explicabo neque impedit unde error dolor. A temporibus sed et neque autem aspernatur.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(38,1,2,'Fugiat sed ut explicabo illo ipsum. Aut culpa dignissimos quia sed possimus. Unde consectetur accusamus atque. Veniam sed temporibus vero corrupti eveniet.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(39,7,3,'Dolorem aliquam ipsum sint minus qui. Omnis aperiam aut magnam minima id. Et sapiente officiis laudantium temporibus quae minus. Et autem cumque quo.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(40,6,3,'Ut voluptatibus saepe tenetur. Incidunt earum optio eius corporis et. Quam sapiente aut qui quo. Ex rerum consequuntur id modi accusamus impedit. Dignissimos maiores accusamus repudiandae dolor eum.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(41,7,3,'Eaque et illum quisquam nihil. Sit ipsum sit aut quae error eaque officiis dolor. Ducimus nemo temporibus ea quis aut autem. Vitae tempora nam sequi.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(42,2,3,'Aspernatur praesentium sequi asperiores perspiciatis quia sapiente magni. Quaerat non quia et quis. Asperiores aliquam quia qui architecto inventore rerum qui.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(43,9,3,'Ut et expedita ea. Aut dignissimos unde assumenda ad omnis ab officiis. Libero soluta hic est assumenda eos.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(44,7,3,'Deleniti et ex voluptas distinctio. Ipsa et molestias quia qui inventore excepturi ut. Possimus qui totam tempora esse sunt qui.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(45,8,2,'In sed eos doloremque veritatis. Beatae cumque rerum maiores cum itaque officiis. Qui est est vel illum. Autem quia veniam cumque corrupti numquam asperiores.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(46,7,3,'Ea autem qui cum dolor sit corrupti. Ducimus sapiente sit aut commodi nihil alias velit. Facilis rerum et consequatur alias et rerum est dolorem. Est ex id dolor vel.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(47,2,3,'Doloremque tenetur accusamus neque saepe. Et ducimus magnam ut sint dolorem nihil cupiditate. Quidem minima totam eaque quaerat pariatur aut nesciunt.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(48,7,3,'Aliquam tempora libero repellendus qui voluptas est doloremque facere. Minima quis vel voluptas et et iusto. Ut occaecati voluptatem soluta vel. Praesentium occaecati illum in.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(49,6,3,'Et est commodi voluptatum fuga ipsum quos quia. Recusandae voluptate quis rerum voluptatem temporibus. Soluta praesentium fugit quia et quia laboriosam.','2024-09-04 11:23:09','2024-09-04 11:23:09'),(50,5,3,'Porro voluptatum magnam eum rerum atque. Aut praesentium ut nihil aspernatur. Aut unde vitae veniam omnis neque sint. Illum ullam labore enim et.','2024-09-04 11:23:09','2024-09-04 11:23:09');
/*!40000 ALTER TABLE `support_tickets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `support_units`
--

DROP TABLE IF EXISTS `support_units`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `support_units` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `support_units`
--

LOCK TABLES `support_units` WRITE;
/*!40000 ALTER TABLE `support_units` DISABLE KEYS */;
INSERT INTO `support_units` VALUES (1,'احراز هویت','1'),(2,'مالی','1'),(3,'سایر','1');
/*!40000 ALTER TABLE `support_units` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `supports`
--

DROP TABLE IF EXISTS `supports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supports` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `unit_id` bigint unsigned NOT NULL,
  `level_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `status` enum('deactive','admin_response','user_response','processing') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supports_unit_id_foreign` (`unit_id`),
  KEY `supports_level_id_foreign` (`level_id`),
  KEY `supports_user_id_foreign` (`user_id`),
  CONSTRAINT `supports_level_id_foreign` FOREIGN KEY (`level_id`) REFERENCES `support_levels` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `supports_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `support_units` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `supports_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `supports`
--

LOCK TABLES `supports` WRITE;
/*!40000 ALTER TABLE `supports` DISABLE KEYS */;
INSERT INTO `supports` VALUES (1,'Ipsam aspernatur qui cum aut velit sunt reiciendis. Ipsam eum eos qui rerum consectetur eum. Tenetur alias veniam iure. Officia id aut perferendis natus ut voluptatem.',2,1,5,'deactive','2024-09-04 11:23:09','2024-09-04 11:23:09'),(2,'Magnam magni quam et reiciendis quam eum. Sed consequatur in impedit ipsam doloribus in enim. Id fugiat est quia. Et consectetur eaque rem ipsa.',3,1,8,'admin_response','2024-09-04 11:23:09','2024-09-04 11:23:09'),(3,'Fugit nihil et nihil est dolorum voluptas. Itaque dolores iure neque rem. Veritatis perspiciatis voluptate velit sint. Deleniti labore adipisci architecto quo praesentium et nostrum.',1,1,10,'processing','2024-09-04 11:23:09','2024-09-04 11:23:09'),(4,'Modi est minus praesentium sed. Consequatur quibusdam qui voluptatem quis rerum ratione nihil. Totam iste veniam mollitia sed quia quo. Nihil quia veniam iste ut voluptatem autem repellat.',1,2,6,'processing','2024-09-04 11:23:09','2024-09-04 11:23:09'),(5,'Occaecati asperiores ex error amet magnam iusto. Occaecati sint ut sed architecto doloremque. Architecto qui nulla nostrum et voluptatibus. Rem eius rerum molestias unde aut explicabo.',3,1,6,'user_response','2024-09-04 11:23:09','2024-09-04 11:23:09'),(6,'Rerum ut alias quis. Nemo libero voluptatem quas. Nobis voluptatem sit odit ut consequuntur molestiae sunt aut. Ipsam et eius tempore neque. Velit possimus sint error et quo earum temporibus.',3,2,1,'admin_response','2024-09-04 11:23:09','2024-09-04 11:23:09'),(7,'Voluptas vero sunt repellat ipsam inventore nihil. Recusandae et in quis. Aut excepturi in in recusandae ad qui earum.',3,2,11,'admin_response','2024-09-04 11:23:09','2024-09-04 11:23:09'),(8,'Aut repellat et provident quia. Odio corrupti minima similique laborum distinctio possimus et neque. Ad aut temporibus tempora neque facere laborum error.',2,2,7,'admin_response','2024-09-04 11:23:09','2024-09-04 11:23:09'),(9,'Eius sapiente quae fuga cumque ut fugit ex. Mollitia esse consequatur ea. Vitae iste quia sunt ea quia et.',3,2,5,'deactive','2024-09-04 11:23:09','2024-09-04 11:23:09'),(10,'Pariatur rem corporis dolorem fuga repellendus quis voluptatum. Pariatur autem asperiores omnis delectus autem. Reiciendis maiores omnis fuga commodi. Qui architecto est qui dolor qui dolores.',1,2,9,'processing','2024-09-04 11:23:09','2024-09-04 11:23:09');
/*!40000 ALTER TABLE `supports` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transactions`
--

DROP TABLE IF EXISTS `transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('deposit','withdraw','gift','transfer','buy','sell','increase','decrease') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'deposit',
  `amount` decimal(36,18) NOT NULL,
  `price` int DEFAULT NULL,
  `wallet_id` bigint unsigned DEFAULT NULL,
  `wallet_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `registrar` bigint unsigned DEFAULT NULL,
  `network` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('pending','waiting','approved','declined','done') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `details` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transactions_wallet_id_foreign` (`wallet_id`),
  KEY `transactions_currency_id_foreign` (`currency_id`),
  KEY `transactions_user_id_foreign` (`user_id`),
  KEY `transactions_registrar_foreign` (`registrar`),
  CONSTRAINT `transactions_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `transactions_registrar_foreign` FOREIGN KEY (`registrar`) REFERENCES `users` (`id`),
  CONSTRAINT `transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `transactions_wallet_id_foreign` FOREIGN KEY (`wallet_id`) REFERENCES `wallets` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transactions`
--

LOCK TABLES `transactions` WRITE;
/*!40000 ALTER TABLE `transactions` DISABLE KEYS */;
INSERT INTO `transactions` VALUES (1,'buy',575000.000000000000000000,NULL,1,NULL,1,1,1,NULL,'done','',NULL,'2024-05-18 22:07:47','2024-05-18 22:07:47',NULL),(2,'decrease',0.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'pending','',NULL,'2024-09-04 11:23:33','2024-09-04 11:23:33',NULL),(3,'increase',1000.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'pending','',NULL,'2024-09-04 11:24:04','2024-09-04 11:24:04',NULL),(4,'increase',1000.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:26:49','2024-09-04 12:26:49',NULL),(28,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:32','2024-09-04 12:38:32',NULL),(29,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:32','2024-09-04 12:38:32',NULL),(30,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:32','2024-09-04 12:38:32',NULL),(31,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:32','2024-09-04 12:38:32',NULL),(32,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:33','2024-09-04 12:38:33',NULL),(33,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:33','2024-09-04 12:38:33',NULL),(34,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:40','2024-09-04 12:38:40',NULL),(35,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:40','2024-09-04 12:38:40',NULL),(36,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:40','2024-09-04 12:38:40',NULL),(37,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:40','2024-09-04 12:38:40',NULL),(38,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:41','2024-09-04 12:38:41',NULL),(39,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:41','2024-09-04 12:38:41',NULL),(40,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:42','2024-09-04 12:38:42',NULL),(41,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:42','2024-09-04 12:38:42',NULL),(42,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:43','2024-09-04 12:38:43',NULL),(43,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:43','2024-09-04 12:38:43',NULL),(44,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:43','2024-09-04 12:38:43',NULL),(45,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:43','2024-09-04 12:38:43',NULL),(46,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:44','2024-09-04 12:38:44',NULL),(47,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:44','2024-09-04 12:38:44',NULL),(48,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:38:45','2024-09-04 12:38:45',NULL),(49,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:38:45','2024-09-04 12:38:45',NULL),(50,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:39:50','2024-09-04 12:39:50',NULL),(51,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:39:50','2024-09-04 12:39:50',NULL),(52,'buy',5.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:39:51','2024-09-04 12:39:51',NULL),(53,'decrease',5.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:39:51','2024-09-04 12:39:51',NULL),(54,'buy',5000.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:40:08','2024-09-04 12:40:08',NULL),(55,'decrease',5000.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:40:08','2024-09-04 12:40:08',NULL),(56,'buy',50000000.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:40:11','2024-09-04 12:40:11',NULL),(57,'decrease',50000000.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:40:11','2024-09-04 12:40:11',NULL),(58,'buy',50000000.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:40:39','2024-09-04 12:40:39',NULL),(59,'decrease',50000000.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:40:39','2024-09-04 12:40:39',NULL),(60,'buy',50000000.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:41:28','2024-09-04 12:41:28',NULL),(61,'decrease',50000000.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:41:28','2024-09-04 12:41:28',NULL),(62,'buy',50000000.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:43:13','2024-09-04 12:43:13',NULL),(63,'decrease',50000000.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:43:13','2024-09-04 12:43:13',NULL),(64,'buy',50000000.000000000000000000,NULL,NULL,NULL,5,1,1,NULL,'pending','',NULL,'2024-09-04 12:43:44','2024-09-04 12:43:44',NULL),(65,'decrease',50000000.000000000000000000,NULL,NULL,NULL,3,1,1,NULL,'done','',NULL,'2024-09-04 12:43:44','2024-09-04 12:43:44',NULL);
/*!40000 ALTER TABLE `transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tron_addresses`
--

DROP TABLE IF EXISTS `tron_addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tron_addresses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `wallet_id` bigint unsigned NOT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `watch_only` tinyint(1) DEFAULT NULL,
  `private_key` text COLLATE utf8mb4_unicode_ci,
  `index` int unsigned DEFAULT NULL,
  `sync_at` timestamp NULL DEFAULT NULL,
  `activated` tinyint(1) DEFAULT NULL,
  `balance` decimal(20,6) DEFAULT NULL,
  `trc20` json DEFAULT NULL,
  `account` json DEFAULT NULL,
  `account_resources` json DEFAULT NULL,
  `touch_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tron_addresses_wallet_id_index_unique` (`wallet_id`,`index`),
  CONSTRAINT `tron_addresses_wallet_id_foreign` FOREIGN KEY (`wallet_id`) REFERENCES `tron_wallets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tron_addresses`
--

LOCK TABLES `tron_addresses` WRITE;
/*!40000 ALTER TABLE `tron_addresses` DISABLE KEYS */;
INSERT INTO `tron_addresses` VALUES (1,1,'TEq8qb1o5CPN2ttVtvViQQSYnJon7q8L9z','Primary Address',NULL,'fc128d21fefbe41b8ce340288be61a3f589914f93ce89eaddc8f44f32afb20f5',0,'2024-09-12 17:01:41',1,5.000000,'[]','{\"address\": \"TEq8qb1o5CPN2ttVtvViQQSYnJon7q8L9z\", \"balance\": \"5\", \"activated\": true, \"createTime\": \"2024-09-12 15:40:00\", \"lastOperationTime\": null}','{\"energy\": {\"used\": 0, \"total\": 0, \"available\": 0}, \"address\": \"TEq8qb1o5CPN2ttVtvViQQSYnJon7q8L9z\", \"activated\": true, \"bandwidth\": {\"used\": 0, \"total\": 600, \"available\": 600}}','2024-09-12 17:00:24','2024-09-12 16:19:44','2024-09-12 17:01:41');
/*!40000 ALTER TABLE `tron_addresses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tron_deposits`
--

DROP TABLE IF EXISTS `tron_deposits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tron_deposits` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `wallet_id` bigint unsigned NOT NULL,
  `address_id` bigint unsigned NOT NULL,
  `trc20_id` bigint unsigned DEFAULT NULL,
  `txid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(20,6) unsigned NOT NULL,
  `block_height` bigint unsigned DEFAULT NULL,
  `confirmations` int unsigned NOT NULL DEFAULT '0',
  `time_at` timestamp NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_index` (`address_id`,`txid`),
  KEY `tron_deposits_wallet_id_foreign` (`wallet_id`),
  KEY `tron_deposits_trc20_id_foreign` (`trc20_id`),
  CONSTRAINT `tron_deposits_address_id_foreign` FOREIGN KEY (`address_id`) REFERENCES `tron_addresses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tron_deposits_trc20_id_foreign` FOREIGN KEY (`trc20_id`) REFERENCES `tron_trc20` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tron_deposits_wallet_id_foreign` FOREIGN KEY (`wallet_id`) REFERENCES `tron_wallets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tron_deposits`
--

LOCK TABLES `tron_deposits` WRITE;
/*!40000 ALTER TABLE `tron_deposits` DISABLE KEYS */;
INSERT INTO `tron_deposits` VALUES (1,1,1,NULL,'94a9976164c46b9274b28ee96cf68fbb7e12b134c50f2512e265fe553baedf27',5.000000,65169205,432,'2024-09-12 15:40:03');
/*!40000 ALTER TABLE `tron_deposits` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tron_nodes`
--

DROP TABLE IF EXISTS `tron_nodes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tron_nodes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `full_node` json NOT NULL,
  `solidity_node` json NOT NULL,
  `block_number` bigint unsigned NOT NULL,
  `requests` int unsigned NOT NULL DEFAULT '0',
  `requests_at` date DEFAULT NULL,
  `sync_at` timestamp NULL DEFAULT NULL,
  `worked` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tron_nodes_name_unique` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tron_nodes`
--

LOCK TABLES `tron_nodes` WRITE;
/*!40000 ALTER TABLE `tron_nodes` DISABLE KEYS */;
INSERT INTO `tron_nodes` VALUES (1,'first_node','First Node','{\"url\": \"https://api.trongrid.io\", \"headers\": {\"TRON-PRO-API-KEY\": \"5dc2d9a6-6c2e-4542-babb-c5e3245c1411\"}}','{\"url\": \"https://api.trongrid.io\", \"headers\": {\"TRON-PRO-API-KEY\": \"5dc2d9a6-6c2e-4542-babb-c5e3245c1411\"}}',65169637,10,'2024-09-12','2024-09-12 17:01:40',1);
/*!40000 ALTER TABLE `tron_nodes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tron_transactions`
--

DROP TABLE IF EXISTS `tron_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tron_transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `txid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('in','out') COLLATE utf8mb4_unicode_ci NOT NULL,
  `time_at` timestamp NOT NULL,
  `from` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `to` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(20,6) NOT NULL,
  `trc20_contract_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `block_number` bigint unsigned DEFAULT NULL,
  `debug_data` json NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tron_transactions_txid_address_unique` (`txid`,`address`),
  KEY `tron_transactions_txid_index` (`txid`),
  KEY `tron_transactions_address_index` (`address`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tron_transactions`
--

LOCK TABLES `tron_transactions` WRITE;
/*!40000 ALTER TABLE `tron_transactions` DISABLE KEYS */;
INSERT INTO `tron_transactions` VALUES (1,'94a9976164c46b9274b28ee96cf68fbb7e12b134c50f2512e265fe553baedf27','TEq8qb1o5CPN2ttVtvViQQSYnJon7q8L9z','in','2024-09-12 15:40:03','TMJ9VvSGWD7JoveZYwsZ7TvoC4uZLuz7YK','TEq8qb1o5CPN2ttVtvViQQSYnJon7q8L9z',5.000000,NULL,65169205,'{\"to\": \"TEq8qb1o5CPN2ttVtvViQQSYnJon7q8L9z\", \"from\": \"TMJ9VvSGWD7JoveZYwsZ7TvoC4uZLuz7YK\", \"time\": \"2024-09-12 15:40:03\", \"txid\": \"94a9976164c46b9274b28ee96cf68fbb7e12b134c50f2512e265fe553baedf27\", \"value\": \"5\", \"success\": true, \"blockNumber\": 65169205}');
/*!40000 ALTER TABLE `tron_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tron_trc20`
--

DROP TABLE IF EXISTS `tron_trc20`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tron_trc20` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `symbol` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `decimals` tinyint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tron_trc20_address_unique` (`address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tron_trc20`
--

LOCK TABLES `tron_trc20` WRITE;
/*!40000 ALTER TABLE `tron_trc20` DISABLE KEYS */;
/*!40000 ALTER TABLE `tron_trc20` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tron_wallets`
--

DROP TABLE IF EXISTS `tron_wallets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tron_wallets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `node_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mnemonic` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `seed` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `sync_at` timestamp NULL DEFAULT NULL,
  `balance` decimal(20,6) DEFAULT NULL,
  `trc20` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tron_wallets_name_unique` (`name`),
  KEY `tron_wallets_node_id_foreign` (`node_id`),
  CONSTRAINT `tron_wallets_node_id_foreign` FOREIGN KEY (`node_id`) REFERENCES `tron_nodes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tron_wallets`
--

LOCK TABLES `tron_wallets` WRITE;
/*!40000 ALTER TABLE `tron_wallets` DISABLE KEYS */;
INSERT INTO `tron_wallets` VALUES (1,NULL,'second_wallet',NULL,'eyJpdiI6ImhLY0dKUkJWbGlsbmp4cEJJbmF0cnc9PSIsInZhbHVlIjoiNUtjcEVHOUl4OFptTmtzZWNqb3JSS3JmbEcxVUR6dW44ODJvZVNsTDdwSVY0OGd5TVhTS09FZ0RtbGxaYWlzSTFOTXUwclk5T2xMMHBlRG1RUDFtS2RWYlZ0WU9zL2dxSzBhUzdGMTBBTEI2UExsNjVoNU5BMkxTeFZUZjVMd1QzREY2ZW9NaW95T2ROSjNtUTRQZmxBPT0iLCJtYWMiOiJmY2UxYmFhZDI3ZjE0MTBmOWJmNjAxMTY3OThmZmY5NmEyOWQyMjg1OWMyMjFjMGVlNzczMjBmN2MxODRkOGU5IiwidGFnIjoiIn0=','eyJpdiI6IkxWNTA1NWgvTFdwVkJTQ2ZPdFJHZVE9PSIsInZhbHVlIjoiZlJ5dkt3K0xFbFNQTUdkcXhpTFEzalBxbFZhZ215ZmhwcVN6MkF2TnZBR1EzQTl0cUozUWlYSFYwRHJRZ0RpRys2REdabjRJUExWSkRFeXBFRXgrSWh4OVVPMWViWE5YalBHRHBmTmw3WlE1eWRYZkdHNlhSeGQzVnBpWi9oSzE2NVA1L1ByMGYrU0x6Q3R6UlVUMmJMaFoyTkNSdjVxUWVsUGJYMFdwbktQTG41bDEvbUtWckI2azNQb3puSmV1IiwibWFjIjoiYjZkNGVlMmVkZmJlYTI3NDUzNGI4MzU3ODc0OGU5NmU0NTQwZGU2NmM0NDlmMGY3ZjU0ZWEzYWJmYjNjYjA4NCIsInRhZyI6IiJ9','2024-09-12 17:01:42',5.000000,'[]','2024-09-12 16:19:44','2024-09-12 17:01:42');
/*!40000 ALTER TABLE `tron_wallets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_agents`
--

DROP TABLE IF EXISTS `user_agents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_agents` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_agents`
--

LOCK TABLES `user_agents` WRITE;
/*!40000 ALTER TABLE `user_agents` DISABLE KEYS */;
INSERT INTO `user_agents` VALUES (1,'PostmanRuntime/7.41.2'),(2,'Windows(10.0) - Firefox(103.0)');
/*!40000 ALTER TABLE `user_agents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `phone` bigint DEFAULT NULL,
  `phone_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `firstname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `lastname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fatherName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `serialCard` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `national_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` enum('male','female','undefined') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'undefined',
  `birth_date` date DEFAULT NULL,
  `options` json DEFAULT NULL,
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `level` int NOT NULL DEFAULT '1',
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  UNIQUE KEY `users_phone_unique` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'<EMAIL>','2024-07-07 10:10:48',9123456789,'2024-07-07 10:10:48','$2y$12$gaEdoU1LXyXgjCisv6nF7exofDwCApixLy5di0Djj02t5/22bY0fi','ادمین','اصلی',NULL,NULL,'0123456789','male','2000-01-01','\"{}\"','approved',2,NULL,'2024-05-11 21:49:48','2024-07-07 10:10:48'),(2,'<EMAIL>',NULL,9757594719,NULL,'$2y$12$BoySaeO9RgcGcJ6BYwTa5efBLkrl/gcbRYh9NykWAGVIAd83MzjRq','مهوار','قانعی',NULL,NULL,'1024026974','female','1999-02-28',NULL,'rejected',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09'),(3,'<EMAIL>',NULL,9243068350,NULL,'$2y$12$S3KTwb77jclAk64zvwfQ1u0/fL9nTamUIqAMEWWoQdv4YXsWPRyKi','به بها','اصفهانی',NULL,NULL,'1024051475','male','2003-05-17',NULL,'rejected',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09'),(4,'<EMAIL>',NULL,9442017358,NULL,'$2y$12$pwrcJzufYG99VkNgyFwN5uR.fmf1kaS.O2eyJPCjQjnYWzoHwFUW6','اورسیا','مصباح‌زاده',NULL,NULL,'1013471737','male','2003-11-23',NULL,'approved',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09'),(5,'<EMAIL>',NULL,9576527361,NULL,'$2y$12$pl7weh7mf9YDHM0zHupF/ub9Yw/RNmSg.T9e7coV7yS.0uEDnadaS','سوفرا','هراتی',NULL,NULL,'1009938510','male','1982-01-20',NULL,'approved',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09'),(6,'<EMAIL>',NULL,9716683862,NULL,'$2y$12$xKQ8Fwi.HoRIH/v1DGFFnOqk69VoHsbDWTasuSn66nYh1MYE7XF3e','هومین','دری',NULL,NULL,'1060168835','male','1972-01-18',NULL,'pending',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09'),(7,'<EMAIL>',NULL,9684008541,NULL,'$2y$12$XM56dLxGW1G8bGm0xXXmKuvgcxp30gf.AJ98Yus0m2AUS74R4uBwC','دلیر','باهنر',NULL,NULL,'1041371804','male','2004-08-19',NULL,'rejected',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09'),(8,'<EMAIL>',NULL,9208448547,NULL,'$2y$12$McWHwHnE9u2Lymp7lPaMbu91x3V8GLEwaCs2jw7FtQTZNN.CKluTW','تیرگر','مظفر',NULL,NULL,'1024158561','female','2004-09-11',NULL,'approved',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09'),(9,'<EMAIL>',NULL,9486266954,NULL,'$2y$12$54XQbsA4vdEk9kR5dhYgHe7A5WvCxJ0jqdz/GicBXPPv2DBDUVc0G','رسا','طباطبائی',NULL,NULL,'1081884193','male','2011-08-30',NULL,'pending',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09'),(10,'<EMAIL>',NULL,9488249698,NULL,'$2y$12$w4kgaDbaFTR7quouYebsYuC7/ItQmM0yewUqCHdWb2G7r7gi3zP6K','نورانگیز','کوشکی',NULL,NULL,'1012918929','male','1984-03-03',NULL,'pending',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09'),(11,'<EMAIL>',NULL,9597924938,NULL,'$2y$12$1v4Gx.OqMUq0Ssr8WU2ir.0BgGitFM0J6ytfBpYisSjVWh1OVg9dO','سریر','شاه‌حسینی',NULL,NULL,'1062844064','male','1974-04-06',NULL,'approved',1,NULL,'2024-09-04 11:23:09','2024-09-04 11:23:09');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wallets`
--

DROP TABLE IF EXISTS `wallets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wallets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `balance` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000',
  `currency_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `primary` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `wallets_user_id_currency_id_unique` (`user_id`,`currency_id`),
  KEY `wallets_currency_id_foreign` (`currency_id`),
  CONSTRAINT `wallets_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `wallets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wallets`
--

LOCK TABLES `wallets` WRITE;
/*!40000 ALTER TABLE `wallets` DISABLE KEYS */;
INSERT INTO `wallets` VALUES (1,380000.000000000000000000,3,1,1,NULL,'2024-05-21 17:50:00');
/*!40000 ALTER TABLE `wallets` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-09-12 19:34:52
