<?php

namespace Database\Seeders;

use App\Models\Level;
use App\Models\LevelItem;
use App\Models\LevelType;
use App\Models\LevelTypeValue;
use Illuminate\Database\Seeder;

class LevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $levels = collect([
            [
                'title' => 'سطح کاربری کاربرجدید',
            ],
            [
                'title' => 'سطح کاربری پایه',
            ],
            [
                'title' => 'سطح کاربری پیشرفته',
            ],
            [
                'title' => 'سطح کاربری کاربر سطح سه(طلایی)',
            ],
            [
                'title' => 'سطح کاربری کاربر سطح چهار(C.I.P)',
            ],
            [
                'title' => 'سطح کاربری کاربر سطح پنج(V.I.P)',
            ],
            [
                'title' => 'سطح کاربری کاربر سطح شش(شرکتی)',
            ],
            [
                'title' => 'سطح کاربری کاربر سطح تریدر',
            ],
            [
                'title' => 'سطح کاربری کاربر سطح مستر',
            ],
        ]);
        foreach ($levels as $level) {
            Level::create([
                'title' => $level['title'],
                'priority' => 0,
            ]);
        }

        $levelsItems = collect([
            [
                'title' => 'رمز ارز به تومان',
            ],
            [
                'title' => 'تومان',
            ],
            [
                'title' => 'رمز ارز و تومان',
            ],
        ]);

        foreach ($levelsItems as $levelsItem) {
            LevelItem::create([
                'title' => $levelsItem['title'],
            ]);
        }

        $levelTypes = collect([
            [
                'title' => 'واریز روزانه',
            ],
            [
                'title' => 'برداشت روزانه',
            ],
            [
                'title' => 'برداشت ماهیانه',
            ],
        ]);
        foreach ($levelTypes as $levelType) {
            LevelType::create([
                'title' => $levelType['title'],
            ]);
        }
        foreach (Level::get() as $level) {
            foreach (LevelItem::get() as $levelItem) {
                foreach (LevelType::get() as $levelType) {
                    LevelTypeValue::create([
                        'level_id' => $level->id,
                        'level_item_id' => $levelItem->id,
                        'level_type_id' => $levelType->id,
                        'value' => null,
                    ]);
                }
            }
        }

    }
}
