<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SubAccountApiSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [
                'userId' => 8,
                'subName' => 'Yc3oA14bs2',
                'remark' => '***************',
                'apiKey' => '66b9e7e419f86c00012f6d4b',
                'apiSecret' => '92a72d66-76b5-4962-8062-69c2dfda13d7',
                'apiVersion' => '3',
                'passphrase' => '***********',
                'permission' => 'General',
                'createdAt' => '*************',
                'created_at' => '2024-08-12 11:45:57',
                'updated_at' => '2024-08-12 11:45:57',
            ],
        ]);

        foreach ($collection as $row) {
            // DB::table('sub_account_apis')->insert([
            //     'userId' => $row['userId'],
            //     'subName' => $row['subName'],
            //     'remark' => $row['remark'],
            //     'apiKey' => $row['apiKey'],
            //     'apiSecret' => $row['apiSecret'],
            //     'apiVersion' => $row['apiVersion'],
            //     'passphrase' => $row['passphrase'],
            //     'permission' => $row['permission'],
            //     'createdAt' => $row['createdAt'],
            // ]);
        }
    }
}
