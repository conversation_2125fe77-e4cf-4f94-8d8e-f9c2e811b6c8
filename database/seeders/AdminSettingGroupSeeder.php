<?php

// namespace Database\Seeders;

// use App\Models\AdminSettingGroup;
// use Illuminate\Database\Seeder;

// class AdminSettingGroupSeeder extends Seeder
// {
//     /**
//      * Run the database seeds.
//      */
//     public function run(): void
//     {
//         $collect = collect([
//             [
//                 'id' => 1,
//                 'title' => 'اطلاعات سایت',
//                 'icon' => 'feather icon-settings',
//                 'priority' => 1,
//             ],
//             [
//                 'id' => 2,
//                 'title' => 'حسابداری',
//                 'icon' => 'feather icon-percent',
//                 'priority' => 2,
//             ],
//             [
//                 'id' => 3,
//                 'title' => 'عمومی',
//                 'icon' => 'feather icon-grid',
//                 'priority' => 3,
//             ],
//             [
//                 'id' => 4,
//                 'title' => 'وبلاگ',
//                 'icon' => 'feather icon-activity',
//                 'priority' => 4,
//             ],
//             [
//                 'id' => 5,
//                 'title' => 'سربرگ',
//                 'icon' => 'feather icon-settings',
//                 'priority' => 5,
//             ],
//             [
//                 'id' => 6,
//                 'title' => 'پابرگ',
//                 'icon' => 'feather icon-settings',
//                 'priority' => 6,
//             ],
//             [
//                 'id' => 7,
//                 'title' => 'اطلاعات تماس',
//                 'icon' => 'feather icon-phone',
//                 'priority' => 7,
//             ],
//             [
//                 'id' => 8,
//                 'title' => 'تنظیمات منو',
//                 'icon' => 'feather icon-menu',
//                 'priority' => 8,
//             ],
//             [
//                 'id' => 9,
//                 'title' => 'سایر تنظیمات',
//                 'icon' => 'feather icon-grid',
//                 'priority' => 9,
//             ],
//             [
//                 'id' => 10,
//                 'title' => 'کسب درآمد',
//                 'icon' => 'feather icon-dollar-sign',
//                 'priority' => 10,
//             ],
//             [
//                 'id' => 11,
//                 'title' => 'وب‌سرویس بانکی',
//                 'icon' => 'feather icon-database',
//                 'priority' => 11,
//             ],
//             [
//                 'id' => 12,
//                 'title' => 'وب‌سرویس پیامکی',
//                 'icon' => 'feather icon-mail',
//                 'priority' => 12,
//             ],
//             [
//                 'id' => 13,
//                 'title' => 'وب‌سرویس رمز ارزی',
//                 'icon' => 'feather icon-dollar-sign',
//                 'priority' => 13,
//             ],
//             [
//                 'id' => 14,
//                 'title' => 'تنظیمات کیف پول',
//                 'icon' => 'feather icon-dollar-sign',
//                 'priority' => 14,
//             ],
//             [
//                 'id' => 15,
//                 'title' => 'آدرس ولت رمزارزها',
//                 'icon' => 'feather icon-grid',
//                 'priority' => 15,
//             ],
//             [
//                 'id' => 16,
//                 'title' => 'محدودیت ها',
//                 'icon' => 'feather icon-settings',
//                 'priority' => 16,
//             ],
//             [
//                 'id' => 17,
//                 'title' => 'صفحه اصلی موبایل',
//                 'icon' => 'feather icon-phone',
//                 'priority' => 17,
//             ],
//         ]);

//         foreach ($collect as $row) {
//             AdminSettingGroup::create($row);
//         }
//     }
// }
