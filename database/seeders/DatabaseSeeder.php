<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // $this->call(UserSeeder::class);
        // $this->call(BankSeeder::class);
        // $this->call(CurrencySeeder::class);
        // $this->call(CardSeeder::class);
        // $this->call(DocumentSeeder::class);
        // $this->call(RolesSeeder::class);
        // $this->call(PremissionSeeder::class);
        // $this->call(WalletSeeder::class);
        // $this->call(TransactionSeeder::class);
        // $this->call(SubAccountApiSeeder::class);
        // $this->call(SubAccountSeeder::class);
        // $this->call(StringSeeder::class);
        $this->call(SupportSeeder::class);
        $this->call(SupportedNetworkSeeder::class);
        
     //   $this->call(AdminSettingGroupSeeder::class);
       // $this->call(AdminSettingSeeder::class);
    }
}
