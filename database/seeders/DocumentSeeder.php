<?php

namespace Database\Seeders;

use App\Models\Document;
use Illuminate\Database\Seeder;

class DocumentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // $collection = collect([
        //     [1,1,'image','consent','2024-06-19 18:54:18','2024-07-05 13:35:20','documents/1/avatar-2.jpg','pending']
        // ]);

        // foreach($collection as $row){
        //     Document::create([
        //         'user_id' => $row[1],
        //         'type' => $row[2],
        //         'name' => $row[3],
        //         'created_at' => $row[4],
        //         'updated_at' => $row[5],
        //         'path' => $row[6],
        //         'status' => $row[7],
        //     ]);
        // }
    }
}
