<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            ['ووچر پرفکت مانی', 'pmv', 0, 0, '/images/pmVoucher.svg', 'currencies.pmv', 600000.000000000000000000, 575000.000000000000000000, 'yes'],
            ['پرفکت مانی', 'pm', 0, 0, '/images/pmVoucher.svg', null, 600000.000000000000000000, 575000.000000000000000000, 'yes'],
            ['ریال', 'irr', 0, 1, '/images/irt.svg', null, 1.000000000000000000, 1.000000000000000000, 'no'],
            ['تتر', 'usdt', 1, 1, '/images/irt.svg', 'currencies.kucoin', 595000.000000000000000000, 575000.000000000000000000, 'yes'],
            ['ترون', 'trx', 1, 1, '/images/irt.svg', 'currencies.kucoin', 81450.000000000000000000, 81200.000000000000000000, 'yes'],
        ]);

        foreach ($collection as $row) {
            Currency::create([
                'name' => $row[0],
                'code' => $row[1],
                'crypto' => $row[2],
                'apiKey' => 'not',
                'needs_approval' => $row[3],
                'icon' => $row[4],
                'provider' => $row[5],
                'buy' => $row[6],
                'sell' => $row[7],
                'active' => $row[8],
            ]);
        }
    }
}
