<?php

namespace Database\Seeders;

use App\Models\Card;
use Illuminate\Database\Seeder;

class CardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [1, '6219861908439214', '001184902384902384239048', null, 'approved', '2024-05-14 06:19:19', '2024-05-14 06:19:19', 7],
            [1, '6037997445418040', null, null, 'rejected', '2024-05-14 06:19:19', '2024-05-14 06:19:19', 7],
            [1, '****************', null, null, 'pending', '2024-05-14 06:19:19', '2024-05-14 06:19:19', 7],
        ]);

        foreach ($collection as $row) {
            Card::create([
                'user_id' => $row[0],
                'number' => $row[1],
                'sheba' => $row[2],
                'iban' => $row[3],
                'status' => $row[4],
                'created_at' => $row[5],
                'updated_at' => $row[6],
                'bank_id' => $row[7],
            ]);
        }
    }
}
