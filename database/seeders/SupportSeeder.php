<?php

namespace Database\Seeders;

use App\Models\Support;
use App\Models\SupportTicket;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SupportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $units = collect([
            [
                'title' => 'احراز هویت',
                'status' => 1,
            ],
            [
                'title' => 'مالی',
                'status' => 1,
            ],
            [
                'title' => 'سایر',
                'status' => 1,
            ],
        ]);
        foreach ($units as $unit) {
            DB::table('support_units')->insert([
                'title' => $unit['title'],
                'status' => $unit['status'],
            ]);
        }

        $levels = collect([
            [
                'title' => 'عادی',
            ],
            [
                'title' => 'متوسط',
            ],
            [
                'title' => 'فوری',
            ],
        ]);
        foreach ($levels as $level) {
            DB::table('support_levels')->insert([
                'title' => $level['title'],
            ]);
        }

      //  Support::factory()->count(10)->create();
       // SupportTicket::factory()->count(50)->create();

    }
}
