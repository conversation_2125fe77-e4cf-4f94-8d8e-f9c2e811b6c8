<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StringSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [
                'string' => 'AbolfazlAndAbolfazl',
            ],
            [
                'string' => 'dKgHEoFdWQ',
            ],
            [
                'string' => 'saman1234',
            ],
            [
                'string' => 'saman12344',
            ],
            [
                'string' => 'Yc3oA14bs2',
            ],
            [
                'string' => 'qCj7toAooU',
            ],
        ]);

        foreach ($collection as $row) {
            DB::table('random_strings')->insert([
                'string' => $row['string'],
            ]);
        }
    }
}
