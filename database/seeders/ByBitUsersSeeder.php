<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ByBitUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [
                'username' => 'samanonfirst',
                'password' => 'Samanpass1234',
                'uid' => '261755213',
            ],
            [
                'username' => 'samanonsecond',
                'password' => 'Samanpass1234',
                'uid' => '261761301',
            ],
        ]);
        foreach ($collection as $row) {
            DB::table('by_bit_sub_users')->insert([
                'uid' => $row['uid'],
                'username' => $row['username'],
                'password' => $row['password'],
                'memberType' => 1,
                'switch' => 1,
                'remark' => 'SPOT',
                'status' => 1,
            ]);

        }
    }
}
