<?php

namespace Database\Seeders;

use App\Models\Wallet;
use Illuminate\Database\Seeder;

class WalletSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [1, 380000.000000000000000000, 3, 1, 1, null, '2024-05-21 17:50:00'],
        ]);

        foreach ($collection as $row) {
            Wallet::create([
                'balance' => $row[1],
                'currency_id' => $row[2],
                'user_id' => $row[3],
                'primary' => $row[4],
                'created_at' => $row[5],
                'updated_at' => $row[6],
            ]);
        }
    }
}
