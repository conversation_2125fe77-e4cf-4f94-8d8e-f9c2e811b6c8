<?php

namespace Database\Seeders;

use App\Models\Transaction;
use Illuminate\Database\Seeder;

class TransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [1, 'buy', 575000.000000000000000000, 1, 1, 1, 1, 'done', '', '{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U18653804\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}', '2024-05-18 22:07:47', '2024-05-18 22:07:47', null],
            // [2,'buy',575000.000000000000000000,1,1,1,1,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U18653804\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-05-18 22:17:51','2024-05-18 22:17:51',NULL],
            // [3,'buy',575000.000000000000000000,1,1,1,1,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U48526010\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-05-19 08:42:33','2024-05-19 08:42:33',NULL],
            // [4,'sell',0.000000000000000000,1,1,1,1,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.00\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-19 08:43:22','2024-05-19 08:43:22',NULL],
            // [5,'sell',0.000000000000000000,5,1,2,2,'done','','{\"ERROR\": \"Invalid ev_number\"}','2024-05-19 08:45:03','2024-05-19 08:45:03',NULL],
            // [6,'sell',0.000000000000000000,5,1,2,2,'done','','{\"ERROR\": \"Invalid ev_number\"}','2024-05-19 08:53:10','2024-05-19 08:53:10',NULL],
            // [7,'sell',0.000000000000000000,5,1,2,2,'done','','{\"ERROR\": \"Invalid ev_number\"}','2024-05-21 13:01:57','2024-05-21 13:01:57',NULL],
            // [8,'sell',0.000000000000000000,5,1,2,2,'done','','{\"ERROR\": \"Invalid ev_number\"}','2024-05-21 13:04:48','2024-05-21 13:04:48',NULL],
            // [9,'sell',0.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.00\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-21 13:17:21','2024-05-21 13:17:21',NULL],
            // [10,'sell',575000.000000000000000000,1,1,1,1,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.00\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-21 13:28:18','2024-05-21 13:28:18',NULL],
            // [11,'sell',575000.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.00\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-21 13:29:20','2024-05-21 13:29:20',NULL],
            // [12,'buy',600000.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U48526010\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-05-21 16:48:44','2024-05-21 16:48:44',NULL],
            // [13,'buy',720000.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U48526010\", \"PAYMENT_AMOUNT\": \"1.2\", \"VOUCHER_AMOUNT\": \"1.20\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-05-21 17:50:00','2024-05-21 17:50:00',NULL],
            // [14,'sell',690000.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.2\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-21 18:13:23','2024-05-21 18:13:23',NULL],
            // [15,'deposit',100000.000000000000000000,8,2,5,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-21 13:58:14','2024-06-21 13:58:14',NULL],
            // [16,'deposit',100000.000000000000000000,8,2,5,NULL,'declined','','{\"transaction_id\": \"**********\"}','2024-06-21 13:59:20','2024-06-21 15:32:30',NULL],
            // [17,'deposit',10000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-21 14:13:12','2024-06-21 14:13:12',NULL],
            // [18,'deposit',10000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-21 14:16:15','2024-06-21 14:16:15',NULL],
            // [19,'deposit',10000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-21 14:44:53','2024-06-21 14:44:53',NULL],
            // [20,'deposit',10000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3647201977\"}','2024-06-21 16:38:43','2024-06-21 16:38:43',NULL],
            // [21,'deposit',100000.000000000000000000,8,2,5,NULL,'waiting','','{\"transaction_id\": \"3647207719\"}','2024-06-21 16:43:10','2024-06-21 16:43:10',NULL],
            // [22,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3647209430\"}','2024-06-21 16:44:35','2024-06-21 16:44:35',NULL],
            // [23,'deposit',100000.000000000000000000,8,2,5,NULL,'waiting','','{\"transaction_id\": \"3647210399\"}','2024-06-21 16:45:20','2024-06-21 16:45:20',NULL],
            // [24,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3649764821\"}','2024-06-23 15:08:48','2024-06-23 15:08:48',NULL],
            // [25,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3649770143\"}','2024-06-23 15:13:26','2024-06-23 15:13:27',NULL],
            // [26,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','[]','2024-06-23 15:16:20','2024-06-23 15:16:20',NULL],
            // [27,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3649774332\"}','2024-06-23 15:17:08','2024-06-23 15:17:08',NULL],
            // [28,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3649784249\"}','2024-06-23 15:26:16','2024-06-23 15:26:17',NULL],
            // [29,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650287333\"}','2024-06-23 22:11:01','2024-06-23 22:11:02',NULL],
            // [30,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650292009\"}','2024-06-23 22:18:27','2024-06-23 22:18:27',NULL],
            // [31,'deposit',10050.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650297592\"}','2024-06-23 22:28:11','2024-06-23 22:28:11',NULL],
            // [32,'deposit',10050.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650297943\"}','2024-06-23 22:28:36','2024-06-23 22:28:37',NULL],
            // [33,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650303903\"}','2024-06-23 22:39:35','2024-06-23 22:39:35',NULL],
            // [34,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650305594\"}','2024-06-23 22:42:21','2024-06-23 22:42:22',NULL],
            // [35,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650307697\"}','2024-06-23 22:46:51','2024-06-23 22:46:51',NULL],
            // [36,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650689601\"}','2024-06-24 09:43:11','2024-06-24 09:43:11',NULL],
            // [37,'deposit',100000.000000000000000000,5,3,2,NULL,'declined','','{\"transaction_id\": \"**********\"}','2024-06-24 21:24:06','2024-06-24 21:24:38',NULL],
            // [38,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-24 21:24:54','2024-06-24 21:24:55',NULL],
            // [39,'buy',600000.000000000000000000,5,3,2,NULL,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U48526010\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-07-06 21:06:19','2024-07-06 21:06:19',NULL],
            // [40,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-07-06 21:14:55','2024-07-06 21:14:55',NULL],
            // [41,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-07-06 21:15:05','2024-07-06 21:15:06',NULL],
            // [42,'deposit',150750.000000000000000000,1,3,1,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-07-07 10:55:23','2024-07-07 10:55:24',NULL],
            // [43,'deposit',150750.000000000000000000,1,3,1,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-07-07 10:57:00','2024-07-07 10:57:01',NULL],
        ]);

        foreach ($collection as $row) {
            Transaction::insert([
                'id' => $row[0],
                'type' => $row[1],
                'amount' => $row[2],
                'wallet_id' => $row[3],
                'currency_id' => $row[4],
                'user_id' => $row[5],
                'registrar' => $row[6],
                'status' => $row[7],
                'description' => $row[8],
                // 'details' => $row[9],
                'created_at' => $row[10],
                'updated_at' => $row[11],
                'deleted_at' => $row[12],
            ]);
        }
    }
}
