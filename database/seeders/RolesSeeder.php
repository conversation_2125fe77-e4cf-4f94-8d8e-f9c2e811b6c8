<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [
                'name' => 'admin',
                'guard_name' => 'api',
                'created_at' => null,
                'updated_at' => null,
            ],
        ]);

        foreach ($collection as $row) {
            DB::table('roles')->insert([
                'name' => $row['name'],
                'guard_name' => $row['guard_name'],
                'created_at' => $row['created_at'],
                'updated_at' => $row['updated_at'],
            ]);
        }
    }
}
