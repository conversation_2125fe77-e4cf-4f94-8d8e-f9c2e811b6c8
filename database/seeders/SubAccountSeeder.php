<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class SubAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [
                'userId' => 4, //"66b63d860320400001b9128a",
                'uid' => *********,
                'subName' => 'AbolfazlAndAbolfazl',
                'access' => 'All',
                'remarks' => 'dKgHEoFdWQ',
            ],
            [
                'userId' => 8, // "66b7bd904724a60001a0c995"
                'uid' => *********,
                'subName' => 'saman1234',
                'access' => 'All',
                'remarks' => null,
            ],
            [
                'userId' => 8, // "66b7bde0c88dbc0001331e4e",
                'uid' => *********,
                'subName' => 'saman12344',
                'access' => 'All',
                'remarks' => null,
            ],
            [
                'userId' => 4, //"66b7c6463d40b100016492d7",
                'uid' => *********,
                'subName' => 'Yc3oA14bs2',
                'access' => 'All',
                'remarks' => null,
            ],
            [
                'userId' => 4, //"66b7c66509ae4a0001fa1070",
                'uid' => *********,
                'subName' => 'qCj7toAooU',
                'access' => 'All',
                'remarks' => null,
            ],
        ]);

        foreach ($collection as $row) {
            // DB::table('sub_accounts')->insert([
            //     'userId' => $row['userId'],
            //     'uid' => $row['uid'],
            //     'subName' => $row['subName'],
            //     'remarks' => $row['remarks'],
            //     'access' => $row['access'],
            //     'password' => $row['password'] ?? Hash::make(*********),
            // ]);
        }
    }
}
