<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PremissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [
                'users.index',
                'api',
            ],
        ]);

        foreach ($collection as $row) {
            DB::table('permissions')->insert([
                'name' => $row[0],
                'guard_name' => $row[1],
            ]);
        }

        $modelHasRoles = collect([
            [1, 'App\\Models\\User', 1],
        ]);
        foreach ($modelHasRoles as $modelHasRole) {
            DB::table('model_has_roles')->insert([
                'role_id' => $modelHasRole[0],
                'model_type' => $modelHasRole[1],
                'model_id' => $modelHasRole[2],
            ]);
        }
    }
}
