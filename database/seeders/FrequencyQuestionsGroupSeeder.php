<?php

namespace Database\Seeders;

use App\Models\FrequencyQuestionGroup;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FrequencyQuestionsGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [
                'title' => 'درباره ما'
            ],
            [
                'title' => 'خرید از سایت'
            ],
            [
                'title' => 'فروش به ما'
            ],
            [
                'title' => 'واریز ارز'
            ],
            [
                'title' => 'همکاری در فروش'
            ],
            [
                'title' => 'پشتیبانی'
            ],
        ]);

        foreach($collection as $row){
            FrequencyQuestionGroup::create([
                'title' => $row['title'],
            ]);
        }
    }
}
