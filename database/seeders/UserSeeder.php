<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [
                'email' => '<EMAIL>',
                'email_verified_at' => '2024-07-07 10:10:48',
                'phone' => 9123456789,
                'phone_verified_at' => '2024-07-07 10:10:48',
                'password' => Hash::make(123456),
                'firstname' => 'ادمین',
                'lastname' => 'اصلی',
                'national_id' => '0123456789',
                'gender' => 'male',
                'birth_date' => '2000/01/01',
                'options' => '{}',
                'remember_token' => null,
                'created_at' => '2024-05-11 21:49:48',
                'updated_at' => '2024-07-07 10:10:48',
                'level' => 1,
                'status' => 'approved',
            ],
        ]);

        foreach ($collection as $row) {
            User::create([
                'email' => $row['email'],
                'email_verified_at' => $row['email_verified_at'],
                'phone' => $row['phone'],
                'phone_verified_at' => $row['phone_verified_at'],
                'password' => $row['password'],
                'firstname' => $row['firstname'],
                'lastname' => $row['lastname'],
                'national_id' => $row['national_id'],
                'gender' => $row['gender'],
                'birth_date' => $row['birth_date'],
                'options' => $row['options'],
                'remember_token' => $row['remember_token'],
                'created_at' => $row['created_at'],
                'updated_at' => $row['updated_at'],
                'level' => $row['level'],
                'status' => $row['status'],
            ]);
        }

        User::factory()->count(10)->create();
    }
}
