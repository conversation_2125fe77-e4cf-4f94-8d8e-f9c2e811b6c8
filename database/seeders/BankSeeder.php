<?php

namespace Database\Seeders;

use App\Models\Bank;
use Illuminate\Database\Seeder;

class BankSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collection = collect([
            [
                'name' => 'دی',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '502938',
            ],
            [
                'name' => 'ایران زمین',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '505785',
            ],
            [
                'name' => 'توسعه تعاون',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '502908',
            ],
            [
                'name' => 'ملی ایران',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '603799',
            ],
            [
                'name' => 'رفاه کارگران',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '589463',
            ],
            [
                'name' => 'سامان',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '621986',
            ],
            [
                'name' => 'سپه',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '589210',
            ],
            [
                'name' => 'تجارت',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '627353',
            ],
            [
                'name' => 'انصار (سپه)',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '627381',
            ],
            [
                'name' => 'شهر',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '502806',
            ],
            [
                'name' => 'اقتصاد نوین',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '627412',
            ],
            [
                'name' => 'صادرات ایران',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '603769',
            ],
            [
                'name' => 'توسعه صادرات ایران',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '627648',
            ],
            [
                'name' => 'قرض الحسنه مهر ایران',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '606373',
            ],
            [
                'name' => 'صنعت و معدن',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '627961',
            ],
            [
                'name' => 'کارآفرین',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '502910',
            ],
            [
                'name' => 'آینده',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '636214',
            ],
            [
                'name' => 'گردشگری',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '505416',
            ],
            [
                'name' => 'مرکزی',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '636795',
            ],
            [
                'name' => 'حکمت ایرانیان (سپه)',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '636949',
            ],
            [
                'name' => 'مسکن',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '628023',
            ],
            [
                'name' => 'پارسیان',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '639194',
            ],
            [
                'name' => 'کشاورزی',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '639217',
            ],
            [
                'name' => 'سینا',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '639346',
            ],
            [
                'name' => 'پاسارگاد',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '639347',
            ],
            [
                'name' => 'مهر اقتصاد (سپه)',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '639370',
            ],
            [
                'name' => 'پست ایران',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '627760',
            ],
            [
                'name' => 'قوامین (سپه)',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '639599',
            ],
            [
                'name' => 'موسسه اعتباری توسعه',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '628157',
            ],
            [
                'name' => 'سرمایه',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '639607',
            ],
            [
                'name' => 'ملت',
                'icon' => '/storage/bank-icons/mellat.png',
                'prefix' => '610433',
            ],
        ]);
        foreach ($collection as $row) {
            Bank::firstOrCreate([
                'name' => $row['name'],
                'icon' => $row['icon'],
                'prefix' => $row['prefix'],
            ]);
        }
    }
}
