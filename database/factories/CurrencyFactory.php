<?php

namespace Database\Factories;

use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class CurrencyFactory extends Factory
{
    protected $model = Currency::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'code' => $this->faker->word(),
            'crypto' => $this->faker->boolean(),
            'needs_approval' => $this->faker->boolean(),
            'icon' => $this->faker->word(),
            'provider' => $this->faker->word(),
            'buy' => $this->faker->randomFloat(),
            'sell' => $this->faker->randomFloat(),
            'active' => $this->faker->word(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
