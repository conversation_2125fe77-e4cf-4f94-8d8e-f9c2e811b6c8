<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

class UserFactory extends Factory
{
    protected $model = \App\Models\User::class;

    public function definition()
    {
        $genderArray = ['male', 'female', 'undefined'];
        $statusArray = ['pending', 'approved', 'rejected'];

        return [
            'email' => $this->faker->unique()->safeEmail(),
            'password' => Hash::make(123456), // رمز عبور ثابت
            'phone' => 9 .''.rand(111111111, 999999999),
            'firstname' => $this->faker->firstName(), //)->nullable();
            'lastname' => $this->faker->lastName(), //)->nullable();
            'national_id' => rand(1111111111, 999999999), //)->nullable();
            'gender' => $genderArray[array_rand($genderArray)],
            'birth_date' => $this->faker->date(max: 'now'),
            'options' => null,
            'status' => $statusArray[array_rand($statusArray)],
            'level' => 1,
        ];
    }
}
