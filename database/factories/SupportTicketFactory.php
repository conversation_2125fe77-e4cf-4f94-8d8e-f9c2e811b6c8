<?php

namespace Database\Factories;

use App\Models\Support;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SupportTicket>
 */
class SupportTicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $support = Support::inRandomOrder()->first();

        return [
            'support_id' => $support->id,
            'user_id' => $support->unit_id,
            'message' => $this->faker->text(),
        ];
    }
}
