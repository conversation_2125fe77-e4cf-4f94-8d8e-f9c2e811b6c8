<?php

namespace Database\Factories;

use App\Models\SupportLevel;
use App\Models\SupportUnit;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Support>
 */
class SupportFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $statusArray = [0 => 'deactive', 1 => 'admin_response', 2 => 'user_response', 3 => 'processing'];

        return [
            'subject' => $this->faker->text(),
            'unit_id' => SupportUnit::inRandomOrder()->first(),
            'level_id' => SupportLevel::inRandomOrder()->first(),
            'user_id' => User::inRandomOrder()->first(),
            'status' => $statusArray[array_rand($statusArray)],
        ];

    }
}
