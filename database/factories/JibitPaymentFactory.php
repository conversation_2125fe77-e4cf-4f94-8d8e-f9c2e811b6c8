<?php

namespace Database\Factories;

use App\Models\JibitPayment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\JibitPayment>
 */
class JibitPaymentFactory extends Factory
{
    protected $model = JibitPayment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'payment_identifier' => 'PIP_' . time() . '_' . $this->faker->randomNumber(8),
            'psp_switching_url' => null,
            'amount' => $this->faker->numberBetween(10000, 1000000),
            'currency' => 'T',
            'description' => $this->faker->sentence(),
            'status' => $this->faker->randomElement(['pending', 'paid', 'failed', 'expired']),
            'reference_number' => $this->faker->optional()->numerify('##########'),
            'trace_number' => $this->faker->optional()->numerify('##########'),
            'paid_at' => $this->faker->optional()->dateTimeBetween('-1 month', 'now'),
            'callback_data' => null,
            'request_data' => [
                'merchantReferenceNumber' => 'MRN_' . time() . '_' . $this->faker->randomNumber(8),
                'userFullName' => $this->faker->name(),
                'userMobile' => $this->faker->phoneNumber(),
            ],
            'response_data' => [
                'payId' => $this->faker->uuid(),
                'registryStatus' => 'VERIFIED',
            ],
            'ip_address' => $this->faker->ipv4(),
        ];
    }

    /**
     * Indicate that the payment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'paid_at' => null,
        ]);
    }

    /**
     * Indicate that the payment is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paid',
            'paid_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'reference_number' => $this->faker->numerify('##########'),
            'trace_number' => $this->faker->numerify('##########'),
        ]);
    }

    /**
     * Indicate that the payment is failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'paid_at' => null,
        ]);
    }
}
