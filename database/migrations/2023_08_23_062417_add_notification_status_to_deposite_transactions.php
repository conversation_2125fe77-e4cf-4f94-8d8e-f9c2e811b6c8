<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('deposite_transactions', function (Blueprint $table) {
            $table->enum('notification_status', ['pending', 'sent'])->default('pending');
        });
    }

    public function down()
    {
        Schema::table('deposite_transactions', function (Blueprint $table) {
            $table->dropColumn('notification_status');
        });
    }
};