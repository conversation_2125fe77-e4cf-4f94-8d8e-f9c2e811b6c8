<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // افزودن ستون toman_balance به جدول users
            $table->decimal('toman_balance', 15, 2)->default(0)->after('balance'); // بعد از ستون balance اضافه می‌شود
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // حذف ستون toman_balance در صورت rollback
            $table->dropColumn('toman_balance');
        });
    }
};
