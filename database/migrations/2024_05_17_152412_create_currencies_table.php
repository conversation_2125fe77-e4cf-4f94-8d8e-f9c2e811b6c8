<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code');
            $table->string('network', 45)->nullable();
            $table->string('apiKey');
            $table->boolean('crypto')->nullable();
            $table->boolean('needs_approval')->default(false);
            $table->text('icon')->nullable();
            $table->string('provider')->nullable();
            $table->decimal('buy', 36, 18)->nullable();
            $table->decimal('sell', 36, 18)->nullable();
            $table->enum('active', ['no', 'yes', 'buy', 'sell'])->default('yes');
            $table->timestamps();
            $table->unique(['code', 'crypto']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currencies');
    }
};
