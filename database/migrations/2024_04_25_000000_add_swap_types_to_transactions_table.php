<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // اضافه کردن مقادیر جدید به enum ستون type
        DB::statement("ALTER TABLE transactions MODIFY COLUMN type ENUM('deposit', 'withdraw', 'gift', 'transfer', 'buy', 'sell', 'increase', 'decrease', 'swap_out', 'swap_in') NOT NULL DEFAULT 'deposit'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // برگرداندن به حالت قبلی
        DB::statement("ALTER TABLE transactions MODIFY COLUMN type ENUM('deposit', 'withdraw', 'gift', 'transfer', 'buy', 'sell', 'increase', 'decrease') NOT NULL DEFAULT 'deposit'");
    }
};
