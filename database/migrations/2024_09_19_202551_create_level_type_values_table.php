<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('level_type_values', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('level_id');
            $table->foreign('level_id')->references('id')->on('levels')->cascadeOnDelete()->cascadeOnUpdate();
            $table->unsignedBigInteger('level_item_id');
            $table->foreign('level_item_id')->references('id')->on('level_items')->cascadeOnDelete()->cascadeOnUpdate();
            $table->unsignedBigInteger('level_type_id');
            $table->foreign('level_type_id')->references('id')->on('level_types')->cascadeOnDelete()->cascadeOnUpdate();
            $table->string('value')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('level_type_values');
    }
};
