<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('usd_prices', function (Blueprint $table) {
            $table->id();
            $table->decimal('buy_price', 15, 2)->comment('قیمت خرید دلار به تومان');
            $table->decimal('sell_price', 15, 2)->comment('قیمت فروش دلار به تومان');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
        
        // Insert default USD price
        DB::table('usd_prices')->insert([
            'buy_price' => 80500, // قیمت خرید دلار به تومان
            'sell_price' => 81000, // قیمت فروش دلار به تومان
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('usd_prices');
    }
};
