<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransactionNotificationsTable extends Migration
{
    public function up()
    {
        Schema::create('transaction_notifications', function (Blueprint $table) {
            $table->id();
            $table->json('payload');
            $table->string('transaction_id')->nullable();
            $table->string('status')->default('pending');
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('transaction_notifications');
    }
}