<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tron_nodes', function (Blueprint $table) {
            $table->id();
            $table->string('name')
                ->unique();
            $table->string('title')
                ->nullable();
            $table->json('full_node');
            $table->json('solidity_node');
            $table->unsignedBigInteger('block_number');
            $table->unsignedInteger('requests')
                ->default(0);
            $table->date('requests_at')
                ->nullable();
            $table->timestamp('sync_at')
                ->nullable();
            $table->boolean('worked')
                ->default(true);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tron_nodes');
    }
};
