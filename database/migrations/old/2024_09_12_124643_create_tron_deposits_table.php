<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Mollsoft\LaravelTronModule\Models\TronAddress;
use Mollsoft\LaravelTronModule\Models\TronTRC20;
use <PERSON>llsoft\LaravelTronModule\Models\TronWallet;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tron_deposits', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(TronWallet::class, 'wallet_id')
                ->constrained('tron_wallets')
                ->cascadeOnDelete();
            $table->foreignIdFor(TronAddress::class, 'address_id')
                ->constrained('tron_addresses')
                ->cascadeOnDelete();
            $table->foreignIdFor(TronTRC20::class, 'trc20_id')
                ->nullable()
                ->constrained('tron_trc20')
                ->cascadeOnDelete();
            $table->string('txid');
            $table->decimal('amount', 20, 6)
                ->unsigned();
            $table->unsignedBigInteger('block_height')
                ->nullable();
            $table->unsignedInteger('confirmations')
                ->default(0);
            $table->timestamp('time_at');

            $table->unique(['address_id', 'txid'], 'unique_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tron_deposits');
    }
};
