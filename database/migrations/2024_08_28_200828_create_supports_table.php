<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('supports', function (Blueprint $table) {
            $table->id();
            $table->string('subject');

            $table->unsignedBigInteger('unit_id');
            $table->foreign('unit_id')->references('id')->on('support_units')->cascadeOnDelete()->cascadeOnUpdate();

            $table->unsignedBigInteger('level_id');
            $table->foreign('level_id')->references('id')->on('support_levels')->cascadeOnDelete()->cascadeOnUpdate();

            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete()->cascadeOnUpdate();

            $table->enum('status', [0 => 'deactive', 1 => 'admin_response', 2 => 'user_response', 3 => 'processing']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('supports');
    }
};
