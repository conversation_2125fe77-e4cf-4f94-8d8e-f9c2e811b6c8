<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // افزودن ستون balance به جدول users
            $table->decimal('balance', 15, 2)->default(0)->after('email'); // بعد از ستون email اضافه می‌شود
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // حذف ستون balance در صورت rollback
            $table->dropColumn('balance');
        });
    }
};
