<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'google2fa_secret')) {
                $table->string('google2fa_secret')->nullable()->after('level');
            }
            
            if (!Schema::hasColumn('users', 'g2f_enabled')) {
                $table->boolean('g2f_enabled')->default(0)->after('google2fa_secret');
            }
            
            if (!Schema::hasColumn('users', 'phone_enabled')) {
                $table->boolean('phone_enabled')->default(0)->after('g2f_enabled');
            }
            
            if (!Schema::hasColumn('users', 'email_enabled')) {
                $table->boolean('email_enabled')->default(0)->after('phone_enabled');
            }
            
            if (!Schema::hasColumn('users', 'otp_code')) {
                $table->string('otp_code')->nullable()->after('email_enabled');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['google2fa_secret', 'g2f_enabled', 'phone_enabled', 'email_enabled', 'otp_code']);
        });
    }
};
