<?php

use App\Http\Middleware\Authorize;
use App\Http\Middleware\CorsMiddleware;
use App\Http\Middleware\ForceJson;
use App\Http\Middleware\LogActivity;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Facade;
use Illuminate\Support\Facades\Route;
use App\Console\ConsoleScheduler;
use Illuminate\Contracts\Console\Kernel;
use App\Http\Middleware\DecryptRSAInput;


return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        then: function () {
            Route::middleware(['api', 'auth:sanctum'])->prefix('api')->group(function () {
                //Route::name('admin.')->group(base_path('routes/admin.php'));
                Route::name('user.')->group(base_path('routes/user.php'));
            });
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware
            ->alias([])
            ->api(prepend: [
                ForceJson::class,
                LogActivity::class,
                // CorsMiddleware::class,
            ])
            ->alias([
                'authorize' => Authorize::class,
                'decrypt.rsa' => DecryptRSAInput::class,
            ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->withBindings([
        Kernel::class => ConsoleScheduler::class
    ])
    ->create();
