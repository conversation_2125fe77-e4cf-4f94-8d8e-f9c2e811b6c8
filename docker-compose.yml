services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: laravel_app
    restart: unless-stopped
    working_dir: /var/www/laravel
    volumes:
      - .:/var/www/laravel
    networks:
      - laravel

  web:
    image: nginx:latest
    container_name: laravel_web
    restart: unless-stopped
    ports:
      - "90:80"
    volumes:
      - .:/var/www/laravel
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    networks:
      - laravel
    depends_on:
      - app

  db:
    image: mysql:latest
    container_name: laravel_db
    restart: unless-stopped
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: 1234
    volumes:
      - dbdata:/var/lib/mysql
    networks:
      - laravel

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    platform: linux/amd64
    container_name: phpmyadmin
    restart: unless-stopped
    ports:
      - "9090:80"
    environment:
      PMA_HOST: db
      MYSQL_ROOT_PASSWORD: 1234
    depends_on:
      - db
    networks:
      - laravel

  redis:
    image: redis:latest
    container_name: laravel_redis
    restart: unless-stopped
    networks:
      - laravel

networks:
  laravel:
    driver: bridge

volumes:
  dbdata:
