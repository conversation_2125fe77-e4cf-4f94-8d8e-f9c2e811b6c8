<?php

// use App\Http\Controllers\Admin\ActivityController;
// use App\Http\Controllers\Admin\BankController;
// use App\Http\Controllers\Admin\CardController;
// use App\Http\Controllers\Admin\Cart\ShebaController;
// use App\Http\Controllers\Admin\CurrencyController;
// use App\Http\Controllers\Admin\DashboardController;
// use App\Http\Controllers\Admin\DocumentController;
// use App\Http\Controllers\Admin\FrequencyQuestionController;
// // use App\Http\Controllers\Admin\FrequencyQuestionGroupController;
// use App\Http\Controllers\Admin\LevelsController;
// use App\Http\Controllers\Admin\SettingController;
// use App\Http\Controllers\Admin\SettlementController;
// use App\Http\Controllers\Admin\SettlementInfoController;
// use App\Http\Controllers\Admin\SupportController;
// use App\Http\Controllers\Admin\TradeController;
// use App\Http\Controllers\Admin\TransactionController;
// use App\Http\Controllers\Admin\TomanWithdrawalController;
// use App\Http\Controllers\Admin\User\AmountController;
// use App\Http\Controllers\Admin\User\LoginController;
// use App\Http\Controllers\Admin\User\UserAdminController;
// use App\Http\Controllers\Admin\User\UserBirthController;
// use App\Http\Controllers\Admin\User\UserController;
// use App\Http\Controllers\Admin\User\UserInfoController;
// use App\Http\Controllers\Admin\Wallet\WalletController;
// use App\Http\Middleware\ForceJson;
// use Illuminate\Support\Facades\Route;

// Route::prefix('admin')->middleware('authorize')->group(function () {

//     Route::prefix('accounting')->name('accounting.')->group(function () {
//         Route::prefix('settlement')->name('settlement.')->group(function () {
//             Route::prefix('info')->name('info.')->group(function () {
//                 Route::controller(SettlementInfoController::class)->group(function () {
//                     Route::get('', 'index')->name('index');
//                     Route::post('', 'store')->name('store');
//                 });
//             });
//             Route::controller(SettlementController::class)->group(function () {
//                 Route::get('', 'index')->name('index');
//                 Route::post('', 'store')->name('store');
//                 Route::get('{id}', 'show')->name('show');
//                 Route::post('{id}', 'update')->name('update');
//             });
//         });

//         Route::prefix('toman-withdrawal')->name('toman-withdrawal.')->group(function () {
//             Route::controller(TomanWithdrawalController::class)->group(function () {
//                 Route::get('pending', 'pending')->name('pending');
//                 Route::get('approved', 'approved')->name('approved');
//                 Route::get('rejected', 'rejected')->name('rejected');
//                 Route::post('approve/{id}', 'approve')->name('approve');
//                 Route::post('reject/{id}', 'reject')->name('reject');
//             });
//         });
//     });

//     Route::apiResource(name: '', controller: DashboardController::class);

//     Route::prefix('user')->name('user.')->group(function () {
//         Route::apiResource('info', UserInfoController::class)->only(['index']);
//         Route::apiResource('amount', AmountController::class)->only(['store']);
//         Route::apiResource('logins', LoginController::class)->only(['show']);
//         Route::apiResource('birth-list', UserBirthController::class)->only(['index']);
//         Route::apiResource('admin-list', UserAdminController::class)->only(['index']);

//         Route::controller(UserController::class)->group(function () {
//             Route::get('', 'index')->name('index');
//             Route::post('', 'store')->name('store');
//             Route::get('{id}', 'show')->name('show');
//             Route::post('{id}', 'update')->name('update');
//             Route::delete('{id}', 'destroy')->name('destroy');
//         });

//     });
//     Route::prefix('support')->name('support.')->group(function () {
//         Route::controller(SupportController::class)->group(function () {
//             Route::get('', 'index')->name('index');
//             Route::get('/create/{userId?}', 'create')->name('create');
//             Route::post('', 'store')->name('store');
//             Route::get('{id}', 'show')->name('show');
//             Route::post('{id}', 'update')->name('update');
//             Route::delete('{id}', 'destroy')->name('destroy');
//         });
//     });



//     Route::apiResource('currency', CurrencyController::class);





//     // Route::apiResource('card/sheba/{id}', ShebaController::class);
//     Route::prefix('card/sheba')->name('card.sheba.')->group(function () {
//         Route::controller(ShebaController::class)->group(function () {
//             Route::post('{id}', 'update')->name('update');
//         });
//     });
//     // to show document file
//     Route::get('documents/{document}/file', [DocumentController::class, 'file'])
//         ->withoutMiddleware(ForceJson::class)->name('document.file');

//     Route::controller(TradeController::class)->group(function () {
//         Route::post('buy', 'buy');
//         Route::post('sell', 'sell');
//     });


//     Route::prefix('frequency-question')->name('frequency-question')->group(function(){
//         Route::controller(FrequencyQuestionController::class)->group(function(){
//             Route::get('', 'index')->name('index');
//             Route::post('', 'store')->name('store');
//             Route::get('{id}', 'show')->name('show');
//             Route::post('{id}', 'update')->name('update');
//             Route::delete('{id}', 'destroy')->name('delete');
//         });
//         // Route::apiResource('/group',FrequencyQuestionGroupController::class);
//     });

//     Route::prefix('activity')->name('activity')->group(function(){
//         Route::controller(ActivityController::class)->group(function(){
//             Route::get('', 'index')->name('index');
//         });
//     });

// });
