FROM php:8.3-fpm

# نصب ابزارها و وابستگی‌ها
RUN apt-get update && apt-get install -y \
    curl \
    git \
    unzip \
    libexif-dev \
    libgmp-dev \
    libonig-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    libzip-dev \
    ffmpeg \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install pdo pdo_mysql gd zip \
    && apt-get install -y libonig-dev \
    && docker-php-ext-install exif

# نصب mpdecimal و افزونه decimal
RUN if [ -z "$EXT_DISABLE_DECIMAL" ] || [ "$EXT_DISABLE_DECIMAL" = "0" ] || [ "$EXT_DISABLE_DECIMAL" = "false" ]; then \
    MPDEC_RELEASE_NAME="mpdecimal-2.5.1"; \
    MPDEC_URL="https://www.bytereef.org/software/mpdecimal/releases/$MPDEC_RELEASE_NAME.tar.gz"; \
    MPDEC_SHA256_SUM="9f9cd4c041f99b5c49ffb7b59d9f12d95b683d88585608aa56a6307667b2b21f"; \
    MPDEC_TMP_DIR="/tmp/$MPDEC_RELEASE_NAME"; \
    MPDEC_TMP_ARCHIVE="$MPDEC_TMP_DIR/$MPDEC_RELEASE_NAME.tar.gz"; \
    mkdir -p $MPDEC_TMP_DIR; \
    cd $MPDEC_TMP_DIR; \
    curl -LO $MPDEC_URL; \
    echo "$MPDEC_SHA256_SUM $MPDEC_TMP_ARCHIVE" | sha256sum --check --status -; \
    tar xf $MPDEC_TMP_ARCHIVE; \
    cd $MPDEC_RELEASE_NAME; \
    ./configure && make && make install; \
    rm -rf $MPDEC_TMP_DIR; \
    pecl install decimal && docker-php-ext-enable decimal; \
fi

# # نصب Composer
# RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# پیکربندی PHP
RUN ln -s /usr/include/x86_64-linux-gnu/gmp.h /usr/include/gmp.h && \
    echo memory_limit = 256M > $(php -r 'echo PHP_CONFIG_FILE_SCAN_DIR;')/zz-custom.ini && \
    docker-php-ext-install mbstring gmp pdo_mysql mysqli

# # تنظیمات محیط
# ENV PATH=~/.composer/vendor/bin:$PATH

# CMD if [ ! -f composer.lock ]; then composer install; fi && \
#     vendor/bin/phpunit ${COVERAGE_FILE:+ --coverage-text --coverage-clover=}$COVERAGE_FILE
RUN docker-php-ext-install bcmath

# کپی فایل php.ini به تصویر
# COPY php.ini /usr/local/etc/php/conf.d/
