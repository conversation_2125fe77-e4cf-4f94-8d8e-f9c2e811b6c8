-- MySQL dump 10.13  Distrib 8.4.2, for macos14 (arm64)
--
-- Host: 127.0.0.1    Database: laracoin
-- ------------------------------------------------------
-- Server version	9.0.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `banks`
--

DROP TABLE IF EXISTS `banks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `banks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `prefix` varchar(6) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `banks_prefix_unique` (`prefix`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `banks`
--

LOCK TABLES `banks` WRITE;
/*!40000 ALTER TABLE `banks` DISABLE KEYS */;
INSERT INTO `banks` VALUES (2,'دی','/storage/bank-icons/mellat.png','502938'),(3,'ایران زمین','/storage/bank-icons/mellat.png','505785'),(4,'توسعه تعاون','/storage/bank-icons/mellat.png','502908'),(5,'ملی ایران','/storage/bank-icons/mellat.png','603799'),(6,'رفاه کارگران','/storage/bank-icons/mellat.png','589463'),(7,'سامان','/storage/bank-icons/mellat.png','621986'),(8,'سپه','/storage/bank-icons/mellat.png','589210'),(9,'تجارت','/storage/bank-icons/mellat.png','627353'),(10,'انصار (سپه)','/storage/bank-icons/mellat.png','627381'),(11,'شهر','/storage/bank-icons/mellat.png','502806'),(12,'اقتصاد نوین','/storage/bank-icons/mellat.png','627412'),(13,'صادرات ایران','/storage/bank-icons/mellat.png','603769'),(14,'توسعه صادرات ایران','/storage/bank-icons/mellat.png','627648'),(15,'قرض الحسنه مهر ایران','/storage/bank-icons/mellat.png','606373'),(17,'صنعت و معدن','/storage/bank-icons/mellat.png','627961'),(18,'کارآفرین','/storage/bank-icons/mellat.png','502910'),(19,'آینده','/storage/bank-icons/mellat.png','636214'),(20,'گردشگری','/storage/bank-icons/mellat.png','505416'),(21,'مرکزی','/storage/bank-icons/mellat.png','636795'),(22,'حکمت ایرانیان (سپه)','/storage/bank-icons/mellat.png','636949'),(23,'مسکن','/storage/bank-icons/mellat.png','628023'),(24,'پارسیان','/storage/bank-icons/mellat.png','639194'),(25,'کشاورزی','/storage/bank-icons/mellat.png','639217'),(26,'سینا','/storage/bank-icons/mellat.png','639346'),(27,'پاسارگاد','/storage/bank-icons/mellat.png','639347'),(28,'مهر اقتصاد (سپه)','/storage/bank-icons/mellat.png','639370'),(29,'پست ایران','/storage/bank-icons/mellat.png','627760'),(30,'قوامین (سپه)','/storage/bank-icons/mellat.png','639599'),(31,'موسسه اعتباری توسعه','/storage/bank-icons/mellat.png','628157'),(32,'سرمایه','/storage/bank-icons/mellat.png','639607'),(33,'موسسه اعتباری کوثر (سپه)','/storage/bank-icons/mellat.png','505801'),(34,'ملت','/storage/bank-icons/mellat.png','610433');
/*!40000 ALTER TABLE `banks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cache`
--

DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cache`
--

LOCK TABLES `cache` WRITE;
/*!40000 ALTER TABLE `cache` DISABLE KEYS */;
/*!40000 ALTER TABLE `cache` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cache_locks`
--

DROP TABLE IF EXISTS `cache_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cache_locks`
--

LOCK TABLES `cache_locks` WRITE;
/*!40000 ALTER TABLE `cache_locks` DISABLE KEYS */;
/*!40000 ALTER TABLE `cache_locks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cards`
--

DROP TABLE IF EXISTS `cards`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cards` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `iban` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('pending','approved','rejected') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `bank_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cards_number_unique` (`number`),
  UNIQUE KEY `cards_iban_unique` (`iban`),
  KEY `cards_user_id_foreign` (`user_id`),
  KEY `cards_bank_id_foreign` (`bank_id`),
  CONSTRAINT `cards_bank_id_foreign` FOREIGN KEY (`bank_id`) REFERENCES `banks` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `cards_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cards`
--

LOCK TABLES `cards` WRITE;
/*!40000 ALTER TABLE `cards` DISABLE KEYS */;
INSERT INTO `cards` VALUES (3,2,'****************',NULL,'pending','2024-05-14 06:18:36','2024-05-14 09:01:16',7),(4,2,'****************',NULL,'pending','2024-05-14 06:19:19','2024-05-14 06:19:19',7),(5,2,'****************',NULL,'pending','2024-05-14 06:32:38','2024-05-14 06:32:38',5),(6,2,'****************',NULL,'pending','2024-05-14 07:53:32','2024-05-14 07:53:32',20),(7,2,'****************',NULL,'pending','2024-05-14 08:04:37','2024-05-14 08:04:37',19);
/*!40000 ALTER TABLE `cards` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `currencies`
--

DROP TABLE IF EXISTS `currencies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `currencies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `crypto` tinyint(1) NOT NULL,
  `needs_approval` tinyint(1) NOT NULL DEFAULT '0',
  `icon` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `buy` decimal(36,18) DEFAULT NULL,
  `sell` decimal(36,18) DEFAULT NULL,
  `active` enum('no','yes','buy','sell') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'yes',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `currencies_code_crypto_unique` (`code`,`crypto`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `currencies`
--

LOCK TABLES `currencies` WRITE;
/*!40000 ALTER TABLE `currencies` DISABLE KEYS */;
INSERT INTO `currencies` VALUES (1,'ووچر پرفکت مانی','pmv',0,0,'/images/pmVoucher.svg','currencies.pmv',600000.000000000000000000,575000.000000000000000000,'yes',NULL,NULL),(2,'پرفکت مانی','pm',0,0,'/images/pmVoucher.svg',NULL,600000.000000000000000000,575000.000000000000000000,'yes',NULL,NULL),(3,'ریال','irr',0,1,'/images/irt.svg',NULL,1.000000000000000000,1.000000000000000000,'no',NULL,NULL),(24,'تتر','usdt',1,1,'/images/irt.svg','currencies.kucoin',595000.000000000000000000,575000.000000000000000000,'yes',NULL,NULL),(25,'ترون','trx',1,1,'/images/irt.svg','currencies.kucoin',81450.000000000000000000,81200.000000000000000000,'yes',NULL,NULL);
/*!40000 ALTER TABLE `currencies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `documents`
--

DROP TABLE IF EXISTS `documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `documents` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `type` enum('video','image','audio','document') COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('pending','approved','rejected') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  PRIMARY KEY (`id`),
  UNIQUE KEY `documents_name_user_id_unique` (`name`,`user_id`),
  KEY `documents_user_id_foreign` (`user_id`),
  KEY `documents_name_index` (`name`),
  CONSTRAINT `documents_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `documents`
--

LOCK TABLES `documents` WRITE;
/*!40000 ALTER TABLE `documents` DISABLE KEYS */;
INSERT INTO `documents` VALUES (17,2,'image','consent','2024-06-19 18:54:18','2024-07-05 13:35:20','documents/2/consent-e1f0d897.webp','rejected');
/*!40000 ALTER TABLE `documents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `failed_jobs`
--

DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `failed_jobs`
--

LOCK TABLES `failed_jobs` WRITE;
/*!40000 ALTER TABLE `failed_jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `failed_jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `job_batches`
--

DROP TABLE IF EXISTS `job_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `job_batches`
--

LOCK TABLES `job_batches` WRITE;
/*!40000 ALTER TABLE `job_batches` DISABLE KEYS */;
/*!40000 ALTER TABLE `job_batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `jobs`
--

DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint unsigned NOT NULL,
  `reserved_at` int unsigned DEFAULT NULL,
  `available_at` int unsigned NOT NULL,
  `created_at` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `jobs`
--

LOCK TABLES `jobs` WRITE;
/*!40000 ALTER TABLE `jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'0001_01_01_000000_create_users_table',1),(2,'0001_01_01_000001_create_cache_table',1),(3,'0001_01_01_000002_create_jobs_table',1),(4,'2024_05_09_172528_create_personal_access_tokens_table',1),(5,'2024_05_11_215740_add_status_and_level_columns_to_users_table',1),(6,'2024_05_11_222201_create_documents_table',1),(7,'2024_05_12_204001_add_status_field_to_documents_table',1),(8,'2024_05_12_205318_add_unique_key_to_documents_table',1),(9,'2024_05_13_102102_create_cards_table',1),(10,'2024_05_13_153205_change_status_field_from_documents_table',1),(11,'2024_05_13_155920_remove_name_field_from_cards_table',1),(12,'2024_05_13_161351_create_banks_table',1),(13,'2024_05_13_175305_change_status_field_from_users_table',1),(14,'2024_05_17_152412_create_currencies_table',1),(15,'2024_05_17_157523_create_wallets_table',1),(16,'2024_05_18_160914_create_transactions_table',1),(17,'2024_05_24_094757_create_permission_tables',1);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `model_has_permissions`
--

DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `model_has_permissions`
--

LOCK TABLES `model_has_permissions` WRITE;
/*!40000 ALTER TABLE `model_has_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `model_has_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `model_has_roles`
--

DROP TABLE IF EXISTS `model_has_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `model_has_roles` (
  `role_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `model_has_roles`
--

LOCK TABLES `model_has_roles` WRITE;
/*!40000 ALTER TABLE `model_has_roles` DISABLE KEYS */;
INSERT INTO `model_has_roles` VALUES (1,'App\\Models\\User',1),(1,'App\\Models\\User',4),(1,'App\\Models\\User',5);
/*!40000 ALTER TABLE `model_has_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `password_reset_tokens`
--

LOCK TABLES `password_reset_tokens` WRITE;
/*!40000 ALTER TABLE `password_reset_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `password_reset_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (2,'users.index','api','2024-05-24 16:25:02','2024-05-24 16:25:02');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `personal_access_tokens`
--

DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB AUTO_INCREMENT=117 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `personal_access_tokens`
--

LOCK TABLES `personal_access_tokens` WRITE;
/*!40000 ALTER TABLE `personal_access_tokens` DISABLE KEYS */;
INSERT INTO `personal_access_tokens` VALUES (1,'App\\Models\\User',1,'phone.9128265825','0be97d234f6b17524253e221dce62008c0e7e1b94b5be562c8055048e034987c','[\"*\"]','2024-05-11 22:16:26',NULL,'2024-05-11 21:49:48','2024-05-11 22:16:26'),(2,'App\\Models\\User',2,'phone.9120396189','b76958d784c3f55eb470b360ce4274e2fb961ea098beb23a8feb201e12696326','[\"*\"]','2024-06-24 21:24:54',NULL,'2024-05-11 21:55:02','2024-06-24 21:24:54'),(3,'App\\Models\\User',2,'phone.9120396189','007f923ff71f0f9b4988c0a6af9a01008bcf1715841bf36f701a91d0131f68a7','[\"*\"]','2024-05-12 15:11:41',NULL,'2024-05-11 23:05:36','2024-05-12 15:11:41'),(4,'App\\Models\\User',2,'phone.9120396189','9c5a99bee657829c7de1a9dc0df311dba120caa0d7b0ac76453dc328e203982f','[\"*\"]','2024-05-12 00:13:41',NULL,'2024-05-12 00:12:45','2024-05-12 00:13:41'),(5,'App\\Models\\User',2,'phone.9120396189','4b3bf42a5e18628a3201821a53938bcee726f81a76c2707af953fda7ff198527','[\"*\"]','2024-05-12 15:15:31',NULL,'2024-05-12 15:13:55','2024-05-12 15:15:31'),(6,'App\\Models\\User',2,'phone.9120396189','2b9bf41c9e0ab426d664bbea341ce64a31a41fa3e166252263df6d8b6d56af02','[\"*\"]','2024-05-12 16:04:59',NULL,'2024-05-12 15:16:09','2024-05-12 16:04:59'),(7,'App\\Models\\User',2,'phone.9120396189','7837335d2b79721ec28c1fb0aa2ab25a434a8547ca97fe883a7dae46656adf53','[\"*\"]','2024-05-12 17:31:30',NULL,'2024-05-12 17:27:54','2024-05-12 17:31:30'),(8,'App\\Models\\User',2,'phone.9120396189','f6a932f8f7eac5b866133216215c7588562ae0a7c920a03f55b716cdad2d8435','[\"*\"]','2024-05-12 18:05:13',NULL,'2024-05-12 17:34:54','2024-05-12 18:05:13'),(9,'App\\Models\\User',2,'phone.9120396189','8cc1d58031f01471d6e2d067bc662fffacceea69330fcd4b921b717b560e5077','[\"*\"]','2024-05-22 16:25:40',NULL,'2024-05-12 19:31:01','2024-05-22 16:25:40'),(10,'App\\Models\\User',1,'phone.9128265825','151ba6f33a44197fcc5ff8071aedcbe20bc852edcaa5c681cb83b8bd10476366','[\"*\"]','2024-05-25 17:31:16',NULL,'2024-05-12 22:24:24','2024-05-25 17:31:16'),(11,'App\\Models\\User',3,'phone.9220877254','61668464c342790ce79a21fc50670f1a2ce3a2847f3acdf5ddbfab710aebdaa2','[\"*\"]','2024-05-22 16:43:01',NULL,'2024-05-22 16:26:38','2024-05-22 16:43:01'),(12,'App\\Models\\User',2,'phone.9120396189','f35dba2eae36428bc6f9849de2b211d80847214249049a21c36abf5219e42788','[\"*\"]','2024-06-25 08:46:47',NULL,'2024-05-22 19:15:22','2024-06-25 08:46:47'),(13,'App\\Models\\User',1,'phone.9128265825','fee3826fcaaeb510388f792ad3fcc46f826705bc200259efaf0cc66058e8ed1d','[\"*\"]','2024-05-25 13:57:11',NULL,'2024-05-25 12:15:51','2024-05-25 13:57:11'),(14,'App\\Models\\User',4,'phone.9373609514','c83ccb972fa2fbb9d4b698ea03ea71c81b2046200be5b086ea6a0fec7f137498','[\"*\"]',NULL,NULL,'2024-05-25 16:15:03','2024-05-25 16:15:03'),(15,'App\\Models\\User',4,'phone.9373609514','cc081f9e18c149bf81e85b15e11d64700d08a8cbf0a63e15c44c21a8502bcc10','[\"*\"]','2024-05-26 20:01:13',NULL,'2024-05-25 18:03:25','2024-05-26 20:01:13'),(16,'App\\Models\\User',4,'phone.9373609514','01c4faf08fcbfbeb9abb113a4ef3c3bd265fe329ae2b235b21a8e739c4a371c6','[\"*\"]',NULL,NULL,'2024-05-26 20:22:23','2024-05-26 20:22:23'),(17,'App\\Models\\User',1,'phone.9128265825','4bc286097b325a14483966ba292da34ec6e758b6390bcc2cc1319dc79b0403a2','[\"*\"]',NULL,NULL,'2024-05-26 21:37:33','2024-05-26 21:37:33'),(18,'App\\Models\\User',5,'phone.9304788200','b463940effb810e943cd99365cf512aa0040fb2da1ccdfc09b870cca3e7b166f','[\"*\"]','2024-07-06 20:51:52',NULL,'2024-05-31 15:30:15','2024-07-06 20:51:52'),(19,'App\\Models\\User',5,'phone.9304788200','7d3ae43c69f7384da8a14a5535a3cadd01eb35ad98c7720c097f7915b8198e63','[\"*\"]',NULL,NULL,'2024-06-02 00:00:36','2024-06-02 00:00:36'),(20,'App\\Models\\User',5,'phone.9304788200','a20d667fc04ba5949e6cc6eef5634cacab50deabc92a0385e15cd6903092c1d7','[\"*\"]',NULL,NULL,'2024-06-02 15:29:12','2024-06-02 15:29:12'),(21,'App\\Models\\User',5,'phone.9304788200','f30b69a5c122b73452b0495f1402b9e793291b20aaaadc6baef14fc6698b18c7','[\"*\"]',NULL,NULL,'2024-06-02 16:01:48','2024-06-02 16:01:48'),(22,'App\\Models\\User',5,'phone.9304788200','02e5a317da0945912a7457a5f7484baa839a284b579e31e1857e66d444bd5f21','[\"*\"]',NULL,NULL,'2024-06-02 16:45:06','2024-06-02 16:45:06'),(23,'App\\Models\\User',5,'phone.9304788200','5bb0f66e0837614c8dda7afe2a14d059f26d44a72b1235724b7bbbe25fc409f6','[\"*\"]',NULL,NULL,'2024-06-03 08:29:34','2024-06-03 08:29:34'),(24,'App\\Models\\User',5,'phone.9304788200','613228fec011d9584a779097ba313b242011f7fbb3f8162af30602ee76d5dd72','[\"*\"]',NULL,NULL,'2024-06-03 08:37:43','2024-06-03 08:37:43'),(25,'App\\Models\\User',5,'phone.9304788200','462f2c212173397095994fe91ed7f046a311a38d5924de280a5784b0b8131673','[\"*\"]',NULL,NULL,'2024-06-03 08:41:06','2024-06-03 08:41:06'),(26,'App\\Models\\User',5,'phone.9304788200','938b0c23a34c318b01654d2b3a9403259827d5642e8389cd1de8705875b617d0','[\"*\"]',NULL,NULL,'2024-06-03 08:43:09','2024-06-03 08:43:09'),(27,'App\\Models\\User',5,'phone.9304788200','e6e99eaf897af983709d3d5dfb635147e0f57566b6a1a3e02bab686039fa46f5','[\"*\"]',NULL,NULL,'2024-06-03 08:52:49','2024-06-03 08:52:49'),(28,'App\\Models\\User',5,'phone.9304788200','654c1eb58165234276810a9afc5c49171813277b77041c5050e4b807452d2e23','[\"*\"]',NULL,NULL,'2024-06-03 08:54:19','2024-06-03 08:54:19'),(29,'App\\Models\\User',5,'phone.9304788200','f34539944893c3da8a5aca649f9047838f531751031299df05389fb28be2a708','[\"*\"]','2024-06-05 03:43:22',NULL,'2024-06-05 03:43:10','2024-06-05 03:43:22'),(30,'App\\Models\\User',5,'phone.9304788200','ea7ba83660f6ed303c54d2a862be5a26f7825a16a77e42dbb1305c2aba9494f6','[\"*\"]','2024-06-05 15:26:44',NULL,'2024-06-05 15:18:24','2024-06-05 15:26:44'),(31,'App\\Models\\User',5,'phone.9304788200','05a119f3892a576d16ac8def59a176b69d7133cef3ddc0c6cfa6c4b63ba5bc91','[\"*\"]','2024-06-05 15:28:43',NULL,'2024-06-05 15:28:37','2024-06-05 15:28:43'),(32,'App\\Models\\User',5,'phone.9304788200','5ef96679f2f31480c17d0cc55564983be254871183962a91501983d83d41e6ea','[\"*\"]','2024-06-05 15:43:02',NULL,'2024-06-05 15:39:56','2024-06-05 15:43:02'),(33,'App\\Models\\User',5,'phone.9304788200','b669b29cbe16c2facda0200399be7d2da92d052d9185b567f2e7365d006d5084','[\"*\"]','2024-06-05 16:18:40',NULL,'2024-06-05 15:55:13','2024-06-05 16:18:40'),(34,'App\\Models\\User',5,'phone.9304788200','efb0386d206d7a24a3a6fc690577c7695a305f033cdd87644b7faddd7810b4d6','[\"*\"]','2024-06-05 17:06:25',NULL,'2024-06-05 16:21:29','2024-06-05 17:06:25'),(35,'App\\Models\\User',5,'phone.9304788200','4c36a3989d032de2b2018a225ea381c689decacbf9629f034c97633fe51cd848','[\"*\"]',NULL,NULL,'2024-06-05 17:08:03','2024-06-05 17:08:03'),(36,'App\\Models\\User',5,'phone.9304788200','281ba349abbf4d30b5db99922d34ae3e28d838541701007f9987d56b2cec484c','[\"*\"]',NULL,NULL,'2024-06-08 14:58:49','2024-06-08 14:58:49'),(37,'App\\Models\\User',5,'phone.9304788200','8ca202c097df9eeb645fc6f681753bf23e97e250f3574320702b6d39da06ecba','[\"*\"]',NULL,NULL,'2024-06-08 15:00:19','2024-06-08 15:00:19'),(38,'App\\Models\\User',5,'phone.9304788200','fddeaff71e553cb20c93975f969c199a52ac0c9bbca2df698d24ee09b3f28f34','[\"*\"]',NULL,NULL,'2024-06-08 15:17:46','2024-06-08 15:17:46'),(39,'App\\Models\\User',5,'phone.9304788200','649ab5397c261a91897e4d7f95dd60c9ab676b264c9c58ecb9c885f7733a0bd9','[\"*\"]',NULL,NULL,'2024-06-08 15:35:57','2024-06-08 15:35:57'),(40,'App\\Models\\User',5,'phone.9304788200','6d1185a281a40b7451aa104aaed73ee68770f6163a90dc6f020cc85d065a3f1f','[\"*\"]',NULL,NULL,'2024-06-08 18:53:08','2024-06-08 18:53:08'),(41,'App\\Models\\User',5,'phone.9304788200','8eff847be96aff09f9f28ba660492c078bb45d7a4cbdfc8315124ba369ec6956','[\"*\"]',NULL,NULL,'2024-06-08 18:55:30','2024-06-08 18:55:30'),(42,'App\\Models\\User',5,'phone.9304788200','702c686b4daaf868d48c3b8f4275173caaf1147523951a3e9fc894b16baa7d48','[\"*\"]',NULL,NULL,'2024-06-08 19:00:17','2024-06-08 19:00:17'),(43,'App\\Models\\User',5,'phone.9304788200','117ec5c0b45c210152d080626418d43fbe231c0bfd660ff2fcd5275ed8ae91bb','[\"*\"]',NULL,NULL,'2024-06-08 19:01:11','2024-06-08 19:01:11'),(44,'App\\Models\\User',5,'phone.9304788200','eb781606b12a97fa20f44af9636739f7ce58171dcc41580ae8a2a6a4712b192d','[\"*\"]',NULL,NULL,'2024-06-08 23:04:45','2024-06-08 23:04:45'),(45,'App\\Models\\User',5,'phone.9304788200','b57ac6805d1e71eee34258284f77e02ce7a4748de5fb7c0ae01b63aba0e3b1f1','[\"*\"]','2024-06-08 23:58:11',NULL,'2024-06-08 23:07:39','2024-06-08 23:58:11'),(46,'App\\Models\\User',5,'phone.9304788200','8a4ee2d0ca047a33705c859dd9fed7340051d20ac6007614ce34f464dcc40f37','[\"*\"]','2024-06-08 23:59:15',NULL,'2024-06-08 23:58:30','2024-06-08 23:59:15'),(47,'App\\Models\\User',5,'phone.9304788200','25af2cb8e630f8b796e164b4802b9569ff69729b12d7fef5de0eb7fbf6ca1133','[\"*\"]','2024-06-09 00:00:24',NULL,'2024-06-08 23:59:18','2024-06-09 00:00:24'),(48,'App\\Models\\User',5,'phone.9304788200','55cfdffe45d9388fb6ed27f24a4834760d9634ec2d1c46fd3e56cd65839770c2','[\"*\"]','2024-06-09 00:01:29',NULL,'2024-06-09 00:00:29','2024-06-09 00:01:29'),(49,'App\\Models\\User',5,'phone.9304788200','ba7247abf4ddcf11b13d958fb0e010b5ed642280b9ff266c25ee85620e77df81','[\"*\"]','2024-06-09 00:02:05',NULL,'2024-06-09 00:01:31','2024-06-09 00:02:05'),(50,'App\\Models\\User',5,'phone.9304788200','d5bc6af6ce61041528e6696e7dc7508c3c139d5e853c4d86a9a1e55b2a2354ed','[\"*\"]','2024-06-09 00:02:58',NULL,'2024-06-09 00:02:09','2024-06-09 00:02:58'),(51,'App\\Models\\User',5,'phone.9304788200','ebc4e5240d9a9a93aa3036388e33d50291d90dbd9e12313b57beb5c4a3ff4eb6','[\"*\"]','2024-06-09 00:03:16',NULL,'2024-06-09 00:03:02','2024-06-09 00:03:16'),(52,'App\\Models\\User',5,'phone.9304788200','48a9bef734a826ce80c43263ad2c3ca4381fd2c1508364e75ff123657d8a52f1','[\"*\"]','2024-06-09 00:34:06',NULL,'2024-06-09 00:03:18','2024-06-09 00:34:06'),(53,'App\\Models\\User',5,'phone.9304788200','95080eb3bdc6ec8cfa62d11afb64ac6bc79382f78f322106da4238980eb56b80','[\"*\"]','2024-06-11 14:41:19',NULL,'2024-06-09 09:31:00','2024-06-11 14:41:19'),(54,'App\\Models\\User',5,'phone.9304788200','63d37a99a28767d86d762c1412e18976827049fc699360326c3ee55cb50b8d37','[\"*\"]',NULL,NULL,'2024-06-11 11:05:12','2024-06-11 11:05:12'),(55,'App\\Models\\User',5,'phone.9304788200','3cf7adc2b19e82bfd3a4e59117fcc20fbaa5f79c733e7c93c74d33c879868f20','[\"*\"]','2024-06-11 15:19:28',NULL,'2024-06-11 11:05:16','2024-06-11 15:19:28'),(56,'App\\Models\\User',5,'phone.9304788200','dd61785209e0e4ec4bfa71bc5f7946a602ba70582246482fbcdc6a235054256e','[\"*\"]','2024-06-11 14:43:46',NULL,'2024-06-11 14:43:00','2024-06-11 14:43:46'),(57,'App\\Models\\User',5,'phone.9304788200','70018657a879ee9218bf09a461c2a65cc0acbfb287432e2644b75bd6aa798b3f','[\"*\"]',NULL,NULL,'2024-06-11 14:43:50','2024-06-11 14:43:50'),(58,'App\\Models\\User',5,'phone.9304788200','859872bf9b6432ff6b0730e008f34c12d6f5ea649f0b256f2f060d17ad15d605','[\"*\"]','2024-06-11 14:56:39',NULL,'2024-06-11 14:48:04','2024-06-11 14:56:39'),(59,'App\\Models\\User',5,'phone.9304788200','7ece3b0e28db6cc36c9be302c8f55c4b6936036d55b1b8e2b898e1513aa23fea','[\"*\"]','2024-06-11 15:02:29',NULL,'2024-06-11 14:59:28','2024-06-11 15:02:29'),(60,'App\\Models\\User',5,'phone.9304788200','76cd422c6c4eedf93fdae462381e63b46b980700ab517337f2f39130e210103c','[\"*\"]','2024-06-11 16:58:48',NULL,'2024-06-11 15:16:49','2024-06-11 16:58:48'),(61,'App\\Models\\User',5,'phone.9304788200','4a7f3c05a7f29076e59d5e946864df07cfe18f502866fc43d329f180d1f9375f','[\"*\"]','2024-06-11 15:21:55',NULL,'2024-06-11 15:20:55','2024-06-11 15:21:55'),(62,'App\\Models\\User',5,'phone.9304788200','08e8a3f49c845eb9ed5da6e8a4181a31221899aee49c0033939c404297acd4d4','[\"*\"]',NULL,NULL,'2024-06-11 15:22:09','2024-06-11 15:22:09'),(63,'App\\Models\\User',5,'phone.9304788200','1d3727a0f8abfa5608ae850685ead37f33da350d12eff6ea116ae599c53a649f','[\"*\"]','2024-06-23 20:50:40',NULL,'2024-06-11 15:22:15','2024-06-23 20:50:40'),(64,'App\\Models\\User',5,'phone.9304788200','7ae937198e5a8dc67ab2b580952cf83f7ccafd9b27c5b725b876a9e1e950c776','[\"*\"]','2024-06-11 17:02:16',NULL,'2024-06-11 17:02:12','2024-06-11 17:02:16'),(65,'App\\Models\\User',5,'phone.9304788200','60be144523e7340ca0da1dc4a453e6ab88399696907207039eb794054411a531','[\"*\"]','2024-06-21 19:16:25',NULL,'2024-06-11 17:02:22','2024-06-21 19:16:25'),(66,'App\\Models\\User',5,'phone.9304788200','57e90c9d72dcae0f1366b90d6b21063f931cdb5ac66650b0d9e5e30f22e2946d','[\"*\"]','2024-06-12 15:16:47',NULL,'2024-06-11 18:04:08','2024-06-12 15:16:47'),(67,'App\\Models\\User',5,'phone.9304788200','909975b83c6f233436cc31de1695fcf897db56f175f371b11483eb406d4473b2','[\"*\"]','2024-06-12 17:27:21',NULL,'2024-06-12 16:55:13','2024-06-12 17:27:21'),(68,'App\\Models\\User',5,'phone.9304788200','e30c2a2489e244543226a5c8be1b7d1bdf81ddbecc84c6b0d3203f7f1bed5486','[\"*\"]','2024-06-12 20:18:37',NULL,'2024-06-12 17:56:01','2024-06-12 20:18:37'),(69,'App\\Models\\User',5,'phone.9304788200','068712c9cc07e083bfad2895d9bebb5269f436f6df269becde1bfd432d412f1e','[\"*\"]','2024-06-13 12:42:15',NULL,'2024-06-13 11:48:30','2024-06-13 12:42:15'),(70,'App\\Models\\User',5,'phone.9304788200','dc16f390cfe8b37ad58758b346e2a558d02e275f6f742375dcce37513c7137d8','[\"*\"]','2024-06-13 18:06:46',NULL,'2024-06-13 18:06:14','2024-06-13 18:06:46'),(71,'App\\Models\\User',5,'phone.9304788200','adaf7573ce0cff5aa5c113e7062c6aef4d869f8b9b9ff55558aafceb6cc3f43c','[\"*\"]',NULL,NULL,'2024-06-13 18:06:48','2024-06-13 18:06:48'),(72,'App\\Models\\User',5,'phone.9304788200','6fb3ae89209da84c91a976670dbb96dd24fd48050b20fbdc36eedb915730c9fc','[\"*\"]','2024-06-13 18:20:55',NULL,'2024-06-13 18:18:49','2024-06-13 18:20:55'),(73,'App\\Models\\User',5,'phone.9304788200','a6eda07a0d103923ed6143a12afae2924a01952cb21c4ce5e64942f9c6b514b4','[\"*\"]','2024-06-13 18:22:15',NULL,'2024-06-13 18:20:54','2024-06-13 18:22:15'),(74,'App\\Models\\User',5,'phone.9304788200','171f9b34f3e26da7b3a78e652b4481838f8291b34c19d0cd5ee878b9ea90c2d6','[\"*\"]','2024-06-17 08:36:20',NULL,'2024-06-13 18:47:43','2024-06-17 08:36:20'),(75,'App\\Models\\User',5,'phone.9304788200','16494a46d327c8ab300e2bd84578604f2e7ed8ed7855262025d8d1df5e4e1cd0','[\"*\"]','2024-06-17 10:12:17',NULL,'2024-06-17 08:36:53','2024-06-17 10:12:17'),(76,'App\\Models\\User',5,'phone.9304788200','3be8a415a1b6848328157d8999022a44f435dd5cfc45e7cb0d48ba416f07abe3','[\"*\"]','2024-06-23 19:29:34',NULL,'2024-06-17 10:12:53','2024-06-23 19:29:34'),(77,'App\\Models\\User',2,'phone.9120396189','32eac552b64839cfbb3e60a2c431823716cb19fdbe1f45227465a53e9d403eca','[\"*\"]',NULL,NULL,'2024-06-21 17:35:26','2024-06-21 17:35:26'),(78,'App\\Models\\User',2,'phone.9120396189','faf7c0d4b7383f32bdeb044d7b76876155c8f95cf7922354f6f49ed4d1cc5c4c','[\"*\"]',NULL,NULL,'2024-06-21 17:36:43','2024-06-21 17:36:43'),(79,'App\\Models\\User',2,'phone.9120396189','4d7ee71e32371b50f9d4e4b2e93468cf5c2604be196cd31a941505c2405073b1','[\"*\"]','2024-06-21 17:44:37',NULL,'2024-06-21 17:40:17','2024-06-21 17:44:37'),(80,'App\\Models\\User',2,'phone.9120396189','68ec84c2b464ded119aea61ee1034a2008af7e76e6521f4a901a2aa0caf8bf2e','[\"*\"]','2024-06-21 17:48:16',NULL,'2024-06-21 17:45:56','2024-06-21 17:48:16'),(81,'App\\Models\\User',2,'phone.9120396189','2f4252544927adde4f216a30f8acbf3acd92604482b10daa656a4e6b704ea8d5','[\"*\"]','2024-06-23 22:55:03',NULL,'2024-06-21 17:50:00','2024-06-23 22:55:03'),(82,'App\\Models\\User',2,'phone.9120396189','9a5893b05eec002bd9c43a462abb875355557aadb2aa21beede8630716a6555d','[\"*\"]','2024-06-22 09:01:59',NULL,'2024-06-21 17:55:12','2024-06-22 09:01:59'),(83,'App\\Models\\User',2,'phone.9120396189','84ff678192ca3f28aada86ed1a14c0c54db4c1208bec64932f39fe10264cba62','[\"*\"]','2024-06-22 09:01:56',NULL,'2024-06-21 17:59:36','2024-06-22 09:01:56'),(84,'App\\Models\\User',2,'phone.9120396189','ed839119691735b38215ada8792abbf0c7d90f09f8c62720953cb9775a6b6f33','[\"*\"]','2024-06-22 09:02:01',NULL,'2024-06-21 18:02:51','2024-06-22 09:02:01'),(85,'App\\Models\\User',2,'phone.9120396189','aa1b8f4a738904f4e65bdf0aa7c8246ea90b445df0764f8d6bb61e9eb527c9a0','[\"*\"]','2024-06-22 09:02:02',NULL,'2024-06-21 18:05:38','2024-06-22 09:02:02'),(86,'App\\Models\\User',2,'phone.9120396189','ff2e77b13ee109dd33b96cb8a451a6ce7769354b7de814ff1f9ec09fe47a6aa3','[\"*\"]','2024-06-21 18:15:29',NULL,'2024-06-21 18:14:40','2024-06-21 18:15:29'),(87,'App\\Models\\User',2,'phone.9120396189','2638a91994f92855949609bc1bc716a5cf716c373bc2a7b099d31c41b568b26d','[\"*\"]','2024-06-21 21:22:19',NULL,'2024-06-21 18:40:17','2024-06-21 21:22:19'),(88,'App\\Models\\User',1,'phone.9128265825','8054c5a390b69b1141052d3749ba7fa83adbec877a51931e47e704aedbaf5f60','[\"*\"]',NULL,NULL,'2024-06-21 19:19:10','2024-06-21 19:19:10'),(89,'App\\Models\\User',1,'phone.9128265825','b0bb587a4dca9d645f5e6ca8d55d46576c22f9b50d28db293ae0bba0a5789bbc','[\"*\"]','2024-06-21 19:22:13',NULL,'2024-06-21 19:20:31','2024-06-21 19:22:13'),(90,'App\\Models\\User',2,'phone.9120396189','ce9fedfb5b27740a3630801967d759e638a3702b34e6a1c7a9f3aeb7a94e9a96','[\"*\"]','2024-07-05 20:01:03',NULL,'2024-06-21 19:28:21','2024-07-05 20:01:03'),(91,'App\\Models\\User',2,'phone.9120396189','83aa29d5bfaea4c678750e5ee0d0618200807658499128e8e284daeebb073016','[\"*\"]','2024-06-22 10:26:04',NULL,'2024-06-22 10:20:15','2024-06-22 10:26:04'),(92,'App\\Models\\User',2,'phone.9120396189','73570b9ec821ef3d70b00e4431c2947a4d1429bbe590856b175a503b9cacfa55','[\"*\"]','2024-06-22 10:33:34',NULL,'2024-06-22 10:31:24','2024-06-22 10:33:34'),(93,'App\\Models\\User',2,'phone.9120396189','76026a7d000ac2462bcce1cb618919b54d51ec041e49d619b362c7e08a40efca','[\"*\"]','2024-06-22 11:52:35',NULL,'2024-06-22 11:52:35','2024-06-22 11:52:35'),(94,'App\\Models\\User',2,'phone.9120396189','f92b188a080d2ae732578b212832611985accdb491c1836abbcf4c72325e34ee','[\"*\"]','2024-06-22 12:01:27',NULL,'2024-06-22 11:56:31','2024-06-22 12:01:27'),(95,'App\\Models\\User',2,'phone.9120396189','84a991f96e39db37889fe46f4d0bb0dc8c66ebdea586bc6befc07d972020d81a','[\"*\"]','2024-06-22 12:04:01',NULL,'2024-06-22 12:02:24','2024-06-22 12:04:01'),(96,'App\\Models\\User',2,'phone.9120396189','37d140668b6e5bad691eb34caf66bdbe743bf7611d196dd4ef29cc79de2e6905','[\"*\"]','2024-06-22 12:11:28',NULL,'2024-06-22 12:05:12','2024-06-22 12:11:28'),(97,'App\\Models\\User',2,'phone.9120396189','5d4696f6a030bfa42d1d6c2ff9b31c923344c26438b3748184e5ec18c39ef2db','[\"*\"]','2024-06-22 12:13:52',NULL,'2024-06-22 12:12:18','2024-06-22 12:13:52'),(98,'App\\Models\\User',2,'phone.9120396189','e4c80ed0fc522078558c4e1ed6e1fdd0f9bf8450496d7e7e7e4feba6ca3f9d05','[\"*\"]','2024-06-22 12:20:18',NULL,'2024-06-22 12:18:02','2024-06-22 12:20:18'),(99,'App\\Models\\User',2,'phone.9120396189','9f69ddc92c8a01b7009389871c7e2e5767b414951845badcfec4db60278c0c41','[\"*\"]','2024-06-22 12:22:50',NULL,'2024-06-22 12:21:22','2024-06-22 12:22:50'),(100,'App\\Models\\User',2,'phone.9120396189','7c92e7982acc743eefe5f0e5711c4a8c3950f90323a6afd705a2624e6f96cac3','[\"*\"]','2024-06-22 12:33:55',NULL,'2024-06-22 12:23:41','2024-06-22 12:33:55'),(101,'App\\Models\\User',2,'phone.9120396189','4f80709763d5fe472d06120b2798f78225fdf1851e6defd8572a4607cdd71481','[\"*\"]','2024-06-22 12:38:49',NULL,'2024-06-22 12:34:42','2024-06-22 12:38:49'),(102,'App\\Models\\User',6,'phone.9058323673','dae7c0f0d339a08f2bf5773814e09ef1b29425004c9963765882cbaa68cd3285','[\"*\"]','2024-06-22 17:53:57',NULL,'2024-06-22 17:52:56','2024-06-22 17:53:57'),(103,'App\\Models\\User',5,'phone.9304788200','ca76993754972baba0e60c5758b5a0dc904a1dab6b1d92e9e78035e2802508c1','[\"*\"]','2024-06-23 20:37:51',NULL,'2024-06-23 20:37:44','2024-06-23 20:37:51'),(104,'App\\Models\\User',5,'phone.9304788200','c62b0230ca874e349656cd204526f7f395fd12be625e0ead2fdd433d855e6edf','[\"*\"]','2024-06-23 21:07:08',NULL,'2024-06-23 20:39:38','2024-06-23 21:07:08'),(105,'App\\Models\\User',5,'phone.9304788200','5936cc87e7b96d8700161e61a1459ab0c2cb7ce0297f49c62186123965ce728d','[\"*\"]','2024-06-23 21:12:43',NULL,'2024-06-23 21:11:44','2024-06-23 21:12:43'),(106,'App\\Models\\User',5,'phone.9304788200','aa89c42d1ca000fc9afd8a804070dca873c720bb771bce6a192f12a53613e31d','[\"*\"]','2024-06-26 01:04:05',NULL,'2024-06-23 21:17:04','2024-06-26 01:04:05'),(107,'App\\Models\\User',2,'phone.9120396189','777a2b3caacca0cc7f0a2228bd19a489aa4303f129d4ef44081a8f1e6db97232','[\"*\"]','2024-06-23 22:44:56',NULL,'2024-06-23 22:42:10','2024-06-23 22:44:56'),(108,'App\\Models\\User',2,'phone.9120396189','4b8c5e9955e8ed297f078af1eda82f62b06b35471071a35b7715994cc001fd7b','[\"*\"]','2024-06-23 22:53:00',NULL,'2024-06-23 22:46:38','2024-06-23 22:53:00'),(109,'App\\Models\\User',2,'phone.9120396189','b677f8f6086884027888202fbd0adb7f571f14aa178ab64008c7a4fbbd6adcc7','[\"*\"]','2024-06-25 16:32:31',NULL,'2024-06-25 16:30:10','2024-06-25 16:32:31'),(110,'App\\Models\\User',2,'phone.9120396189','cc7778f9cba0d8a780d44377637885c1a0e497155da3a1417a9816b544cc9d0e','[\"*\"]','2024-07-06 21:15:20',NULL,'2024-07-06 21:05:18','2024-07-06 21:15:20'),(111,'App\\Models\\User',2,'phone.9120396189','9076c4d9d1b5015a3814ab0ad7c539f913829468a7a80ff4a94c7732dffccac1','[\"*\"]','2024-07-06 22:14:17',NULL,'2024-07-06 22:13:06','2024-07-06 22:14:17'),(112,'App\\Models\\User',2,'phone.9120396189','e2c0726dc0896600636aa1047d01a87be2f23a3b6d82876323a68e7be8e23586','[\"*\"]','2024-07-06 22:19:14',NULL,'2024-07-06 22:15:37','2024-07-06 22:19:14'),(113,'App\\Models\\User',2,'phone.9120396189','eed14982d8033f008df8b4ffd3ecb8a4cc6355aeaf41dd2b560f346e748dea90','[\"*\"]','2024-07-06 22:20:55',NULL,'2024-07-06 22:19:55','2024-07-06 22:20:55'),(114,'App\\Models\\User',1,'phone.9128265825','5f791c616e1cdd11e2ef1c6b0e0b0a93aabe79007bbb3f87d8febee9e1d7cadc','[\"*\"]','2024-07-07 11:33:15',NULL,'2024-07-07 10:10:48','2024-07-07 11:33:15'),(115,'App\\Models\\User',7,'phone.9991381260','3f275a6a3366d6c9594d24ee3d6d844ba30913f5489b9548f3dcdaca28302d6d','[\"*\"]','2024-07-07 10:19:53',NULL,'2024-07-07 10:15:12','2024-07-07 10:19:53'),(116,'App\\Models\\User',2,'phone.9120396189','a5fb7899b7f82730cabeb6fc7a746b1a501c77f3c8a11618881f0a680775cbfd','[\"*\"]','2024-07-07 14:20:54',NULL,'2024-07-07 10:38:36','2024-07-07 14:20:54');
/*!40000 ALTER TABLE `personal_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_has_permissions`
--

DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_has_permissions`
--

LOCK TABLES `role_has_permissions` WRITE;
/*!40000 ALTER TABLE `role_has_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `role_has_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'admin','api',NULL,NULL);
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
INSERT INTO `sessions` VALUES ('3Efnac3ZvbQSkaITqA3NDMKVWTj9UbLJobBqI1LO',NULL,'*************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTozOntzOjY6Il90b2tlbiI7czo0MDoiWTRQaXBMSnE4NHRwUzVuQm10dDhHUWs3SnZqdFN5cUNrQWswYzB6RiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9leGNoYW5naW0uYWxpanZoci5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',1720349863),('6LmJEeyT0qQFkiwEbgmrzuDHvCbPTdNWZtjRGcPu',NULL,'**************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTozOntzOjY6Il90b2tlbiI7czo0MDoiRGlScUJaMXpkbnk3NzZCbjkyWktqbDJ1SlpwODRNUEJzZnNHek9PSCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9leGNoYW5naW0uYWxpanZoci5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',1719176664),('H1eUqDsC8XvyKKJ5weO5WrxHgFqoAwRLYXbPUjJa',NULL,'************','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTozOntzOjY6Il90b2tlbiI7czo0MDoicmhRQjZDbGc5cmlFVUFvZUtNc2ptUUpuc2kyMXRaWHdoOVdBUklpSCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9leGNoYW5naW0uYWxpanZoci5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',1719175828),('n3vLwdrudgWSOEyKGagtlR72ZRTGdNHKunSuhOuu',NULL,'**************','Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36','YTozOntzOjY6Il90b2tlbiI7czo0MDoiSlJKY3lVMlpZZjQ1ZkVnWkFXa21FcUZKbnM2Q3FRdGF0TnljR2xSNCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9leGNoYW5naW0uYWxpanZoci5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',1719176828),('QfwTWGeYCGTbvMDac3VU3pXP5mbqGSA5DRccvSLx',NULL,'**************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTozOntzOjY6Il90b2tlbiI7czo0MDoiWnRFNHVFdkM0NTh5TmNtSGlzSEY1S0xUWm9nRnFyUGRtMmVsbWxhZiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9leGNoYW5naW0uYWxpanZoci5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',1719176698),('rn4iq4aHZxjmGe4TQfRykqut9mdaWG0ilm5zUruV',NULL,'127.0.0.1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTozOntzOjY6Il90b2tlbiI7czo0MDoiVXZ5aVNvNzdLQnlXbTJoZDdpczhQRGFzcFAwSkZUUDhVc2M3WkUyeSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=',1721824294),('sB0aXjntqt7JGDFD77niaOL9dFaMYQyiaHJ40Cl3',NULL,'**************','Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36','YTozOntzOjY6Il90b2tlbiI7czo0MDoiMWJWMzBsNDk4NmI1cUtFWm9ERGZMbHIyd2tDeWxPRksyUFQ3WlhpMiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9leGNoYW5naW0uYWxpanZoci5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',1719176820),('uuJFrR7OzHfVTA6PxnMOVYN6wpSy6WMy332NRQa2',NULL,'**************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTozOntzOjY6Il90b2tlbiI7czo0MDoiUmpsdGJFZjhaZVdUc2owMzBFUkFBelpYc25MWDR3V0ZLcVJ2VGNobCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9leGNoYW5naW0uYWxpanZoci5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',1719176670),('wsbKG4tMlVqaG5OQKKTOL0bVUe9CLWnOq3di8UAF',NULL,'162.158.87.177','Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36','YTozOntzOjY6Il90b2tlbiI7czo0MDoiZWJNMDl4SzNVVEpLQXF5UTlFM01pY3dEM3pGR3A3d3Q2NlBMZks4RCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9leGNoYW5naW0uYWxpanZoci5jb20iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19',1719176802);
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transactions`
--

DROP TABLE IF EXISTS `transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transactions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('deposit','withdraw','gift','transfer','buy','sell') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'deposit',
  `amount` decimal(36,18) NOT NULL,
  `wallet_id` bigint unsigned NOT NULL,
  `currency_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `registrar` bigint unsigned DEFAULT NULL,
  `status` enum('pending','waiting','approved','declined','done') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `details` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transactions_wallet_id_foreign` (`wallet_id`),
  KEY `transactions_currency_id_foreign` (`currency_id`),
  KEY `transactions_user_id_foreign` (`user_id`),
  KEY `transactions_registrar_foreign` (`registrar`),
  CONSTRAINT `transactions_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `transactions_registrar_foreign` FOREIGN KEY (`registrar`) REFERENCES `users` (`id`),
  CONSTRAINT `transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `transactions_wallet_id_foreign` FOREIGN KEY (`wallet_id`) REFERENCES `wallets` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transactions`
--

LOCK TABLES `transactions` WRITE;
/*!40000 ALTER TABLE `transactions` DISABLE KEYS */;
INSERT INTO `transactions` VALUES (1,'buy',575000.000000000000000000,1,1,1,1,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U18653804\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-05-18 22:07:47','2024-05-18 22:07:47',NULL),(2,'buy',575000.000000000000000000,1,1,1,1,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U18653804\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-05-18 22:17:51','2024-05-18 22:17:51',NULL),(3,'buy',575000.000000000000000000,1,1,1,1,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U48526010\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-05-19 08:42:33','2024-05-19 08:42:33',NULL),(4,'sell',0.000000000000000000,1,1,1,1,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.00\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-19 08:43:22','2024-05-19 08:43:22',NULL),(5,'sell',0.000000000000000000,5,1,2,2,'done','','{\"ERROR\": \"Invalid ev_number\"}','2024-05-19 08:45:03','2024-05-19 08:45:03',NULL),(6,'sell',0.000000000000000000,5,1,2,2,'done','','{\"ERROR\": \"Invalid ev_number\"}','2024-05-19 08:53:10','2024-05-19 08:53:10',NULL),(7,'sell',0.000000000000000000,5,1,2,2,'done','','{\"ERROR\": \"Invalid ev_number\"}','2024-05-21 13:01:57','2024-05-21 13:01:57',NULL),(8,'sell',0.000000000000000000,5,1,2,2,'done','','{\"ERROR\": \"Invalid ev_number\"}','2024-05-21 13:04:48','2024-05-21 13:04:48',NULL),(9,'sell',0.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.00\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-21 13:17:21','2024-05-21 13:17:21',NULL),(10,'sell',575000.000000000000000000,1,1,1,1,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.00\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-21 13:28:18','2024-05-21 13:28:18',NULL),(11,'sell',575000.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.00\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-21 13:29:20','2024-05-21 13:29:20',NULL),(12,'buy',600000.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U48526010\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-05-21 16:48:44','2024-05-21 16:48:44',NULL),(13,'buy',720000.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U48526010\", \"PAYMENT_AMOUNT\": \"1.2\", \"VOUCHER_AMOUNT\": \"1.20\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-05-21 17:50:00','2024-05-21 17:50:00',NULL),(14,'sell',690000.000000000000000000,5,1,2,2,'done','','{\"VOUCHER_NUM\": \"**********\", \"Payee_Account\": \"U48526010\", \"VOUCHER_AMOUNT\": \"1.2\", \"PAYMENT_BATCH_NUM\": \"*********\", \"VOUCHER_AMOUNT_CURRENCY\": \"1\"}','2024-05-21 18:13:23','2024-05-21 18:13:23',NULL),(15,'deposit',100000.000000000000000000,8,2,5,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-21 13:58:14','2024-06-21 13:58:14',NULL),(16,'deposit',100000.000000000000000000,8,2,5,NULL,'declined','','{\"transaction_id\": \"**********\"}','2024-06-21 13:59:20','2024-06-21 15:32:30',NULL),(17,'deposit',10000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-21 14:13:12','2024-06-21 14:13:12',NULL),(18,'deposit',10000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-21 14:16:15','2024-06-21 14:16:15',NULL),(19,'deposit',10000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-21 14:44:53','2024-06-21 14:44:53',NULL),(20,'deposit',10000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3647201977\"}','2024-06-21 16:38:43','2024-06-21 16:38:43',NULL),(21,'deposit',100000.000000000000000000,8,2,5,NULL,'waiting','','{\"transaction_id\": \"3647207719\"}','2024-06-21 16:43:10','2024-06-21 16:43:10',NULL),(22,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3647209430\"}','2024-06-21 16:44:35','2024-06-21 16:44:35',NULL),(23,'deposit',100000.000000000000000000,8,2,5,NULL,'waiting','','{\"transaction_id\": \"3647210399\"}','2024-06-21 16:45:20','2024-06-21 16:45:20',NULL),(24,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3649764821\"}','2024-06-23 15:08:48','2024-06-23 15:08:48',NULL),(25,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3649770143\"}','2024-06-23 15:13:26','2024-06-23 15:13:27',NULL),(26,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','[]','2024-06-23 15:16:20','2024-06-23 15:16:20',NULL),(27,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3649774332\"}','2024-06-23 15:17:08','2024-06-23 15:17:08',NULL),(28,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3649784249\"}','2024-06-23 15:26:16','2024-06-23 15:26:17',NULL),(29,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650287333\"}','2024-06-23 22:11:01','2024-06-23 22:11:02',NULL),(30,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650292009\"}','2024-06-23 22:18:27','2024-06-23 22:18:27',NULL),(31,'deposit',10050.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650297592\"}','2024-06-23 22:28:11','2024-06-23 22:28:11',NULL),(32,'deposit',10050.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650297943\"}','2024-06-23 22:28:36','2024-06-23 22:28:37',NULL),(33,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650303903\"}','2024-06-23 22:39:35','2024-06-23 22:39:35',NULL),(34,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650305594\"}','2024-06-23 22:42:21','2024-06-23 22:42:22',NULL),(35,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"3650307697\"}','2024-06-23 22:46:51','2024-06-23 22:46:51',NULL),(36,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-24 09:43:11','2024-06-24 09:43:11',NULL),(37,'deposit',100000.000000000000000000,5,3,2,NULL,'declined','','{\"transaction_id\": \"**********\"}','2024-06-24 21:24:06','2024-06-24 21:24:38',NULL),(38,'deposit',100000.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-06-24 21:24:54','2024-06-24 21:24:55',NULL),(39,'buy',600000.000000000000000000,5,3,2,NULL,'done','','{\"VOUCHER_NUM\": \"**********\", \"VOUCHER_CODE\": \"****************\", \"Payer_Account\": \"U48526010\", \"PAYMENT_AMOUNT\": \"1.00\", \"VOUCHER_AMOUNT\": \"1\", \"PAYMENT_BATCH_NUM\": \"*********\"}','2024-07-06 21:06:19','2024-07-06 21:06:19',NULL),(40,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-07-06 21:14:55','2024-07-06 21:14:55',NULL),(41,'deposit',100500.000000000000000000,5,3,2,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-07-06 21:15:05','2024-07-06 21:15:06',NULL),(42,'deposit',150750.000000000000000000,1,3,1,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-07-07 10:55:23','2024-07-07 10:55:24',NULL),(43,'deposit',150750.000000000000000000,1,3,1,NULL,'waiting','','{\"transaction_id\": \"**********\"}','2024-07-07 10:57:00','2024-07-07 10:57:01',NULL);
/*!40000 ALTER TABLE `transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `phone` bigint DEFAULT NULL,
  `phone_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `firstname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `lastname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `national_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` enum('male','female','undefined') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'undefined',
  `options` json DEFAULT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `level` int NOT NULL DEFAULT '0',
  `status` enum('pending','approved','rejected') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  UNIQUE KEY `users_phone_unique` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,NULL,NULL,9128265825,'2024-07-07 10:10:48','$2y$12$rciVoHEa38RGj90PrWYc8.kK0DdJhTDiA6f8xpYuokpBTRIOkfzI.','علی','جواهری','0016543610','male','{}',NULL,'2024-05-11 21:49:48','2024-07-07 10:10:48',0,'approved'),(2,NULL,NULL,9120396189,'2024-07-07 10:38:36','$2y$12$rciVoHEa38RGj90PrWYc8.kK0DdJhTDiA6f8xpYuokpBTRIOkfzI.','محمد','محمدی','3980420256','male','{}',NULL,'2024-05-11 21:55:02','2024-07-07 10:38:36',0,'approved'),(3,NULL,NULL,9220877254,NULL,NULL,NULL,NULL,NULL,'undefined','{}',NULL,'2024-05-22 16:26:38','2024-05-22 16:26:38',0,'approved'),(4,NULL,NULL,9373609514,NULL,'$2y$12$rciVoHEa38RGj90PrWYc8.kK0DdJhTDiA6f8xpYuokpBTRIOkfzI.',NULL,NULL,NULL,'male','{}',NULL,'2024-05-25 16:15:03','2024-05-25 16:15:03',0,'approved'),(5,NULL,NULL,9304788200,'2024-05-31 15:30:15','$2y$12$rciVoHEa38RGj90PrWYc8.kK0DdJhTDiA6f8xpYuokpBTRIOkfzI.',NULL,NULL,NULL,'undefined','{}',NULL,'2024-05-31 15:30:15','2024-05-31 15:30:15',0,'pending'),(6,NULL,NULL,9058323673,'2024-06-22 17:52:56',NULL,NULL,NULL,NULL,'undefined','{}',NULL,'2024-06-22 17:52:56','2024-06-22 17:52:56',0,'pending'),(7,NULL,NULL,9991381260,'2024-07-07 10:15:12',NULL,NULL,NULL,NULL,'undefined','{}',NULL,'2024-07-07 10:15:12','2024-07-07 10:15:12',0,'pending');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wallets`
--

DROP TABLE IF EXISTS `wallets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wallets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `balance` decimal(36,18) NOT NULL DEFAULT '0.000000000000000000',
  `currency_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `primary` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `wallets_user_id_currency_id_unique` (`user_id`,`currency_id`),
  KEY `wallets_currency_id_foreign` (`currency_id`),
  CONSTRAINT `wallets_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `wallets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wallets`
--

LOCK TABLES `wallets` WRITE;
/*!40000 ALTER TABLE `wallets` DISABLE KEYS */;
INSERT INTO `wallets` VALUES (1,380000.000000000000000000,3,1,1,NULL,'2024-05-21 17:50:00'),(5,10770000.000000000000000000,3,2,1,'2024-05-19 08:26:54','2024-07-06 21:06:18'),(6,0.000000000000000000,2,3,1,'2024-05-22 16:26:38','2024-05-22 16:26:38'),(7,0.000000000000000000,2,4,1,'2024-05-25 16:15:03','2024-05-25 16:15:03'),(8,0.000000000000000000,2,5,1,'2024-05-31 15:30:15','2024-05-31 15:30:15'),(9,0.000000000000000000,2,6,1,'2024-06-22 17:52:56','2024-06-22 17:52:56'),(10,0.000000000000000000,2,7,1,'2024-07-07 10:15:12','2024-07-07 10:15:12');
/*!40000 ALTER TABLE `wallets` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-07-24 16:24:45
