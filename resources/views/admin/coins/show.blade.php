@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">جزئیات ارز {{ $coin->name }}</h1>
                <div>
                    <a href="{{ route('admin.coins.edit', $coin->id) }}" class="btn btn-info">
                        <i class="fas fa-edit"></i> ویرایش
                    </a>
                    <a href="{{ route('admin.coins.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> بازگشت
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">اطلاعات اصلی</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <tr>
                            <th>نام</th>
                            <td>{{ $coin->name }}</td>
                        </tr>
                        <tr>
                            <th>نوع ارز</th>
                            <td>{{ $coin->coin_type }}</td>
                        </tr>
                        <tr>
                            <th>شبکه</th>
                            <td>{{ $coin->network }}</td>
                        </tr>
                        <tr>
                            <th>تعداد اعشار</th>
                            <td>{{ $coin->decimal }}</td>
                        </tr>
                        <tr>
                            <th>وضعیت</th>
                            <td>
                                <span class="badge bg-{{ $coin->status ? 'success' : 'danger' }}">
                                    {{ $coin->status ? 'فعال' : 'غیرفعال' }}
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تنظیمات معاملات</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <tr>
                            <th>برداشت</th>
                            <td>
                                <i class="fas fa-{{ $coin->is_withdrawal ? 'check text-success' : 'times text-danger' }}"></i>
                            </td>
                        </tr>
                        <tr>
                            <th>واریز</th>
                            <td>
                                <i class="fas fa-{{ $coin->is_deposit ? 'check text-success' : 'times text-danger' }}"></i>
                            </td>
                        </tr>
                        <tr>
                            <th>خرید</th>
                            <td>
                                <i class="fas fa-{{ $coin->is_buy ? 'check text-success' : 'times text-danger' }}"></i>
                            </td>
                        </tr>
                        <tr>
                            <th>فروش</th>
                            <td>
                                <i class="fas fa-{{ $coin->is_sell ? 'check text-success' : 'times text-danger' }}"></i>
                            </td>
                        </tr>
                        <tr>
                            <th>کارمزد برداشت</th>
                            <td>{{ $coin->withdrawal_fees }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection