@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">{{ isset($coin) ? 'ویرایش ارز' : 'افزودن ارز جدید' }}</h1>
                <a href="{{ route('admin.coins.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> بازگشت
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
        @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="card">
                <div class="card-body">
                    <form action="{{ isset($coin) ? route('admin.coins.update', $coin->id) : route('admin.coins.store') }}" 
                          method="POST" 
                          enctype="multipart/form-data">
                        @csrf
                        @if(isset($coin))
                            @method('PUT')
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نام</label>
                                    <input type="text" 
                                           name="name" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           value="{{ old('name', $coin->name ?? '') }}" 
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نماد ارز (Coin Type)</label>
                                    <input type="text" class="form-control" name="coin_type" @if(isset($coin))value="{{$coin->coin_type}}" @else value="{{old('coin_type')}}" @endif>

                                    @error('coin_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">مثال: BTC, ETH, USDT</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">شبکه</label>
                                    <input type="text" 
                                           name="network" 
                                           class="form-control @error('network') is-invalid @enderror" 
                                           value="{{ old('network', $coin->network ?? '') }}" 
                                           required>
                                    @error('network')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تعداد اعشار</label>
                                    <input type="number" 
                                           name="decimal" 
                                           class="form-control @error('decimal') is-invalid @enderror" 
                                           value="{{ old('decimal', $coin->decimal ?? '') }}" 
                                           required>
                                    @error('decimal')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">آیکون</label>
                                    <input type="file" 
                                           name="coin_icon" 
                                           class="form-control @error('coin_icon') is-invalid @enderror">
                                    @error('coin_icon')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">کارمزد برداشت</label>
                                    <input type="number" 
                                           step="0.00000001" 
                                           name="withdrawal_fees" 
                                           class="form-control @error('withdrawal_fees') is-invalid @enderror" 
                                           value="{{ old('withdrawal_fees', $coin->withdrawal_fees ?? '') }}" 
                                           required>
                                    @error('withdrawal_fees')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input type="checkbox" 
                                                   name="status" 
                                                   class="form-check-input" 
                                                   value="1" 
                                                   {{ (isset($coin) && $coin->status == 1) ? 'checked' : '' }}>
                                            <label class="form-check-label">فعال</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input type="checkbox" 
                                                   name="is_withdrawal" 
                                                   class="form-check-input" 
                                                   value="1" 
                                                   {{ (isset($coin) && $coin->is_withdrawal == 1) ? 'checked' : '' }}>
                                            <label class="form-check-label">برداشت</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input type="checkbox" 
                                                   name="is_deposit" 
                                                   class="form-check-input" 
                                                   value="1" 
                                                   {{ (isset($coin) && $coin->is_deposit == 1) ? 'checked' : '' }}>
                                            <label class="form-check-label">واریز</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input type="checkbox" 
                                                   name="is_buy" 
                                                   class="form-check-input" 
                                                   value="1" 
                                                   {{ (isset($coin) && $coin->is_buy == 1) ? 'checked' : '' }}>
                                            <label class="form-check-label">خرید</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check mb-3">
                                            <input type="checkbox" 
                                                   name="is_sell" 
                                                   class="form-check-input" 
                                                   value="1" 
                                                   {{ (isset($coin) && $coin->is_sell == 1) ? 'checked' : '' }}>
                                            <label class="form-check-label">فروش</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {{ isset($coin) ? 'بروزرسانی' : 'ذخیره' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
