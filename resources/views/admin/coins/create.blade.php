@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">افزودن ارز جدید</h1>
                <a href="{{ route('admin.coins.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> بازگشت
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            @if(session('success'))
                <div class="alert alert-success">{{ session('success') }}</div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger">{{ session('error') }}</div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="card">
                <div class="card-body">
                    <form action="{{ route('admin.coins.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نوع ارز دیجیتال</label>
                                    <select name="currency_type" id="currency_type" class="form-control @error('currency_type') is-invalid @enderror" required>
                                        <option value="">انتخاب کنید</option>
                                        <option value="1" {{ old('currency_type') == '1' ? 'selected' : '' }}>اصلی</option>
                                        <option value="2" {{ old('currency_type') == '2' ? 'selected' : '' }}>توکن</option>
                                    </select>
                                    @error('currency_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نام کامل ارز</label>
                                    <input type="text" name="name" class="form-control @error('name') is-invalid @enderror" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">نماد ارز (Coin Type)</label>
                                    <div id="coin_type_input">
                                        <input type="text" id="coin_type_input_" name="coin_type" class="form-control @error('coin_type') is-invalid @enderror" value="{{ old('coin_type') }}">
                                    </div>
                                    <div id="coin_type_select" class="d-none">
                                        <select id="coin_type_select_" class="form-control @error('coin_type') is-invalid @enderror">
                                            <option value="">انتخاب کنید</option>
                                            @foreach ($currencies ?? [] as $currency)
                                                <option value="{{ $currency->code }}" data-price="{{ $currency->rate }}">{{ $currency->code }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('coin_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">مثال: BTC, ETH, USDT</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تعداد اعشار</label>
                                    <input type="number" name="decimal" class="form-control @error('decimal') is-invalid @enderror" value="{{ old('decimal') }}">
                                    @error('decimal')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6" id="coin_rate_api">
                                <div class="mb-3">
                                    <label class="form-label">دریافت قیمت از API؟</label>
                                    <select name="get_price_api" class="form-control">
                                        <option value="1">بله</option>
                                        <option value="2">خیر</option>
                                    </select>
                                    <small class="text-warning">اگر خیر، لطفICollectionView قیمت ارز را وارد کنید</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">قیمت ارز (به دلار)</label>
                                    <div class="input-group">
                                        <input type="text" name="coin_price" class="form-control @error('coin_price') is-invalid @enderror" value="{{ old('coin_price') }}">
                                        <div class="input-group-append">
                                            <span class="input-group-text">USD</span>
                                        </div>
                                    </div>
                                    @error('coin_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">قیمت ارز به دلار. این مقدار به طور منظم توسط API بروزرسانی می‌شود</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                ذخیره
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
"use strict";
let currency = 1;

function add_coin_ui_change() {
    let coin_type_input = document.getElementById("coin_type_input");
    let coin_type_select = document.getElementById("coin_type_select");

    if (currency == 1) {
        $("#coin_type_input_").attr("name", "coin_type");
        $("#coin_type_select_").attr("name", "");

        coin_type_select.classList.add("d-none");
        coin_type_input.classList.remove("d-none");
        $("#coin_rate_api").show();
    }

    if (currency == 2) {
        $("#coin_type_input_").attr("name", "");
        $("#coin_type_select_").attr("name", "coin_type");

        coin_type_select.classList.remove("d-none");
        coin_type_input.classList.add("d-none");
        $("#coin_rate_api").hide();
    }
}

function currency_change(event) {
    currency = event.target.value;
    add_coin_ui_change();
}

$(document).ready(function() {
    add_coin_ui_change();
    $("#currency_type").on("change", currency_change);
    $("#coin_type_select_").on("change", function(e) {
        let rate = $(this).find(':selected').data("price");
        $('input[name="coin_price"]').val((1/rate).toFixed(8));
    });
});
</script>
@endpush