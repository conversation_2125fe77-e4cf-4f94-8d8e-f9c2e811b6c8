@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">مدیریت ارزها</h1>
                <a href="{{ route('admin.coins.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> افزودن ارز جدید
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>آیکون</th>
                                    <th>نام</th>
                                    <th>نوع ارز</th>
                                    <th>قیمت</th>
                                    <th>وضعیت</th>
                                    <th>برداشت</th>
                                    <th>واریز</th>
                                    <th>خرید</th>
                                    <th>فروش</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($coins as $coin)
                                <tr>
                                    <td>
                                        @if($coin->coin_icon)
                                            <img src="{{ asset('storage/' . $coin->coin_icon) }}" 
                                                 alt="{{ $coin->name }}" 
                                                 width="32" 
                                                 height="32">
                                        @endif
                                    </td>
                                    <td>{{ $coin->name }}</td>
                                    <td>{{ $coin->coin_type }}</td>
                                    <td>
                                        <span class="text-nowrap">
                                            <span class="text-muted">$</span>
                                            {{ visual_number_format($coin->coin_price) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-{{ $coin->status == "1" ? 'success' : 'danger' }} fw-bold">
                                            {{ $coin->status == "1" ? 'فعال' : 'غیرفعال' }}
                                        </span>
                                    </td>
                                    <td>
                                        <i class="fas fa-{{ $coin->is_withdrawal ? 'check text-success' : 'times text-danger' }}"></i>
                                    </td>
                                    <td>
                                        <i class="fas fa-{{ $coin->is_deposit ? 'check text-success' : 'times text-danger' }}"></i>
                                    </td>
                                    <td>
                                        <i class="fas fa-{{ $coin->is_buy ? 'check text-success' : 'times text-danger' }}"></i>
                                    </td>
                                    <td>
                                        <i class="fas fa-{{ $coin->is_sell ? 'check text-success' : 'times text-danger' }}"></i>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('admin.coins.edit', $coin->id) }}" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('admin.coins.show', $coin->id) }}" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-danger" 
                                                    onclick="deleteCoin({{ $coin->id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-4">
                        {{ $coins->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function deleteCoin(id) {
    if (confirm('آیا از حذف این ارز اطمینان دارید؟')) {
        axios.delete(`/admin/coins/${id}`)
            .then(response => {
                toastr.success('ارز با موفقیت حذف شد');
                window.location.reload();
            })
            .catch(error => {
                toastr.error('خطا در حذف ارز');
            });
    }
}
</script>
@endpush
@endsection
