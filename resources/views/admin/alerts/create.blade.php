@extends('admin.layouts.app')

@section('title', 'ایجاد هشدار جدید')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.alerts.index') }}">مدیریت هشدارها</a></li>
    <li class="breadcrumb-item active">ایجاد هشدار جدید</li>
@endsection

@section('content')
<div class="container-fluid fade-in">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title h3">ایجاد هشدار جدید</h1>
            <p class="text-muted">از این فرم برای ایجاد هشدار جدید برای کاربران استفاده کنید</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">اطلاعات هشدار</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.alerts.store') }}" method="POST">
                        @csrf
                        
                        <div class="mb-4">
                            <label for="message" class="form-label">متن هشدار <span class="text-danger">*</span></label>
                            <textarea name="message" id="message" rows="5" class="form-control @error('message') is-invalid @enderror" required>{{ old('message') }}</textarea>
                            @error('message')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">متن هشداری که به کاربر نمایش داده می‌شود را وارد کنید.</div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="type" class="form-label">نوع هشدار <span class="text-danger">*</span></label>
                                <select name="type" id="type" class="form-select @error('type') is-invalid @enderror" required>
                                    <option value="success" {{ old('type') == 'success' ? 'selected' : '' }}>موفقیت</option>
                                    <option value="warning" {{ old('type') == 'warning' ? 'selected' : '' }}>هشدار</option>
                                    <option value="error" {{ old('type') == 'error' ? 'selected' : '' }}>خطا</option>
                                    <option value="info" {{ old('type') == 'info' ? 'selected' : '' }}>اطلاعات</option>
                                    <option value="primary" {{ old('type') == 'primary' ? 'selected' : '' }}>عمومی</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6">
                                <label for="user_id" class="form-label">کاربر</label>
                                <select name="user_id" id="user_id" class="form-select @error('user_id') is-invalid @enderror">
                                    <option value="">هشدار سیستمی (بدون کاربر)</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                            {{ $user->firstname }} {{ $user->lastname }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('user_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">اگر هشدار مربوط به کاربر خاصی است، آن را انتخاب کنید. در غیر این صورت، هشدار به عنوان هشدار سیستمی ثبت می‌شود.</div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            هشدارهای ایجاد شده به صورت پیش‌فرض به عنوان "خوانده نشده" ثبت می‌شوند.
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ route('admin.alerts.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> انصراف
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> ذخیره هشدار
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">راهنما</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="fw-bold">انواع هشدار</h6>
                        <div class="d-flex flex-column gap-2 mt-3">
                            <div class="alert alert-success mb-0 py-2">
                                <strong>موفقیت:</strong> برای اطلاع‌رسانی عملیات‌های موفق
                            </div>
                            <div class="alert alert-warning mb-0 py-2">
                                <strong>هشدار:</strong> برای هشدارهایی که نیاز به توجه دارند
                            </div>
                            <div class="alert alert-danger mb-0 py-2">
                                <strong>خطا:</strong> برای اعلام خطاها و مشکلات
                            </div>
                            <div class="alert alert-info mb-0 py-2">
                                <strong>اطلاعات:</strong> برای پیام‌های اطلاع‌رسانی عمومی
                            </div>
                            <div class="alert alert-primary mb-0 py-2">
                                <strong>عمومی:</strong> برای پیام‌های عمومی سیستم
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="fw-bold">نکات مهم</h6>
                        <ul class="mt-2">
                            <li class="mb-2">هشدارها در داشبورد مدیریت و همچنین در پنل کاربری نمایش داده می‌شوند.</li>
                            <li class="mb-2">متن هشدار را واضح و مختصر بنویسید.</li>
                            <li class="mb-2">نوع هشدار را متناسب با محتوای پیام انتخاب کنید.</li>
                            <li>اگر هشدار مربوط به کاربر خاصی نیست، فیلد کاربر را خالی بگذارید.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // نمایش پیش‌نمایش هشدار بر اساس نوع انتخاب شده
        const typeSelect = document.getElementById('type');
        const messageTextarea = document.getElementById('message');
        
        function updatePreview() {
            const type = typeSelect.value;
            const message = messageTextarea.value.trim() || 'متن هشدار اینجا نمایش داده می‌شود';
            
            // اینجا می‌توانید کد نمایش پیش‌نمایش را اضافه کنید
        }
        
        typeSelect.addEventListener('change', updatePreview);
        messageTextarea.addEventListener('input', updatePreview);
        
        // اجرای اولیه برای نمایش پیش‌نمایش
        updatePreview();
    });
</script>
@endpush
