@extends('admin.layouts.app')

@section('title', 'مدیریت هشدارها')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
    <li class="breadcrumb-item active">مدیریت هشدارها</li>
@endsection

@section('content')
<div class="container-fluid fade-in">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title h3">مدیریت هشدارها</h1>
                <p class="text-muted">مشاهده و مدیریت هشدارهای کاربران</p>
            </div>
            <div>
                <a href="{{ route('admin.alerts.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> ایجاد هشدار جدید
                </a>
                <form action="{{ route('admin.alerts.mark-all-as-read') }}" method="POST" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check-double me-1"></i> علامت‌گذاری همه به عنوان خوانده شده
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- فیلترها -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">فیلترها</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.alerts.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="type" class="form-label">نوع هشدار</label>
                    <select name="type" id="type" class="form-select">
                        <option value="">همه</option>
                        <option value="success" {{ request('type') == 'success' ? 'selected' : '' }}>موفقیت</option>
                        <option value="warning" {{ request('type') == 'warning' ? 'selected' : '' }}>هشدار</option>
                        <option value="error" {{ request('type') == 'error' ? 'selected' : '' }}>خطا</option>
                        <option value="info" {{ request('type') == 'info' ? 'selected' : '' }}>اطلاعات</option>
                        <option value="primary" {{ request('type') == 'primary' ? 'selected' : '' }}>عمومی</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="read" class="form-label">وضعیت خواندن</label>
                    <select name="read" id="read" class="form-select">
                        <option value="">همه</option>
                        <option value="true" {{ request('read') == 'true' ? 'selected' : '' }}>خوانده شده</option>
                        <option value="false" {{ request('read') == 'false' ? 'selected' : '' }}>خوانده نشده</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="user_id" class="form-label">کاربر</label>
                    <select name="user_id" id="user_id" class="form-select">
                        <option value="">همه کاربران</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                {{ $user->firstname }} {{ $user->lastname }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> اعمال فیلتر
                    </button>
                    <a href="{{ route('admin.alerts.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> حذف فیلترها
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول هشدارها -->
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" width="60">#</th>
                            <th scope="col">پیام</th>
                            <th scope="col">نوع</th>
                            <th scope="col">کاربر</th>
                            <th scope="col">تاریخ ایجاد</th>
                            <th scope="col">وضعیت</th>
                            <th scope="col" width="150">عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($alerts as $alert)
                            <tr>
                                <td>{{ $alert->id }}</td>
                                <td>{{ Str::limit($alert->message, 70) }}</td>
                                <td>
                                    @if($alert->type == 'success')
                                        <span class="badge bg-success">موفقیت</span>
                                    @elseif($alert->type == 'warning')
                                        <span class="badge bg-warning text-dark">هشدار</span>
                                    @elseif($alert->type == 'error')
                                        <span class="badge bg-danger">خطا</span>
                                    @elseif($alert->type == 'info')
                                        <span class="badge bg-info text-dark">اطلاعات</span>
                                    @elseif($alert->type == 'primary')
                                        <span class="badge bg-primary">عمومی</span>
                                    @endif
                                </td>
                                <td>
                                    @if($alert->user)
                                        <a href="{{ route('admin.users.show', $alert->user->id) }}">
                                            {{ $alert->user->firstname }} {{ $alert->user->lastname }}
                                        </a>
                                    @else
                                        <span class="text-muted">سیستم</span>
                                    @endif
                                </td>
                                <td>{{ $alert->created_at->format('Y/m/d H:i') }}</td>
                                <td>
                                    @if($alert->read)
                                        <span class="badge bg-light text-dark">خوانده شده</span>
                                    @else
                                        <span class="badge bg-danger">خوانده نشده</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.alerts.show', $alert->id) }}" class="btn btn-sm btn-info" title="مشاهده">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($alert->read)
                                            <form action="{{ route('admin.alerts.mark-as-unread', $alert->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-warning" title="علامت‌گذاری به عنوان خوانده نشده">
                                                    <i class="fas fa-envelope"></i>
                                                </button>
                                            </form>
                                        @else
                                            <form action="{{ route('admin.alerts.mark-as-read', $alert->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-success" title="علامت‌گذاری به عنوان خوانده شده">
                                                    <i class="fas fa-envelope-open"></i>
                                                </button>
                                            </form>
                                        @endif
                                        <form action="{{ route('admin.alerts.destroy', $alert->id) }}" method="POST" class="d-inline delete-form">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">هیچ هشداری یافت نشد</h5>
                                        <p class="text-muted small">هشدار جدیدی ایجاد کنید یا فیلترهای خود را تغییر دهید.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    نمایش {{ $alerts->firstItem() ?? 0 }} تا {{ $alerts->lastItem() ?? 0 }} از {{ $alerts->total() }} مورد
                </div>
                <div>
                    {{ $alerts->withQueryString()->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأیید حذف هشدار
        const deleteForms = document.querySelectorAll('.delete-form');
        deleteForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                Swal.fire({
                    title: 'آیا مطمئن هستید؟',
                    text: "این هشدار به طور دائمی حذف خواهد شد!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'بله، حذف شود!',
                    cancelButtonText: 'انصراف'
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.submit();
                    }
                });
            });
        });
    });
</script>
@endpush
