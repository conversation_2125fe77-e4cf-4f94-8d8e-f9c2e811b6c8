@extends('admin.layouts.app')

@section('title', 'جزئیات هشدار')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.alerts.index') }}">مدیریت هشدارها</a></li>
    <li class="breadcrumb-item active">جزئیات هشدار</li>
@endsection

@section('content')
<div class="container-fluid fade-in">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title h3">جزئیات هشدار</h1>
                <p class="text-muted">مشاهده اطلاعات کامل هشدار</p>
            </div>
            <div>
                <a href="{{ route('admin.alerts.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> بازگشت به لیست
                </a>
                @if($alert->read)
                    <form action="{{ route('admin.alerts.mark-as-unread', $alert->id) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-envelope me-1"></i> علامت‌گذاری به عنوان خوانده نشده
                        </button>
                    </form>
                @else
                    <form action="{{ route('admin.alerts.mark-as-read', $alert->id) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-envelope-open me-1"></i> علامت‌گذاری به عنوان خوانده شده
                        </button>
                    </form>
                @endif
                <form action="{{ route('admin.alerts.destroy', $alert->id) }}" method="POST" class="d-inline delete-form">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> حذف هشدار
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- کارت جزئیات هشدار -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">اطلاعات هشدار</h5>
                    <span class="badge bg-{{ $alert->type }}">
                        @if($alert->type == 'success')
                            موفقیت
                        @elseif($alert->type == 'warning')
                            هشدار
                        @elseif($alert->type == 'error')
                            خطا
                        @elseif($alert->type == 'info')
                            اطلاعات
                        @elseif($alert->type == 'primary')
                            عمومی
                        @endif
                    </span>
                </div>
                <div class="card-body">
                    <div class="alert alert-{{ $alert->type }} mb-4">
                        {{ $alert->message }}
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">شناسه هشدار</label>
                                <p class="form-control-static">{{ $alert->id }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">وضعیت</label>
                                <p class="form-control-static">
                                    @if($alert->read)
                                        <span class="badge bg-light text-dark">خوانده شده</span>
                                    @else
                                        <span class="badge bg-danger">خوانده نشده</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">تاریخ ایجاد</label>
                                <p class="form-control-static">{{ $alert->created_at->format('Y/m/d H:i:s') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">تاریخ بروزرسانی</label>
                                <p class="form-control-static">{{ $alert->updated_at->format('Y/m/d H:i:s') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            @if($alert->user)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">اطلاعات کاربر</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <img src="{{ $alert->user->avatar ?? "https://ui-avatars.com/api/?name={$alert->user->firstname}+{$alert->user->lastname}&background=5a67d8&color=fff" }}" 
                                 class="rounded-circle mb-3" 
                                 width="80" 
                                 height="80"
                                 alt="{{ $alert->user->firstname }} {{ $alert->user->lastname }}">
                            <h5 class="mb-1">{{ $alert->user->firstname }} {{ $alert->user->lastname }}</h5>
                            <p class="text-muted">{{ $alert->user->email }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">شناسه کاربر</label>
                            <p class="form-control-static">{{ $alert->user->id }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">شماره تماس</label>
                            <p class="form-control-static">{{ $alert->user->phone ?? 'ثبت نشده' }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">تاریخ عضویت</label>
                            <p class="form-control-static">{{ $alert->user->created_at->format('Y/m/d') }}</p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="{{ route('admin.users.show', $alert->user->id) }}" class="btn btn-primary">
                                <i class="fas fa-user me-1"></i> مشاهده پروفایل کاربر
                            </a>
                        </div>
                    </div>
                </div>
            @else
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">اطلاعات کاربر</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <i class="fas fa-robot fa-4x text-muted mb-3"></i>
                            <h5 class="mb-1">هشدار سیستمی</h5>
                            <p class="text-muted">این هشدار توسط سیستم ایجاد شده است و به کاربر خاصی تعلق ندارد.</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأیید حذف هشدار
        const deleteForm = document.querySelector('.delete-form');
        if (deleteForm) {
            deleteForm.addEventListener('submit', function(e) {
                e.preventDefault();
                Swal.fire({
                    title: 'آیا مطمئن هستید؟',
                    text: "این هشدار به طور دائمی حذف خواهد شد!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'بله، حذف شود!',
                    cancelButtonText: 'انصراف'
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.submit();
                    }
                });
            });
        }
    });
</script>
@endpush
