@extends('admin.layouts.app')

<style>
    .ticket-header {
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 4px 6px -1px rgb(99 102 241 / 0.2);
    }

    .ticket-status {
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .status-open { background-color: #22c55e; }
    .status-closed { background-color: #64748b; }
    .status-pending { background-color: #eab308; }

    .info-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        transition: all 0.3s ease;
    }

    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    }

    .info-card .card-header {
        border-bottom: 1px solid #e5e7eb;
        padding: 1.25rem;
    }

    .info-card .card-body {
        padding: 1.25rem;
    }

    .user-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        margin-top: 1rem;
    }

    .stat-item {
        background: #f8fafc;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 600;
        color: #4f46e5;
    }

    .stat-label {
        color: #64748b;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .messages-container {
        max-height: 600px;
        overflow-y: auto;
        padding: 1rem;
    }

    .message-bubble {
        margin-bottom: 1.5rem;
        max-width: 80%;
    }

    .message-bubble.user {
        margin-right: auto;
        background: #f3f4f6;
        border-radius: 15px 15px 0 15px;
    }

    .message-bubble.admin {
        margin-left: auto;
        background: #ede9fe;
        border-radius: 15px 15px 15px 0;
    }

    .message-header {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        font-size: 0.875rem;
        color: #6b7280;
    }

    .message-content {
        padding: 1rem;
        line-height: 1.5;
    }

    .reply-form {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        margin-top: 2rem;
    }

    .reply-form textarea {
        border: 1px solid #e5e7eb;
        border-radius: 10px;
        padding: 1rem;
        transition: all 0.3s ease;
    }

    .reply-form textarea:focus {
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    }

    .btn-reply {
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 9999px;
        border: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-reply:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.4);
    }

    .badge {
        padding: 0.35em 0.65em;
        border-radius: 9999px;
        font-size: 0.75em;
        font-weight: 500;
    }

    .badge-role {
        background: #818cf8;
        color: white;
    }
</style>

@section('content')
<div class="container-fluid">
    <!-- هدر تیکت -->
    <div class="ticket-header">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">{{ $ticket->subject }}</h1>
            <span class="ticket-status status-{{ $ticket->status }}">
                @switch($ticket->status)
                    @case('open')
                        باز
                        @break
                    @case('closed')
                        بسته
                        @break
                    @case('pending')
                        در انتظار پاسخ
                        @break
                @endswitch
            </span>
        </div>
        <div class="d-flex gap-4">
            <div>
                <small class="d-block text-white/70">دپارتمان</small>
                <span>{{ $ticket->unit?->name ?: 'نامشخص' }}</span>
            </div>
            <div>
                <small class="d-block text-white/70">اولویت</small>
                <span>{{ $ticket->level?->name ?: 'نامشخص' }}</span>
            </div>
            <div>
                <small class="d-block text-white/70">تاریخ ایجاد</small>
                <span>{{ jdate($ticket->created_at)->format('Y/m/d H:i') }}</span>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- اطلاعات کاربر -->
        <div class="col-md-4 mb-4">
            <div class="info-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">اطلاعات کاربر</h5>
                    <a href="{{ route('admin.users.show', $ticket->user->id) }}" class="btn btn-sm btn-outline-primary rounded-pill">
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3">
                            <div class="avatar-circle">
                                {{ substr($ticket->user->firstname, 0, 1) }}{{ substr($ticket->user->lastname, 0, 1) }}
                            </div>
                        </div>
                        <div>
                            <h6 class="mb-1">{{ $ticket->user->firstname }} {{ $ticket->user->lastname }}</h6>
                            <p class="mb-0 text-muted">{{ $ticket->user->email }}</p>
                        </div>
                    </div>

                    <div class="user-info mb-4">
                        <div class="mb-2">
                            <small class="text-muted">موبایل:</small>
                            <span class="ms-2">{{ $ticket->user->phone }}</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">کد ملی:</small>
                            <span class="ms-2">{{ $ticket->user->national_id }}</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">جنسیت:</small>
                            <span class="ms-2">
                                @if($ticket->user->gender == 'male')
                                    مرد
                                @elseif($ticket->user->gender == 'female')
                                    زن
                                @else
                                    نامشخص
                                @endif
                            </span>
                        </div>
                    </div>

                    <div class="user-stats">
                        <div class="stat-item">
                            <div class="stat-value">{{ $ticket->user->transactions->where('type', 'buy')->count() }}</div>
                            <div class="stat-label">خرید</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ $ticket->user->transactions->where('type', 'sell')->count() }}</div>
                            <div class="stat-label">فروش</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{ $ticket->user->tickets->count() }}</div>
                            <div class="stat-label">تیکت‌ها</div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <small class="text-muted d-block mb-2">نقش‌ها:</small>
                        @foreach($ticket->user->roles as $role)
                            <span class="badge badge-role me-1">{{ $role->name }}</span>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- پیام‌های تیکت -->
        <div class="col-md-8">
            <div class="info-card">
                <div class="card-header">
                    <h5 class="mb-0">پیام‌های تیکت</h5>
                </div>
                <div class="messages-container">
                    @foreach($ticket->tickets as $message)
                        <div class="message-bubble {{ $message->user_id == $ticket->user_id ? 'user' : 'admin' }}">
                            <div class="message-header">
                                <span>{{ $message->user_id == $ticket->user_id ? 'کاربر' : 'پشتیبان' }}</span>
                                <small class="ms-2">{{ jdate($message->created_at)->format('Y/m/d H:i') }}</small>
                            </div>
                            <div class="message-content">
                                {{ $message->message }}
                            </div>
                        </div>
                    @endforeach
                </div>

                @if($ticket->status != 'closed')
                    <div class="reply-form">
                        <form action="{{ route('admin.tickets.reply', $ticket->id) }}" method="POST">
                            @csrf
                            <div class="form-group">
                                <label for="message" class="form-label">پاسخ شما</label>
                                <textarea name="message" id="message" rows="4" 
                                    class="form-control @error('message') is-invalid @enderror"
                                    placeholder="پاسخ خود را بنویسید..."></textarea>
                                @error('message')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="text-end mt-3">
                                <button type="submit" class="btn-reply">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    ارسال پاسخ
                                </button>
                            </div>
                        </form>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
