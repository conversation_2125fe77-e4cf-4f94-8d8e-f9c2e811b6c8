@extends('admin.layouts.app')
@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ایجاد کیف پول جدید</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.system-wallets.store') }}" method="POST">
                        @csrf

                        <div class="form-group">
                            <label>نوع ساخت</label>
                            <select name="creation_type" class="form-control @error('creation_type') is-invalid @enderror" required>
                                <option value="">انتخاب کنید</option>
                                <option value="0" {{ old('creation_type') == '0' ? 'selected' : '' }}>ساخت خودکار</option>
                                <option value="1" {{ old('creation_type') == '1' ? 'selected' : '' }}>وارد کردن دستی</option>
                            </select>
                            @error('creation_type')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label>شبکه</label>
                            <select name="network_id" class="form-control @error('network_id') is-invalid @enderror" required>
                                <option value="">انتخاب کنید</option>
                                @foreach($networks as $network)
                                    <option value="{{ $network->id }}" {{ old('network_id') == $network->id ? 'selected' : '' }}>
                                        {{ $network->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('network_id')
                                <span class="invalid-feedback">{{ $message }}</span>
                            @enderror
                        </div>

                        <div id="manual-inputs" style="display: none;">
                            <div class="form-group">
                                <label>آدرس کیف پول</label>
                                <input type="text" name="address" class="form-control @error('address') is-invalid @enderror" value="{{ old('address') }}">
                                @error('address')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label>کلید خصوصی</label>
                                <input type="text" name="private_key" class="form-control @error('private_key') is-invalid @enderror" value="{{ old('private_key') }}">
                                @error('private_key')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" 
                                       class="custom-control-input" 
                                       id="status" 
                                       name="status" 
                                       value="1"
                                       {{ old('status', '1') == '1' ? 'checked' : '' }}>
                                <label class="custom-control-label" for="status">فعال</label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">ایجاد</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const creationTypeSelect = document.querySelector('select[name="creation_type"]');
    const manualInputs = document.getElementById('manual-inputs');

    function toggleManualInputs() {
        if (creationTypeSelect.value === '1') {
            manualInputs.style.display = 'block';
        } else {
            manualInputs.style.display = 'none';
        }
    }

    creationTypeSelect.addEventListener('change', toggleManualInputs);
    toggleManualInputs(); // Run on initial load
});
</script>
@endpush

@endsection
