@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">ویرایش کیف پول</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.system-wallets.update', $wallet->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="form-group">
                            <label>شبکه</label>
                            <select name="network_id" class="form-control" required>
                                <option value="">انتخاب کنید</option>
                                @foreach($networks as $network)
                                    <option value="{{ $network->id }}" 
                                        {{ $wallet->network_id == $network->id ? 'selected' : '' }}>
                                        {{ $network->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form-group">
                            <label>آدرس کیف پول</label>
                            <input type="text" 
                                   name="address" 
                                   class="form-control" 
                                   value="{{ $wallet->address }}" 
                                   required>
                        </div>

                        <div class="form-group">
                            <label>کلید خصوصی</label>
                            <input type="text" 
                                   name="pv"  value="{{ $wallet->pv }}"
                                   class="form-control">
                            <small class="text-muted">در صورت عدم تغییر خالی بگذارید</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" 
                                       class="custom-control-input" 
                                       id="status" 
                                       name="status" 
                                       value="1"
                                       {{ $wallet->status ? 'checked' : '' }}>
                                <label class="custom-control-label" for="status">فعال</label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">بروزرسانی</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection