@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">کیف پول‌های سیستمی</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.system-wallets.create') }}" class="btn btn-primary">
                            ایجاد کیف پول جدید
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>شبکه</th>
                                <th>آدرس</th>
                                <th>وضعیت</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($wallets as $wallet)
                            <tr>
                                <td>
                                    @if($wallet->network->logo)
                                        <img src="{{ asset('storage/' . $wallet->network->logo) }}" 
                                             alt="{{ $wallet->network->name }}" 
                                             height="40">
                                    @endif
                                    {{ $wallet->network->name }}
                                </td>
                                <td>{{ $wallet->address }}</td>
                                <td>
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" 
                                               class="custom-control-input status-toggle" 
                                               id="status_{{ $wallet->id }}"
                                               data-id="{{ $wallet->id }}"
                                               {{ $wallet->status ? 'checked' : '' }}>
                                        <label class="custom-control-label" 
                                               for="status_{{ $wallet->id }}"></label>
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ route('admin.system-wallets.edit', $wallet->id) }}" 
                                       class="btn btn-sm btn-info">
                                        ویرایش
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    {{ $wallets->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('.status-toggle').on('change', function() {
        const id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("admin.system-wallets.toggle-status") }}',
            type: 'POST',
            data: {
                id: id,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            }
        });
    });
});
</script>
@endpush