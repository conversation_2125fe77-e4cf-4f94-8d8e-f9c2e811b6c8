@extends('admin.layouts.app')

@section('content')
    <!-- breadcrumb -->
    <div class="custom-breadcrumb">
        <div class="row">
            <div class="col-md-9">
                <ul>
                    <li>{{__('شبکه')}}</li>
                    <li class="active-item">{{ $title }}</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- /breadcrumb -->

    <!-- User Management -->
    <div class="user-management">
        <div class="row">
            <div class="col-12">
                <div class="profile-info-form">
                    <div>
                    <form action="{{ route('admin.createCoinNetworkProccess') }}" method="POST">
                        @csrf
                        @if(isset($item->id))
                            <input type="hidden" name="uid" value="{{ $item->uid }}" />
                        @endif
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('شبکه')}}</div>
                                        <select name="network_id" id="" class="form-control" @if(isset($item)) disabled @endif>
                                                <option>{{ __("انتخاب شبکه") }}</option>
                                            @foreach($networks as $network)
                                                <option @if(isset($item) && ($item->network_id == $network->id)) selected @endif value="{{ $network->id }}">{{ $network->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('ارز')}}</div>
                                        <select name="currency_id" id="" class="form-control" @if(isset($item)) disabled @endif>
                                                <option>{{ __("انتخاب ارز") }}</option>
                                            @foreach($currencys as $currency)
                                                <option @if(isset($item) && ($item->currency_id == $currency->id)) selected @endif value="{{ $currency->id }}">{{ $currency->coin_type }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('نوع')}}</div>
                                        <select name="type" id="coin_type" class="form-control">
                                                <option>{{ __("انتخاب نوع") }}</option>
                                            @foreach(getCoinNetworkType() as $key => $val)
                                                <option @if(isset($item) && ($item->type == $key)) selected @endif value="{{ $key }}">{{ $val }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 contract_address">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('آدرس قرارداد')}}</div>
                                        <input type="text" class="form-control" name="contract_address" @if(isset($item))value="{{$item->contract_address}}" @else value="{{old('contract_address')}}" @endif>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('نوع کارمزد برداشت')}}</div>
                                        <select name="withdrawal_fees_type" id="" class="form-control">
                                            @foreach(discount_type() as $key => $val)
                                                <option @if(isset($item) && ($item->withdrawal_fees_type == $key)) selected @endif value="{{ $key }}">{{ $val }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('کارمزد برداشت')}}</div>
                                        <input type="text" class="form-control" name="withdrawal_fees"
                                               @if(isset($item))value="{{$item->withdrawal_fees}}" @else value="0.00000010" @endif>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('وضعیت')}}</div>
                                        <label class="switch">
                                            <input type="checkbox" name="status" @if(isset($item) && $item->status==1)checked  @endif>
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                @if(isset($item))
                                    <button type="submit" class="btn theme-btn">{{ __("بروزرسانی") }}</button>
                                @else
                                    <button type="submit" class="btn theme-btn">{{ __("ایجاد") }}</button>
                                @endif

                            </div>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /User Management -->
@endsection

@push('scripts')
<script>
    (function($) {
        "use strict";
        function hideNode(value){
            if(value == 1){
                $(".contract_address").show();
            }else{
                $(".contract_address").hide();
            }
        }
        $("#coin_type").change((e)=>{
            var value = $(e.target).val();
            hideNode(value);
        });
        hideNode({{ isset($item->type) ? $item->type : 0 }});
    })(jQuery);
</script>
@endpush
