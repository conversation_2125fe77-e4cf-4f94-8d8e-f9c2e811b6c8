@extends('admin.layouts.app')

@section('content')
    <!-- breadcrumb -->
    <div class="custom-breadcrumb">
        <div class="row">
            <div class="col-9">
                <ul>
                    <li>{{__('مدیریت ارز')}}</li>
                    <li class="active-item">{{ $title }}</li>
                </ul>
            </div>
            <div class="col-sm-3 text-left">
                <a class="add-btn theme-btn" href="{{ route('admin.createCoinNetwork') }}">
                    <i class="fa fa-plus"></i>{{ __('ایجاد ارز و شبکه') }}
                </a>
            </div>
        </div>
    </div>
    <!-- /breadcrumb -->

    <!-- User Management -->
    <div class="user-management">
        <div class="row">
            <div class="col-12">
                <div class="table-area">
                    <div class="table-responsive">
                        <table id="coinNetworkTable" class="table table-borderless custom-table display text-center">
                            <thead>
                                <tr>
                                    <th scope="col">{{ __('لوگو') }}</th>
                                    <th scope="col">{{ __('ارز') }}</th>
                                    <th scope="col">{{ __('شبکه') }}</th>
                                    <th scope="col">{{ __('نوع') }}</th>
                                    <th scope="col">{{ __('وضعیت') }}</th>
                                    <th scope="col">{{ __('تاریخ ایجاد') }}</th>
                                    <th scope="col">{{ __('تاریخ بروزرسانی') }}</th>
                                    <th scope="col">{{ __('عملیات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /User Management -->
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        $('#coinNetworkTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: '{{ route("admin.getCoinNetworkList") }}',
            columns: [
                {data: 'logo', name: 'logo'},
                {data: 'coin.coin_type', name: 'coin.coin_type'},
                {data: 'network.name', name: 'network.name'},
                {data: 'type', name: 'type'},
                {data: 'status', name: 'status'},
                {data: 'created_at', name: 'created_at'},
                {data: 'updated_at', name: 'updated_at'},
                {data: 'actions', name: 'actions'}
            ]
        });
    });

    function changeNetworkStatus(id) {
        $.ajax({
            url: "{{ route('admin.changeCoinNetworkStatus') }}",
            type: "POST",
            data: {
                '_token': '{{ csrf_token() }}',
                'id': id
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(error) {
                toastr.error("{{ __('خطایی رخ داد') }}");
            }
        });
    }
</script>
@endpush
