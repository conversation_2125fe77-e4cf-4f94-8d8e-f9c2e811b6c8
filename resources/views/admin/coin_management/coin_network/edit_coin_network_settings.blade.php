@extends('admin.layouts.app')

@section('content')
    <!-- breadcrumb -->
    <div class="custom-breadcrumb">
        <div class="row">
            <div class="col-md-9">
                <ul>
                    <li>{{__('شبکه')}}</li>
                    <li class="active-item">{{ $title }}</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- /breadcrumb -->

    <!-- User Management -->
    <div class="user-management">
        <div class="row">
            <div class="col-12">
                <div class="profile-info-form">
                    <div>
                        {{Form::open(['route'=>'adminCoinSettingsSave', 'method' => 'POST'])}}
                        <input type="hidden" name="coin_id" value="{{ encrypt($item->id) }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('نام ارز')}}</div>
                                        <input type="text" class="form-control" value="{{ $item->name }}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('نوع ارز')}}</div>
                                        <input type="text" class="form-control" value="{{ $item->coin_type }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('شبکه')}}</div>
                                        <input type="text" class="form-control" value="{{ $network->name }}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('نوع شبکه')}}</div>
                                        <input type="text" class="form-control" value="{{ getBaseNetworkType($network->base_type) }}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($network->base_type == BITGO_API)
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('شناسه کیف پول Bitgo')}} <span class="text-danger">*</span></div>
                                        <input type="text" name="bitgo_wallet_id" id="bitgo_wallet_id" class="form-control" value="{{ $coin_setting->bitgo_wallet_id ?? old('bitgo_wallet_id') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('کیف پول Bitgo')}} <span class="text-danger">*</span></div>
                                        <input type="text" name="bitgo_wallet" id="bitgo_wallet" class="form-control" value="{{ $coin_setting->bitgo_wallet ?? old('bitgo_wallet') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('زنجیره')}} <span class="text-danger">*</span></div>
                                        <input type="text" name="chain" id="chain" class="form-control" value="{{ $coin_setting->chain ?? old('chain') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        @elseif($network->base_type == BITCOIN_API)
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('نام کاربری API ارز')}} <span class="text-danger">*</span></div>
                                        <input type="text" name="coin_api_user" id="coin_api_user" class="form-control" value="{{ $coin_setting->coin_api_user ?? old('coin_api_user') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('رمز عبور API ارز')}} <span class="text-danger">*</span></div>
                                        <input type="password" name="coin_api_pass" id="coin_api_pass" class="form-control" value="{{ $coin_setting->coin_api_pass ?? old('coin_api_pass') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('هاست API ارز')}} <span class="text-danger">*</span></div>
                                        <input type="text" name="coin_api_host" id="coin_api_host" class="form-control" value="{{ $coin_setting->coin_api_host ?? old('coin_api_host') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('پورت API ارز')}} <span class="text-danger">*</span></div>
                                        <input type="text" name="coin_api_port" id="coin_api_port" class="form-control" value="{{ $coin_setting->coin_api_port ?? old('coin_api_port') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        @else
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('نام ارز قرارداد')}}</div>
                                        <input type="text" name="contract_coin_name" id="contract_coin_name" class="form-control" value="{{ $coin_setting->contract_coin_name ?? old('contract_coin_name') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('لینک زنجیره')}}</div>
                                        <input type="text" name="chain_link" id="chain_link" class="form-control" value="{{ $coin_setting->chain_link ?? old('chain_link') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('شناسه زنجیره')}}</div>
                                        <input type="text" name="chain_id" id="chain_id" class="form-control" value="{{ $coin_setting->chain_id ?? old('chain_id') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('آدرس قرارداد')}}</div>
                                        <input type="text" name="contract_address" id="contract_address" class="form-control" value="{{ $coin_setting->contract_address ?? old('contract_address') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('آدرس کیف پول')}}</div>
                                        <input type="text" name="wallet_address" id="wallet_address" class="form-control" value="{{ $coin_setting->wallet_address ?? old('wallet_address') }}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('اعشار قرارداد')}}</div>
                                        <input type="text" name="contract_decimal" id="contract_decimal" class="form-control" value="{{ $coin_setting->contract_decimal ?? old('contract_decimal') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="controls">
                                        <div class="form-label">{{__('محدودیت گاز')}}</div>
                                        <input type="text" name="gas_limit" id="gas_limit" class="form-control" value="{{ $coin_setting->gas_limit ?? old('gas_limit') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                        <div class="row mt-3">
                            <div class="col-md-2">
                                <button type="submit" class="btn theme-btn">{{ $button_title }}</button>
                            </div>
                        </div>
                        {{Form::close()}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /User Management -->
@endsection
