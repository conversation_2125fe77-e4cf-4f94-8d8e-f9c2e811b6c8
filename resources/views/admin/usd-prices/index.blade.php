@extends('admin.layouts.app')

@section('title', 'مدیریت قیمت دلار')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">لیست قیمت‌های دلار</h5>
                    <a href="{{ route('admin.usd-prices.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> افزودن قیمت جدید
                    </a>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif
                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>شناسه</th>
                                    <th>قیمت خرید (تومان)</th>
                                    <th>قیمت فروش (تومان)</th>
                                    <th>وضعیت</th>
                                    <th>تاریخ ایجاد</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($usdPrices as $price)
                                    <tr>
                                        <td>{{ $price->id }}</td>
                                        <td>{{ number_format($price->buy_price) }}</td>
                                        <td>{{ number_format($price->sell_price) }}</td>
                                        <td>
                                            @if($price->is_active)
                                                <span class="badge bg-success">فعال</span>
                                            @else
                                                <span class="badge bg-secondary">غیرفعال</span>
                                            @endif
                                        </td>
                                        <td>{{ jdate($price->created_at)->format('Y/m/d H:i') }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ route('admin.usd-prices.edit', $price) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-edit"></i> ویرایش
                                                </a>
                                                @if(!$price->is_active)
                                                    <form action="{{ route('admin.usd-prices.set-active', $price) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-success">
                                                            <i class="fas fa-check"></i> فعال کردن
                                                        </button>
                                                    </form>
                                                    <form action="{{ route('admin.usd-prices.destroy', $price) }}" method="POST" class="d-inline delete-form">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i> حذف
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">هیچ قیمتی ثبت نشده است</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-4">
                        {{ $usdPrices->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        $('.delete-form').on('submit', function(e) {
            e.preventDefault();
            if (confirm('آیا از حذف این قیمت اطمینان دارید؟')) {
                this.submit();
            }
        });
    });
</script>
@endsection
