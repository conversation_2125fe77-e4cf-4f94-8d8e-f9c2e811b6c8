@extends('admin.layouts.app')

@section('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<style>
    /* کارت‌های آمار - طراحی جدید با گرادینت و افکت شیشه‌ای */
    .stats-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.1);
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        margin-bottom: 1.5rem;
    }

    .stats-card .card-body {
        padding: 2rem;
        position: relative;
        z-index: 2;
    }

    .stats-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 100%);
        z-index: 1;
    }

    .stats-card h5 {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 1rem;
        color: rgba(255,255,255,0.9);
    }

    .stats-card h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        background: linear-gradient(45deg, #ffffff, #f0f0f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    /* فیلترها - طراحی مدرن با انیمیشن */
    .filter-card {
        background: #ffffff;
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        border: none;
        transition: all 0.3s ease;
    }

    .filter-card:hover {
        box-shadow: 0 15px 40px rgba(0,0,0,0.08);
    }

    .form-control, .form-select {
        border-radius: 15px;
        padding: 0.8rem 1.2rem;
        border: 2px solid #eef2f7;
        font-size: 0.95rem;
        transition: all 0.3s;
        background: #f8fafc;
    }

    .form-control:focus, .form-select:focus {
        border-color: #5a67d8;
        box-shadow: 0 0 0 4px rgba(90, 103, 216, 0.1);
        background: #ffffff;
    }

    .btn {
        border-radius: 15px;
        padding: 0.8rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-primary {
        background: linear-gradient(45deg, #5a67d8, #4c51bf);
        border: none;
        box-shadow: 0 4px 15px rgba(90, 103, 216, 0.2);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(90, 103, 216, 0.3);
    }

    /* جدول تراکنش‌ها - طراحی مدرن */
    .transactions-card {
        background: #ffffff;
        border-radius: 25px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        border: none;
    }

    .transactions-card .card-header {
        background: linear-gradient(45deg, #f7fafc, #ffffff);
        padding: 1.5rem 2rem;
        border-bottom: 2px solid #eef2f7;
    }

    .table {
        margin: 0;
    }

    .table th {
        font-weight: 600;
        color: #4a5568;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        padding: 1.2rem 1.5rem;
        background: #f8fafc;
        border-bottom: 2px solid #eef2f7;
    }

    .table td {
        padding: 1.2rem 1.5rem;
        vertical-align: middle;
        border-bottom: 1px solid #eef2f7;
        transition: all 0.3s;
    }

    .table tr:hover td {
        background: #f8fafc;
        transform: scale(1.01);
    }

    /* بج‌های وضعیت - طراحی جدید */
    .status-badge {
        padding: 0.5rem 1.2rem;
        border-radius: 30px;
        font-size: 0.85rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .badge-done {
        background: linear-gradient(45deg, #48bb78, #38a169);
        color: #ffffff;
    }

    .badge-pending {
        background: linear-gradient(45deg, #ecc94b, #d69e2e);
        color: #ffffff;
    }

    .badge-rejected {
        background: linear-gradient(45deg, #f56565, #c53030);
        color: #ffffff;
    }

    /* جزئیات تراکنش - طراحی مدرن */
    .transaction-details {
        background: linear-gradient(135deg, #ffffff, #f7fafc);
        border-radius: 20px;
        padding: 2rem;
        margin-top: 1.5rem;
        border: 2px solid #eef2f7;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        transform-origin: top;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .transaction-details.show {
        display: block;
        animation: slideDown 0.4s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .user-info-item {
        background: #f8fafc;
        border-radius: 15px;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s;
    }

    .user-info-item:hover {
        background: #ffffff;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transform: translateY(-2px);
    }

    .user-info-item label {
        color: #718096;
        font-size: 0.85rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
    }

    .user-info-item p {
        color: #2d3748;
        font-size: 1rem;
        font-weight: 500;
        margin: 0;
    }

    /* انیمیشن‌های اضافی */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .pulse {
        animation: pulse 2s infinite;
    }

    /* دکمه‌های عملیات */
    .action-buttons .btn {
        width: 40px;
        height: 40px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        transition: all 0.3s;
        margin: 0 5px;
        background: #f8fafc;
        border: 2px solid #eef2f7;
        color: #4a5568;
    }

    .action-buttons .btn:hover {
        transform: translateY(-3px) rotate(8deg);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        background: #5a67d8;
        color: #ffffff;
        border-color: #5a67d8;
    }

    /* پیام خالی بودن */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
    }

    .empty-state img {
        width: 200px;
        height: 200px;
        margin-bottom: 2rem;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
        100% { transform: translateY(0px); }
    }

    .empty-state .text-muted {
        font-size: 1.1rem;
        color: #718096;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- آمار تراکنش‌ها -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-primary text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('تعداد کل تراکنش‌ها') }}</h5>
                    <h2 class="mb-0">{{ $stats['total_count'] ?? 0 }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-success text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('مجموع تراکنش‌های موفق') }}</h5>
                    <h2 class="mb-0">{{ $stats['success_count'] ?? 0 }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-warning text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('تراکنش‌های امروز') }}</h5>
                    <h2 class="mb-0">{{ $stats['today_count'] ?? 0 }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-info text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('کاربران فعال') }}</h5>
                    <h2 class="mb-0">{{ $stats['active_users'] ?? 0 }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- فیلترها -->
    <div class="card filter-card">
        <form action="{{ route('admin.transaction.index') }}" method="GET" id="filter-form">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="type">{{ __('نوع تراکنش') }}</label>
                    <select class="form-control" id="type" name="type">
                        <option value="">{{ __('همه') }}</option>
                        <option value="deposit" {{ request('type') == 'deposit' ? 'selected' : '' }}>{{ __('واریز') }}</option>
                        <option value="withdraw" {{ request('type') == 'withdraw' ? 'selected' : '' }}>{{ __('برداشت') }}</option>
                        <option value="buy" {{ request('type') == 'buy' ? 'selected' : '' }}>{{ __('خرید') }}</option>
                        <option value="sell" {{ request('type') == 'sell' ? 'selected' : '' }}>{{ __('فروش') }}</option>
                        <option value="swap_out" {{ request('type') == 'swap_out' ? 'selected' : '' }}>{{ __('تبدیل ارز (خروجی)') }}</option>
                        <option value="swap_in" {{ request('type') == 'swap_in' ? 'selected' : '' }}>{{ __('تبدیل ارز (ورودی)') }}</option>
                        <option value="increase" {{ request('type') == 'increase' ? 'selected' : '' }}>{{ __('افزایش موجودی') }}</option>
                        <option value="decrease" {{ request('type') == 'decrease' ? 'selected' : '' }}>{{ __('کاهش موجودی') }}</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="status">{{ __('وضعیت') }}</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">{{ __('همه') }}</option>
                        <option value="done" {{ request('status') == 'done' ? 'selected' : '' }}>{{ __('انجام شده') }}</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>{{ __('در انتظار') }}</option>
                        <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>{{ __('رد شده') }}</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="from_date">{{ __('از تاریخ') }}</label>
                    <input type="date" class="form-control" id="from_date" name="from_date" value="{{ request('from_date') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="to_date">{{ __('تا تاریخ') }}</label>
                    <input type="date" class="form-control" id="to_date" name="to_date" value="{{ request('to_date') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="amount_min">{{ __('حداقل مبلغ') }}</label>
                    <input type="number" step="0.00000001" class="form-control" id="amount_min" name="amount_min" value="{{ request('amount_min') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="amount_max">{{ __('حداکثر مبلغ') }}</label>
                    <input type="number" step="0.00000001" class="form-control" id="amount_max" name="amount_max" value="{{ request('amount_max') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="user_id">{{ __('کاربر') }}</label>
                    <input type="text" class="form-control" id="user_id" name="user_id" value="{{ request('user_id') }}" placeholder="{{ __('شناسه کاربر') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="currency_id">{{ __('ارز') }}</label>
                    <select class="form-control" id="currency_id" name="currency_id">
                        <option value="">{{ __('همه') }}</option>
                        @foreach($currencies as $currency)
                            <option value="{{ $currency->id }}" {{ request('currency_id') == $currency->id ? 'selected' : '' }}>
                                {{ $currency->name }} ({{ $currency->coin_type }})
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-12 text-right">
                    <button type="submit" class="btn btn-primary">{{ __('اعمال فیلتر') }}</button>
                    <a href="{{ route('admin.transaction.index') }}" class="btn btn-secondary">{{ __('پاک کردن فیلترها') }}</a>
                </div>
            </div>
        </form>
    </div>

    <!-- جدول تراکنش‌ها -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('لیست تراکنش‌ها') }}</h5>
            <div>
                <a href="{{ route('admin.transaction.index', ['type' => 'buy,sell']) }}" class="btn btn-info btn-sm">
                    <i class="fas fa-exchange-alt mr-1"></i> {{ __('خرید و فروش') }}
                </a>
                <a href="{{ route('admin.transaction.index', ['type' => 'swap_in,swap_out']) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-sync mr-1"></i> {{ __('تبدیل ارز') }}
                </a>
                <a href="{{ route('admin.transaction.index', ['type' => 'deposit,withdraw']) }}" class="btn btn-success btn-sm">
                    <i class="fas fa-wallet mr-1"></i> {{ __('واریز و برداشت') }}
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="thead-light">
                        <tr>
                            <th>{{ __('شناسه') }}</th>
                            <th>{{ __('کاربر') }}</th>
                            <th>{{ __('نوع') }}</th>
                            <th>{{ __('مبلغ') }}</th>
                            <th>{{ __('ارز') }}</th>
                            <th>{{ __('وضعیت') }}</th>
                            <th>{{ __('تاریخ') }}</th>
                            <th>{{ __('عملیات') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($transactions as $transaction)
                            <tr>
                                <td>{{ $transaction->id }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div>
                                            <strong>{{ $transaction->user ? $transaction->user->email : 'N/A' }}</strong>
                                            <div class="small text-muted">ID: {{ $transaction->user_id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge
                                        @if(in_array($transaction->type, ['buy', 'deposit', 'increase'])) badge-success
                                        @elseif(in_array($transaction->type, ['sell', 'withdraw', 'decrease'])) badge-danger
                                        @elseif($transaction->type == 'swap_out') badge-warning
                                        @elseif($transaction->type == 'swap_in') badge-info
                                        @else badge-secondary @endif">
                                        {{ $transaction->type_description }}
                                    </span>
                                </td>
                                <td>
                                    <strong>{{ number_format($transaction->amount, 8) }}</strong>
                                </td>
                                <td>
                                    {{ $transaction->currency ? $transaction->currency->coin_type : 'N/A' }}
                                </td>
                                <td>
                                    <span class="badge
                                        @if($transaction->status == 'done') badge-success
                                        @elseif($transaction->status == 'pending') badge-warning
                                        @elseif($transaction->status == 'rejected') badge-danger
                                        @else badge-secondary @endif">
                                        {{ $transaction->status }}
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        {{ $transaction->created_at ? \Hekmatinasser\Verta\Verta::instance($transaction->created_at)->format('Y/m/d H:i') : 'N/A' }}
                                    </div>
                                    <div class="small text-muted">
                                        {{ $transaction->created_at ? \Hekmatinasser\Verta\Verta::instance($transaction->created_at)->formatDifference() : 'N/A' }}
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ route('admin.transaction.show', $transaction->id) }}"
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        <button type="button"
                                                class="btn btn-sm btn-primary transaction-details-btn"
                                                data-id="{{ $transaction->id }}">
                                            <i class="fas fa-info-circle"></i>
                                        </button>
                                    </div>

                                    <!-- جزئیات تراکنش -->
                                    <div class="transaction-details" id="details-{{ $transaction->id }}">
                                        <h6 class="mb-3">{{ __('جزئیات تراکنش') }}</h6>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="user-info-item">
                                                    <label>{{ __('شناسه تراکنش') }}</label>
                                                    <p><code>{{ $transaction->id }}</code></p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="user-info-item">
                                                    <label>{{ __('نوع تراکنش') }}</label>
                                                    <p>{{ $transaction->type_description }}</p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="user-info-item">
                                                    <label>{{ __('مبلغ') }}</label>
                                                    <p>{{ number_format($transaction->amount, 8) }} {{ $transaction->currency ? $transaction->currency->coin_type : '' }}</p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="user-info-item">
                                                    <label>{{ __('وضعیت') }}</label>
                                                    <p>{{ $transaction->status }}</p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="user-info-item">
                                                    <label>{{ __('موجودی قبل') }}</label>
                                                    <p>{{ number_format($transaction->balance_before ?? 0, 8) }} {{ $transaction->currency ? $transaction->currency->coin_type : '' }}</p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="user-info-item">
                                                    <label>{{ __('موجودی بعد') }}</label>
                                                    <p>{{ number_format($transaction->balance_after ?? 0, 8) }} {{ $transaction->currency ? $transaction->currency->coin_type : '' }}</p>
                                                </div>
                                            </div>

                                            @if($transaction->type == 'swap_out' || $transaction->type == 'swap_in')
                                                <div class="col-md-12">
                                                    <hr>
                                                    <h6>{{ __('جزئیات تبدیل ارز') }}</h6>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('ارز مبدأ') }}</label>
                                                        <p>{{ $transaction->swap_details['from_currency'] ?? 'N/A' }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('ارز مقصد') }}</label>
                                                        <p>{{ $transaction->swap_details['to_currency'] ?? 'N/A' }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('مقدار مبدأ') }}</label>
                                                        <p>{{ number_format($transaction->swap_details['from_amount'] ?? 0, 8) }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('مقدار مقصد') }}</label>
                                                        <p>{{ number_format($transaction->swap_details['to_amount'] ?? 0, 8) }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('کارمزد') }}</label>
                                                        <p>{{ $transaction->swap_details['fee_percentage'] ?? '0%' }} ({{ number_format($transaction->swap_details['fee_amount'] ?? 0, 8) }})</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('ارزش دلاری') }}</label>
                                                        <p>${{ number_format($transaction->swap_details['usd_value'] ?? 0, 2) }}</p>
                                                    </div>
                                                </div>
                                            @elseif($transaction->type == 'buy')
                                                <div class="col-md-12">
                                                    <hr>
                                                    <h6>{{ __('جزئیات خرید') }}</h6>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('مبلغ تومان') }}</label>
                                                        <p>{{ number_format($transaction->buy_details['toman_amount'] ?? 0) }} تومان</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('مبلغ دلار') }}</label>
                                                        <p>${{ number_format($transaction->buy_details['usd_amount'] ?? 0, 2) }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('نرخ دلار') }}</label>
                                                        <p>{{ number_format($transaction->buy_details['usd_rate'] ?? 0) }} تومان</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('مقدار ارز') }}</label>
                                                        <p>{{ number_format($transaction->buy_details['crypto_amount'] ?? 0, 8) }} {{ $transaction->currency ? $transaction->currency->coin_type : '' }}</p>
                                                    </div>
                                                </div>
                                            @elseif($transaction->type == 'sell')
                                                <div class="col-md-12">
                                                    <hr>
                                                    <h6>{{ __('جزئیات فروش') }}</h6>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('مقدار ارز') }}</label>
                                                        <p>{{ number_format($transaction->sell_details['crypto_amount'] ?? 0, 8) }} {{ $transaction->currency ? $transaction->currency->coin_type : '' }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('مبلغ دلار') }}</label>
                                                        <p>${{ number_format($transaction->sell_details['usd_amount'] ?? 0, 2) }}</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('نرخ دلار') }}</label>
                                                        <p>{{ number_format($transaction->sell_details['usd_rate'] ?? 0) }} تومان</p>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="user-info-item">
                                                        <label>{{ __('مبلغ تومان') }}</label>
                                                        <p>{{ number_format($transaction->sell_details['toman_amount'] ?? 0) }} تومان</p>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <img src="{{ asset('images/no-data.svg') }}" alt="بدون داده" style="width: 120px; height: 120px;">
                                    <div class="mt-3 text-muted">{{ __('هیچ تراکنشی یافت نشد') }}</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($transactions->hasPages())
            <div class="card-footer bg-white">
                {{ $transactions->appends(request()->except('page'))->links() }}
            </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // نمایش/مخفی کردن جزئیات تراکنش
        $('.transaction-details-btn').on('click', function() {
            const id = $(this).data('id');
            $('#details-' + id).toggleClass('show');
        });

        // اعمال فیلترها با تغییر مقادیر
        $('#type, #status, #currency_id').on('change', function() {
            $('#filter-form').submit();
        });
    });
</script>
@endsection
