@extends('admin.layouts.app')

@section('content')
    <div class="main-content">
        <div class="container-fluid">
            <div class="page-title-box">
                <div class="row align-items-center">
                    <div class="col-sm-6">
                        <h2 class="page-title">{{ $title }}</h2>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            {{-- اضافه کردن بخش نمایش پیام‌ها --}}
                            @if(session()->has('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session()->get('success') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif
                            
                            @if(session()->has('dismiss'))
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    {{ session()->get('dismiss') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif
                            
                            {{-- ادامه فرم و محتوای صفحه --}}
                            <form action="{{ route('admin.deposit.submitCheckDeposit') }}" method="get">
                                <div class="row">
                                    <div class="col-md-6 col-lg-4">
                                        <div class="form-group">
                                            <div class="controls">
                                                <div class="form-label">{{ __('API ارز') }}</div>
                                                <div class="cp-select-area">
                                                    <select name="network" id="network_id" class="form-control h-50">
                                                        <option>{{ __("انتخاب شبکه") }}</option>
                                                        @foreach($networks as $net)
                                                            <option @if(isset($network) && $network == $net->id) selected @endif value="{{$net->id}}">{{$net->name}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <small>{{ __('لطف_DIPSETTING که API ارز شما صحیح است. شما هرگز نمی‌توانید این API را تغییر دهید، پس مراقب باشید') }}</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-4">
                                        <div class="form-group">
                                            <div class="controls">
                                                <div class="form-label">{{ __('نوع ارز') }}</div>
                                                <div class="cp-select-area">
                                                    <select name="coin_network" id="coin_network" class="form-control h-50">
                                                        <option value="">{{ __("انتخاب ارز") }}</option>
                                                        @if(isset($currencies))
                                                            @foreach($currencies as $currency)
                                                                <option value="{{ $currency->id }}" 
                                                                    @if(isset($coin_network) && $coin_network == $currency->id) selected @endif>
                                                                    {{ $currency->coin->coin_type }}
                                                                </option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-4">
                                        <div class="form-group">
                                            <div class="controls">
                                                <div class="form-label">{{ __('شناسه تراکنش') }}</div>
                                                <input type="text" class="form-control h-50" style="height: 38px !important" name="transaction_id"
                                                    value="{{ isset($transaction_id) ? $transaction_id : old('transaction_id') }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-2">
                                        <input type="hidden" name="type" value="{{ CHECK_DEPOSIT }}">
                                        <button type="submit" class="btn theme-btn">{{ __('ثبت') }}</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mt-4">
                    @if (isset($transaction_id))
                        <div class="profile-info custom-box-shadow p-3">
                            <h4 class="text-center text-warning">{{ __('جزئیات تراکنش') }}</h4>
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <tbody>
                                        <tr>
                                            <td>{{ __('ارز تراکنش') }}</td>
                                            <td>:</td>
                                            <td><span>{{ $coin_type ?? '' }}</span></td>
                                        </tr>
                                        <tr>
                                            <td>{{ __('هش تراکنش') }}</td>
                                            <td>:</td>
                                            <td><span>{{ $transaction_id ?? '' }}</span></td>
                                        </tr>
                                        <tr>
                                            <td>{{ __('آدرس') }}</td>
                                            <td>:</td>
                                            <td><span>{{ $address ?? '' }}</span></td>
                                        </tr>
                                        <tr>
                                            <td>{{ __('مقدار') }}</td>
                                            <td>:</td>
                                            <td><span>{{ $amount ?? '' }} {{ $coin_type ?? '' }}</span></td>
                                        </tr>
                                        <tr>
                                            <td>{{ __('تأییدیه‌ها') }}</td>
                                            <td>:</td>
                                            <td><span>{{ $confirmations ?? '' }}</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <form action="{{ route('admin.deposit.submitCheckDeposit') }}" method="get">
                                <div class="row">
                                    <div class="col-md-12">
                                        <p class="text-warning">
                                            {{ __('اگر واریز با این شناسه تراکنش پیدا نشد، می‌توانید با کلیک بر روی دکمه زیر واریز را تنظیم کنید') }}
                                        </p>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="hidden" name="type" value="{{ ADJUST_DEPOSIT }}">
                                        <input type="hidden" name="transaction_id" value="{{isset($transaction_id) ? $transaction_id : ''}}">
                                        <input type="hidden" name="coin_network" value="{{isset($coin_network) ? $coin_network : ''}}">
                                        <input type="hidden" name="coin_type" value="{{isset($coin_type) ? $coin_type : ''}}">
                                        <input type="hidden" name="network" value="{{isset($network) ? $network : ''}}">
                                        <button type="submit" class="btn theme-btn">{{__('تنظیم واریز')}}</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script>
    // اضافه کردن کد برای بستن خودکار alert‌ها بعد از 5 ثانیه
    setTimeout(function() {
        $('.alert').fadeOut('slow', function() {
            $(this).remove();
        });
    }, 5000);
</script>
@endpush
