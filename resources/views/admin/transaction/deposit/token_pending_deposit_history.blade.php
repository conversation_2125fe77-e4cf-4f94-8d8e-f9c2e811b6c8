@extends('admin.layouts.app')

@push('styles')
<style>
    /* استایل‌های سفارشی برای دکمه‌های عملیات */
    .action-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
    }

    .action-buttons .deleteuser {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .action-buttons .deleteuser a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f8fafc;
        border: 2px solid #eef2f7;
        transition: all 0.3s ease;
    }

    .action-buttons .deleteuser a:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    /* اطمینان از نمایش درست در دیتاتیبل */
    .dataTables_wrapper .dataTables_scroll {
        overflow: visible !important;
    }

    /* استایل برای ستون عملیات */
    table.dataTable th.all {
        min-width: 80px;
    }

    /* استایل برای ستون عملیات */
    .actions-column {
        min-width: 80px !important;
        width: 80px !important;
        text-align: center !important;
    }

    /* استایل برای آدرس‌ها و هش تراکنش */
    .address-text, .tx-hash {
        max-width: 150px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        display: inline-block;
        font-family: monospace;
        background-color: #f8f9fa;
        padding: 3px 8px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        font-size: 0.85rem;
    }

    .address-text:hover, .tx-hash:hover {
        background-color: #e9ecef;
    }

    /* تنظیم عرض ستون‌ها */
    table.dataTable th:nth-child(3),
    table.dataTable th:nth-child(4),
    table.dataTable th:nth-child(5) {
        max-width: 150px;
    }

    table.dataTable td:nth-child(3),
    table.dataTable td:nth-child(4),
    table.dataTable td:nth-child(5) {
        max-width: 150px;
    }
</style>
@endpush

@section('content')
    <!-- breadcrumb -->
    <div class="custom-breadcrumb">
        <div class="row">
            <div class="col-9">
                <ul>
                    <li>{{ __('واریز توکن') }}</li>
                    <li class="active-item">{{ $title }}</li>
                </ul>
            </div>
        </div>
    </div>
    <!-- /breadcrumb -->

    <!-- مدیریت کاربران -->
    <div class="user-management">
        <div class="row">
            <div class="col-12">
                <div class="header-bar">
                    <div>
                        <div class="p-3 custom-box-shadow">
                            <h5 style="color: #cbcfd7">{{ __('زمانی که کاربر توکن ERC20/BEP20/TRC20 واریز می‌کند، باید آن را به آدرس ادمین منتقل کنیم، زیرا هنگام برداشت کاربر، توکن از آدرس ادمین ارسال خواهد شد') }}</h5>
                        </div>
                        <div class="p-3 custom-box-shadow mt-4">
                            <h5 class="text-danger">{{ __('مرحله 1: ') }}:
                                {{ __('ابتدا هزینه گس تخمینی را به آدرس کاربر ارسال کنید') }}</h5>
                            <h5 class="text-danger">{{ __('مرحله 2: ') }}:
                                {{ __('سپس توکن را از آدرس کاربر به آدرس ادمین ارسال کنید') }}</h5>
                        </div>
                        <div class="p-3 custom-box-shadow mt-4">
                            <h5 class="text-success">
                                {{ __('شما فقط رکورد را از طریق اکشن تایید می‌کنید، ما همه چیز را در پس‌زمینه مدیریت خواهیم کرد') }}
                            </h5>
                        </div>
                        <div class="p-3 custom-box-shadow mt-4">
                            <h5 class="text-warning">
                                {{ __('نکته: اگر این فرآیند تایید دستی را نادیده بگیرید، می‌توانید از یک دستور استفاده کنید که به طور خودکار این کار را انجام می‌دهد. دستور "adjust-token-deposit" است که می‌توانید همیشه در پس‌زمینه اجرا کنید') }}
                            </h5>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-area">
                        <div>
                            <table id="table" class="table table-borderless custom-table display text-center" width="100%">
                                <thead>
                                    <tr>
                                        <th scope="col">{{ __('مقدار') }}</th>
                                        <th scope="col">{{ __('ارز') }}</th>
                                        <th scope="col">{{ __('از آدرس') }}</th>
                                        <th scope="col">{{ __('به آدرس') }}</th>
                                        <th scope="col">{{ __('هش تراکنش') }}</th>
                                        <th scope="col">{{ __('وضعیت') }}</th>
                                        <th scope="col">{{ __('تاریخ ایجاد') }}</th>
                                        <th class="all" scope="col">{{ __('عملیات') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if ($buy_token)
        <!-- تاریخچه خرید توکن ICO -->
        <div class="user-management">
            <div class="row">
                <div class="col-12">
                    <div class="header-bar p-4">
                        <div class="table-title">
                            <h3>{{ __('تاریخچه تراکنش خرید توکن') }}</h3>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-area">
                            <div>
                                <table id="ico-buy-table" class="table table-borderless custom-table display text-center" width="100%">
                                    <thead>
                                        <tr>
                                            <th scope="col">{{ __('مقدار') }}</th>
                                            <th scope="col">{{ __('ارز') }}</th>
                                            <th scope="col">{{ __('از آدرس') }}</th>
                                            <th scope="col">{{ __('به آدرس') }}</th>
                                            <th scope="col">{{ __('هش تراکنش') }}</th>
                                            <th scope="col">{{ __('وضعیت') }}</th>
                                            <th scope="col">{{ __('تاریخ ایجاد') }}</th>
                                            <th class="all" scope="col">{{ __('عملیات') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        "use strict";

        $('#table').DataTable({
            processing: true,
            serverSide: true,
            pageLength: 10,
            retrieve: true,
            bLengthChange: true,
            responsive: true,
            ajax: {
                url: '{{ route('admin.deposit.pending-history') }}',
                type: 'GET',
                error: function (xhr, error, thrown) {
                    console.error('DataTables error:', error);
                }
            },
            order: [6, 'desc'],
            autoWidth: false,
            scrollX: false,
            scrollCollapse: true,
            headerCallback: function(thead, data, start, end, display) {
                if (data?.length == 0) {
                    $(thead).parent().parent().parent().addClass("width-full")
                    $(thead).parent().parent().addClass("width-full")
                }
            },
            language: {
                paginate: {
                    next: '<i class="fa fa-angle-double-right" aria-hidden="true"></i>',
                    previous: '<i class="fa fa-angle-double-left" aria-hidden="true"></i>'
                },
                processing: '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">در حال بارگذاری...</span>',
                emptyTable: 'داده‌ای موجود نیست',
                info: 'نمایش _START_ تا _END_ از _TOTAL_ رکورد',
                infoEmpty: 'رکوردی یافت نشد',
                search: 'جستجو:',
                lengthMenu: 'نمایش _MENU_ رکورد',
            },
            columns: [
                {"data": "amount", "orderable": true},
                {"data": "coin_type", "orderable": true},
                {"data": "from_address", "orderable": true},
                {"data": "address", "orderable": true},
                {"data": "transaction_id", "orderable": false},
                {"data": "status", "orderable": false},
                {"data": "created_at", "orderable": true},
                {"data": "actions", "orderable": false, "className": "actions-column"},
            ]
        });

        console.log('Initializing ico-buy-table...');

        let icoTable = $('#ico-buy-table').DataTable({
            processing: true,
            serverSide: true,
            pageLength: 10,
            retrieve: true,
            bLengthChange: true,
            responsive: true,
            ajax: {
                url: '{{ route('admin.deposit.icoTokenBuyListAccept') }}',
                type: 'GET',
                error: function (xhr, error, thrown) {
                    console.error('DataTables error:', error);
                    console.error('XHR:', xhr);
                    console.error('Thrown:', thrown);
                },
                complete: function(response) {
                    console.log('Ajax response:', response);
                }
            },
            order: [6, 'desc'],
            autoWidth: false,
            scrollX: false,
            scrollCollapse: true,
            headerCallback: function(thead, data, start, end, display) {
                if (data?.length == 0) {
                    $(thead).parent().parent().parent().addClass("width-full")
                    $(thead).parent().parent().addClass("width-full")
                }
            },
            language: {
                paginate: {
                    next: '<i class="fa fa-angle-double-right" aria-hidden="true"></i>',
                    previous: '<i class="fa fa-angle-double-left" aria-hidden="true"></i>'
                },
                processing: '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">در حال بارگذاری...</span>',
                emptyTable: 'داده‌ای موجود نیست',
                info: 'نمایش _START_ تا _END_ از _TOTAL_ رکورد',
                infoEmpty: 'رکوردی یافت نشد',
                search: 'جستجو:',
                lengthMenu: 'نمایش _MENU_ رکورد',
            },
            columns: [
                {"data": "amount", "orderable": true},
                {"data": "coin_type", "orderable": true},
                {"data": "from_address", "orderable": true},
                {"data": "address", "orderable": true},
                {"data": "transaction_id", "orderable": false},
                {"data": "status", "orderable": false},
                {"data": "created_at", "orderable": true},
                {"data": "actions", "orderable": false, "className": "actions-column"},
            ]
        });

        // Refresh table every 30 seconds
        setInterval(function() {
            icoTable.ajax.reload(null, false);
        }, 30000);

        // Handle modal actions
        $(document).on('click', '[data-toggle="modal"]', function(e) {
            e.preventDefault();
            var targetModal = $(this).attr('href');
            $(targetModal).modal('show');
        });

        // Handle accept action
        $(document).on('click', '.modal-accept-btn', function(e) {
            e.preventDefault();
            var form = $(this).closest('form');
            form.submit();
        });

        // نمایش متن کامل آدرس‌ها و هش تراکنش در کلیک
        $(document).on('click', '.address-text, .tx-hash', function() {
            var fullText = $(this).attr('title');
            var type = $(this).hasClass('address-text') ? 'آدرس' : 'هش تراکنش';

            // ایجاد مودال برای نمایش متن کامل
            var modal = `
                <div class="modal fade" id="textModal" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${type} کامل</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="form-group">
                                    <textarea class="form-control text-monospace" rows="3" readonly>${fullText}</textarea>
                                </div>
                                <button class="btn btn-sm btn-primary copy-btn" data-text="${fullText}">
                                    <i class="fa fa-copy ml-1"></i> کپی
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // حذف مودال قبلی اگر وجود داشته باشد
            $('#textModal').remove();

            // اضافه کردن مودال جدید به صفحه و نمایش آن
            $('body').append(modal);
            $('#textModal').modal('show');
        });

        // کپی کردن متن
        $(document).on('click', '.copy-btn', function() {
            var text = $(this).data('text');
            var tempInput = $('<input>');
            $('body').append(tempInput);
            tempInput.val(text).select();
            document.execCommand('copy');
            tempInput.remove();

            // نمایش پیام موفقیت
            $(this).html('<i class="fa fa-check ml-1"></i> کپی شد');
            setTimeout(() => {
                $(this).html('<i class="fa fa-copy ml-1"></i> کپی');
            }, 2000);
        });
    });
</script>
@endpush
