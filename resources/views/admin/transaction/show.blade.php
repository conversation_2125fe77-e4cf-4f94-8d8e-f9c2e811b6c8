@extends('admin.layouts.app')

@section('styles')
<style>
    .transaction-card {
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0,0,0,0.05);
        margin-bottom: 30px;
    }
    .transaction-header {
        background-color: #f8f9fa;
        border-radius: 10px 10px 0 0;
        padding: 20px;
        border-bottom: 1px solid #eee;
    }
    .transaction-body {
        padding: 20px;
    }
    .transaction-info-item {
        margin-bottom: 20px;
    }
    .transaction-info-item label {
        font-weight: bold;
        color: #6c757d;
        margin-bottom: 5px;
        display: block;
    }
    .transaction-info-item p {
        margin: 0;
        font-size: 1rem;
    }
    .transaction-status {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        display: inline-block;
    }
    .transaction-actions {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }
    .detail-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .detail-section h5 {
        margin-bottom: 15px;
        color: #495057;
        font-weight: 600;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2>{{ __('جزئیات تراکنش') }}</h2>
                <div>
                    <a href="{{ route('admin.transaction.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right ml-1"></i> {{ __('بازگشت به لیست') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- کارت اصلی تراکنش -->
            <div class="card transaction-card">
                <div class="transaction-header d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0">{{ __('تراکنش #:id', ['id' => $transaction->id]) }}</h4>
                        <div class="text-muted">{{ \Hekmatinasser\Verta\Verta::instance($transaction->created_at)->format('Y/m/d H:i:s') }}</div>
                    </div>
                    <div>
                        <span class="transaction-status
                            @if($transaction->status == 'done') bg-success text-white
                            @elseif($transaction->status == 'pending') bg-warning text-dark
                            @elseif($transaction->status == 'rejected') bg-danger text-white
                            @else bg-secondary text-white @endif">
                            {{ $transaction->status }}
                        </span>
                    </div>
                </div>
                <div class="transaction-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="transaction-info-item">
                                <label>{{ __('نوع تراکنش') }}</label>
                                <p>{{ $transaction->type_description }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="transaction-info-item">
                                <label>{{ __('مبلغ') }}</label>
                                <p>{{ number_format($transaction->amount, 8) }} {{ $transaction->currency->coin_type ?? '' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="transaction-info-item">
                                <label>{{ __('کاربر') }}</label>
                                <p>{{ $transaction->user ? $transaction->user->name : 'N/A' }} ({{ $transaction->user ? $transaction->user->email : 'N/A' }})</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="transaction-info-item">
                                <label>{{ __('ثبت کننده') }}</label>
                                <p>{{ $transaction->registrar ? $transaction->registrar->name : 'N/A' }} ({{ $transaction->registrar ? $transaction->registrar->email : 'N/A' }})</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="transaction-info-item">
                                <label>{{ __('ارز') }}</label>
                                <p>{{ $transaction->currency ? $transaction->currency->name : 'N/A' }} ({{ $transaction->currency ? $transaction->currency->coin_type : 'N/A' }})</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="transaction-info-item">
                                <label>{{ __('کیف پول') }}</label>
                                <p>{{ $transaction->wallet ? $transaction->wallet->name : 'N/A' }} (ID: {{ $transaction->wallet_id ?? 'N/A' }})</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="transaction-info-item">
                                <label>{{ __('موجودی قبل') }}</label>
                                <p>{{ number_format($transaction->balance_before, 8) }} {{ $transaction->currency->coin_type ?? '' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="transaction-info-item">
                                <label>{{ __('موجودی بعد') }}</label>
                                <p>{{ number_format($transaction->balance_after, 8) }} {{ $transaction->currency->coin_type ?? '' }}</p>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="transaction-info-item">
                                <label>{{ __('توضیحات') }}</label>
                                <p>{{ $transaction->description ?? 'بدون توضیحات' }}</p>
                            </div>
                        </div>
                    </div>

                    @if($transaction->type == 'swap_out' || $transaction->type == 'swap_in')
                        <!-- جزئیات تبدیل ارز -->
                        <div class="detail-section">
                            <h5>{{ __('جزئیات تبدیل ارز') }}</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('ارز مبدأ') }}</label>
                                        <p>{{ $transaction->swap_details['from_currency'] ?? 'N/A' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('ارز مقصد') }}</label>
                                        <p>{{ $transaction->swap_details['to_currency'] ?? 'N/A' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('مقدار مبدأ') }}</label>
                                        <p>{{ number_format($transaction->swap_details['from_amount'] ?? 0, 8) }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('مقدار مقصد') }}</label>
                                        <p>{{ number_format($transaction->swap_details['to_amount'] ?? 0, 8) }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('کارمزد') }}</label>
                                        <p>{{ $transaction->swap_details['fee_percentage'] ?? '0%' }} ({{ number_format($transaction->swap_details['fee_amount'] ?? 0, 8) }})</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('ارزش دلاری') }}</label>
                                        <p>${{ number_format($transaction->swap_details['usd_value'] ?? 0, 2) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @elseif($transaction->type == 'buy')
                        <!-- جزئیات خرید -->
                        <div class="detail-section">
                            <h5>{{ __('جزئیات خرید') }}</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('مبلغ تومان') }}</label>
                                        <p>{{ number_format($transaction->buy_details['toman_amount'] ?? 0) }} تومان</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('مبلغ دلار') }}</label>
                                        <p>${{ number_format($transaction->buy_details['usd_amount'] ?? 0, 2) }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('نرخ دلار') }}</label>
                                        <p>{{ number_format($transaction->buy_details['usd_rate'] ?? 0) }} تومان</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('مقدار ارز') }}</label>
                                        <p>{{ number_format($transaction->buy_details['crypto_amount'] ?? 0, 8) }} {{ $transaction->currency->coin_type ?? '' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @elseif($transaction->type == 'sell')
                        <!-- جزئیات فروش -->
                        <div class="detail-section">
                            <h5>{{ __('جزئیات فروش') }}</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('مقدار ارز') }}</label>
                                        <p>{{ number_format($transaction->sell_details['crypto_amount'] ?? 0, 8) }} {{ $transaction->currency->coin_type ?? '' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('مبلغ دلار') }}</label>
                                        <p>${{ number_format($transaction->sell_details['usd_amount'] ?? 0, 2) }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('نرخ دلار') }}</label>
                                        <p>{{ number_format($transaction->sell_details['usd_rate'] ?? 0) }} تومان</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="transaction-info-item">
                                        <label>{{ __('مبلغ تومان') }}</label>
                                        <p>{{ number_format($transaction->sell_details['toman_amount'] ?? 0) }} تومان</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @elseif($transaction->transaction_details)
                        <!-- سایر جزئیات -->
                        <div class="detail-section">
                            <h5>{{ __('جزئیات اضافی') }}</h5>
                            <div class="row">
                                @foreach($transaction->transaction_details as $key => $value)
                                    <div class="col-md-6">
                                        <div class="transaction-info-item">
                                            <label>{{ $key }}</label>
                                            <p>{{ is_array($value) ? json_encode($value) : $value }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- بخش عملیات -->
                    <div class="transaction-actions">
                        <form action="{{ route('admin.transaction.update', $transaction->id) }}" method="POST">
                            @csrf
                            <div class="form-group">
                                <label for="status">{{ __('تغییر وضعیت') }}</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="done" {{ $transaction->status == 'done' ? 'selected' : '' }}>{{ __('انجام شده') }}</option>
                                    <option value="pending" {{ $transaction->status == 'pending' ? 'selected' : '' }}>{{ __('در انتظار') }}</option>
                                    <option value="rejected" {{ $transaction->status == 'rejected' ? 'selected' : '' }}>{{ __('رد شده') }}</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="description">{{ __('توضیحات') }}</label>
                                <textarea class="form-control" id="description" name="description" rows="3">{{ $transaction->description }}</textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">{{ __('ذخیره تغییرات') }}</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- کارت اطلاعات کاربر -->
            <div class="card transaction-card">
                <div class="transaction-header">
                    <h5 class="mb-0">{{ __('اطلاعات کاربر') }}</h5>
                </div>
                <div class="transaction-body">
                    <div class="text-center mb-4">
                        <img src="{{ $transaction->user->avatar ?? asset('images/default-avatar.png') }}"
                             class="rounded-circle" style="width: 100px; height: 100px;">
                        <h5 class="mt-3 mb-0">{{ $transaction->user->name ?? 'N/A' }}</h5>
                        <p class="text-muted">{{ $transaction->user->email ?? 'N/A' }}</p>
                    </div>

                    <div class="transaction-info-item">
                        <label>{{ __('شناسه کاربر') }}</label>
                        <p>{{ $transaction->user_id }}</p>
                    </div>
                    <div class="transaction-info-item">
                        <label>{{ __('تاریخ عضویت') }}</label>
                        <p>{{ $transaction->user->created_at ? \Hekmatinasser\Verta\Verta::instance($transaction->user->created_at)->format('Y/m/d') : 'N/A' }}</p>
                    </div>
                    <div class="transaction-info-item">
                        <label>{{ __('وضعیت') }}</label>
                        <p>
                            <span class="badge {{ $transaction->user->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                {{ $transaction->user->status ?? 'N/A' }}
                            </span>
                        </p>
                    </div>

                    <div class="mt-4">
                        <a href="{{ route('admin.users.show', $transaction->user_id) }}" class="btn btn-info btn-block">
                            <i class="fas fa-user mr-1"></i> {{ __('مشاهده پروفایل کاربر') }}
                        </a>
                        <a href="{{ route('admin.transaction.index', ['user_id' => $transaction->user_id]) }}" class="btn btn-secondary btn-block mt-2">
                            <i class="fas fa-history mr-1"></i> {{ __('تراکنش‌های کاربر') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- کارت اطلاعات کیف پول -->
            @if($transaction->wallet)
            <div class="card transaction-card">
                <div class="transaction-header">
                    <h5 class="mb-0">{{ __('اطلاعات کیف پول') }}</h5>
                </div>
                <div class="transaction-body">
                    <div class="transaction-info-item">
                        <label>{{ __('نام کیف پول') }}</label>
                        <p>{{ $transaction->wallet->name }}</p>
                    </div>
                    <div class="transaction-info-item">
                        <label>{{ __('ارز') }}</label>
                        <p>{{ $transaction->wallet->coin->name ?? 'N/A' }} ({{ $transaction->wallet->coin->coin_type ?? 'N/A' }})</p>
                    </div>
                    <div class="transaction-info-item">
                        <label>{{ __('موجودی فعلی') }}</label>
                        <p>{{ number_format($transaction->wallet->balance, 8) }} {{ $transaction->wallet->coin->coin_type ?? '' }}</p>
                    </div>
                    <div class="transaction-info-item">
                        <label>{{ __('وضعیت') }}</label>
                        <p>
                            <span class="badge {{ $transaction->wallet->status == 'active' ? 'bg-success' : 'bg-danger' }}">
                                {{ $transaction->wallet->status }}
                            </span>
                        </p>
                    </div>

                    <div class="mt-4">
                        <a href="{{ route('admin.wallet.show', $transaction->wallet_id) }}" class="btn btn-info btn-block">
                            <i class="fas fa-wallet mr-1"></i> {{ __('مشاهده کیف پول') }}
                        </a>
                        <a href="{{ route('admin.transaction.index', ['wallet_id' => $transaction->wallet_id]) }}" class="btn btn-secondary btn-block mt-2">
                            <i class="fas fa-history mr-1"></i> {{ __('تراکنش‌های کیف پول') }}
                        </a>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
