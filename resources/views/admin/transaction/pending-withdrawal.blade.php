
@extends('admin.layouts.app')

@section('styles')
<style>
    .user-info-item {
        margin-bottom: 15px;
    }

    .user-info-item label {
        font-weight: bold;
        color: #6c757d;
        margin-bottom: 5px;
        display: block;
    }

    .user-info-item p {
        margin: 0;
        font-size: 0.95rem;
    }

    .stats-card {
        border: none;
        border-radius: 10px;
    }

    .stats-card .card-body {
        padding: 1.5rem;
    }
    .stats-card {
        border-radius: 10px;
        transition: all 0.3s;
    }
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .filter-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
    }
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.02);
    }
    .status-badge {
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }
    .amount-col {
        font-family: monospace;
        font-weight: 600;
    }
    .action-buttons .btn {
        border-radius: 20px;
        padding: 5px 15px;
        margin: 0 3px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- کارت‌های آمار -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-primary text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('تعداد در انتظار') }}</h5>
                    <h2 class="mb-0">{{ $stats['total_pending'] ?? 0 }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-success text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('مجموع مبلغ') }}</h5>
                    <h2 class="mb-0">{{ number_format($stats['total_amount'] ?? 0, 2) }}</h2>
                    <div class="small text-white-50">تتر</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-warning text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('میانگین مبلغ') }}</h5>
                    <h2 class="mb-0">{{ number_format($stats['avg_amount'] ?? 0, 2) }}</h2>
                    <div class="small text-white-50">تتر</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-info text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('کاربران منحصر به فرد') }}</h5>
                    <h2 class="mb-0">{{ $stats['unique_users'] ?? 0 }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- اطلاعات کاربر -->
    @if(isset($user))
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">{{ __('اطلاعات کاربر') }}</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('نام و نام خانوادگی') }}:</label>
                        <p>{{ $user->firstname }} {{ $user->lastname }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('کد ملی') }}:</label>
                        <p>{{ $user->national_id }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('ایمیل') }}:</label>
                        <p>{{ $user->email }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('شماره تماس') }}:</label>
                        <p>{{ $user->phone }}</p>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('جنسیت') }}:</label>
                        <p>{{ __($user->gender) }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('تاریخ تولد') }}:</label>
                        <p>{{ $user->birth_date ? \Hekmatinasser\Verta\Verta::instance($user->birth_date)->format('Y/m/d') : '-' }}</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('وضعیت') }}:</label>
                        <p><span class="badge badge-{{ $user->status === 'active' ? 'success' : 'warning' }}">{{ __($user->status) }}</span></p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="user-info-item">
                        <label>{{ __('تاریخ عضویت') }}:</label>
                        <p>{{ \Hekmatinasser\Verta\Verta::instance($user->created_at)->format('Y/m/d H:i') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- فیلترها -->
    <div class="card filter-card">
        <form method="GET" action="{{ route('admin.withdrawal.adminPendingWithdrawal') }}">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>{{ __('نوع ارز') }}</label>
                        <select name="coin_type" class="form-control">
                            <option value="">{{ __('همه') }}</option>
                            @foreach($coinTypes as $coin)
                                <option value="{{ $coin }}" {{ request('coin_type') == $coin ? 'selected' : '' }}>
                                    {{ $coin }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>{{ __('شبکه') }}</label>
                        <select name="network" class="form-control">
                            <option value="">{{ __('همه') }}</option>
                            @foreach($networks as $network)
                                <option value="{{ $network }}" {{ request('network') == $network ? 'selected' : '' }}>
                                    {{ $network }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>{{ __('حداقل مبلغ') }}</label>
                        <input type="number" name="min_amount" class="form-control" value="{{ request('min_amount') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>{{ __('حداکثر مبلغ') }}</label>
                        <input type="number" name="max_amount" class="form-control" value="{{ request('max_amount') }}">
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-left">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> {{ __('فیلتر') }}
                    </button>
                    <a href="{{ route('admin.withdrawal.adminPendingWithdrawal') }}" class="btn btn-secondary">
                        <i class="fas fa-redo"></i> {{ __('بازنشانی') }}
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- جدول اصلی -->
    <div class="card">
        <div class="card-header bg-white">
            <div class="row align-items-center">
                <div class="col">
                    <h4 class="mb-0">{{ __('برداشت‌های در انتظار') }}</h4>
                </div>
                <div class="col text-left">
                    <button class="btn btn-sm btn-success" onclick="return confirm('{{ __('آیا از تایید همه موارد انتخاب شده مطمئن هستید؟') }}')">
                        <i class="fas fa-check-circle"></i> {{ __('تایید انتخاب شده‌ها') }}
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="return confirm('{{ __('آیا از رد همه موارد انتخاب شده مطمئن هستید؟') }}')">
                        <i class="fas fa-times-circle"></i> {{ __('رد انتخاب شده‌ها') }}
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="thead-light">
                        <tr>
                            <th width="20">
                                <input type="checkbox" id="select-all">
                            </th>
                            <th>{{ __('کاربر') }}</th>
                            <th>{{ __('مبلغ') }}</th>
                            <th>{{ __('ارز') }}</th>
                            <th>{{ __('شبکه') }}</th>
                            <th>{{ __('آدرس') }}</th>
                            <th>{{ __('تاریخ ایجاد') }}</th>
                            <th>{{ __('عملیات') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($withdrawals as $withdrawal)
                            <tr>
                                <td>
                                    <input type="checkbox" class="withdrawal-checkbox" value="{{ $withdrawal->id }}">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm ml-3">
                                            <img src="{{ $withdrawal->user->avatar ?? asset('images/default-avatar.png') }}"
                                                 class="avatar-img rounded-circle">
                                        </div>
                                        <div>
                                            <strong>{{ $withdrawal->user->email }}</strong>
                                            <div class="small text-muted">شناسه: {{ $withdrawal->user->id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="amount-col">
                                    {{ number_format($withdrawal->amount, 8) }}
                                </td>
                                <td>
                                    <span class="badge badge-soft-primary">
                                        {{ $withdrawal->coin_type }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-soft-info">
                                        {{ $withdrawal->network_type }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <code class="ml-2">{{ Str::limit($withdrawal->address, 20) }}</code>
                                        <button class="btn btn-sm btn-link p-0" onclick="copyToClipboard('{{ $withdrawal->address }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <div>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i') }}</div>
                                    <div class="small text-muted">
                                        {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->formatDifference() }}
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <form action="{{ route('admin.withdrawal.adminAcceptPendingWithdrawal', $withdrawal->id) }}"
                                              method="GET"
                                              style="display: inline;">
                                            @csrf
                                            <button type="submit"
                                                    class="btn btn-sm btn-success"
                                                    onclick="return confirm('{{ __('آیا از تایید این برداشت مطمئن هستید؟') }}')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>

                                        <form action="{{ route('admin.withdrawal.adminRejectPendingWithdrawal', encrypt($withdrawal->id)) }}"
                                              method="GET"
                                              style="display: inline;">
                                            @csrf
                                            <button type="submit"
                                                    class="btn btn-sm btn-danger"
                                                    onclick="return confirm('{{ __('آیا از رد این برداشت مطمئن هستید؟') }}')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>

                                        <a href="{{ route('admin.withdrawal.adminWithdrawalDetails', $withdrawal->id) }}"
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <img src="{{ asset('images/no-data.svg') }}" alt="بدون داده" style="width: 120px; height: 120px;">
                                    <div class="mt-3 text-muted">{{ __('هیچ برداشت در انتظاری یافت نشد') }}</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($withdrawals->hasPages())
            <div class="card-footer bg-white">
                {{ $withdrawals->links() }}
            </div>
        @endif
    </div>
</div>


@endsection

@push('scripts')
<script>
document.getElementById('select-all').addEventListener('change', function() {
    document.querySelectorAll('.withdrawal-checkbox').forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        alert('کپی شد!');
    });
}
</script>
@endpush
