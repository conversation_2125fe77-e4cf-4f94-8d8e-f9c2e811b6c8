@extends('admin.layouts.app')

@section('styles')
<style>
    .detail-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 25px;
    }
    
    .detail-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 20px;
    }
    
    .detail-card .card-body {
        padding: 30px;
    }
    
    .info-row {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #007bff;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
        font-size: 14px;
    }
    
    .info-value {
        font-size: 16px;
        color: #212529;
        margin: 0;
    }
    
    .status-badge {
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 14px;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .status-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .status-rejected {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .copy-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .copy-btn:hover {
        background: #0056b3;
        transform: translateY(-1px);
    }
    
    .action-buttons .btn {
        margin: 0 5px;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
        color: #6c757d;
    }
    
    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .network-badge {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .amount-display {
        font-family: 'Courier New', monospace;
        font-size: 24px;
        font-weight: bold;
        color: #28a745;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('admin.dashboard') }}">{{ __('داشبورد') }}</a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ route('admin.withdrawal.adminPendingWithdrawal') }}">{{ __('برداشت‌های در انتظار') }}</a>
            </li>
            <li class="breadcrumb-item active">{{ __('جزئیات برداشت') }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- اطلاعات اصلی برداشت -->
        <div class="col-lg-8">
            <div class="card detail-card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-info-circle ml-2"></i>
                        {{ __('اطلاعات برداشت') }}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">{{ __('شناسه تراکنش') }}</div>
                                <div class="info-value">
                                    <code>{{ $withdrawal->transaction_hash ?: 'در انتظار تولید' }}</code>
                                    @if($withdrawal->transaction_hash)
                                        <button class="copy-btn mr-2" onclick="copyToClipboard('{{ $withdrawal->transaction_hash }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">{{ __('وضعیت') }}</div>
                                <div class="info-value">
                                    @if($withdrawal->status == 0)
                                        <span class="status-badge status-pending">{{ __('در انتظار') }}</span>
                                    @elseif($withdrawal->status == 1)
                                        <span class="status-badge status-success">{{ __('تایید شده') }}</span>
                                    @else
                                        <span class="status-badge status-rejected">{{ __('رد شده') }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">{{ __('مبلغ برداشت') }}</div>
                                <div class="info-value amount-display">
                                    {{ number_format($withdrawal->amount, 8) }} {{ $withdrawal->coin_type }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">{{ __('کارمزد شبکه') }}</div>
                                <div class="info-value">
                                    {{ number_format($withdrawal->fees, 8) }} {{ $withdrawal->coin_type }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">{{ __('نوع ارز') }}</div>
                                <div class="info-value">
                                    <span class="badge badge-primary">{{ $withdrawal->coin_type }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">{{ __('شبکه') }}</div>
                                <div class="info-value">
                                    <span class="network-badge">{{ $withdrawal->network_type ?: $withdrawal->network->name ?? 'نامشخص' }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="info-row">
                                <div class="info-label">{{ __('آدرس مقصد') }}</div>
                                <div class="info-value">
                                    <code style="word-break: break-all;">{{ $withdrawal->address }}</code>
                                    <button class="copy-btn mr-2" onclick="copyToClipboard('{{ $withdrawal->address }}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        @if($withdrawal->memo)
                        <div class="col-12">
                            <div class="info-row">
                                <div class="info-label">{{ __('یادداشت/Memo') }}</div>
                                <div class="info-value">
                                    <code>{{ $withdrawal->memo }}</code>
                                </div>
                            </div>
                        </div>
                        @endif
                        
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">{{ __('تاریخ ایجاد') }}</div>
                                <div class="info-value">
                                    {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i:s') }}
                                    <div class="small text-muted">
                                        {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->formatDifference() }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        @if($withdrawal->updated_at != $withdrawal->created_at)
                        <div class="col-md-6">
                            <div class="info-row">
                                <div class="info-label">{{ __('آخرین بروزرسانی') }}</div>
                                <div class="info-value">
                                    {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->updated_at)->format('Y/m/d H:i:s') }}
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- اطلاعات کاربر -->
        <div class="col-lg-4">
            <div class="card detail-card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user ml-2"></i>
                        {{ __('اطلاعات کاربر') }}
                    </h4>
                </div>
                <div class="card-body text-center">
                    <img src="{{ $withdrawal->user->avatar ?? asset('images/default-avatar.png') }}" 
                         class="user-avatar mb-3" alt="User Avatar">
                    
                    <h5 class="mb-3">{{ $withdrawal->user->firstname ?? '' }} {{ $withdrawal->user->lastname ?? '' }}</h5>
                    
                    <div class="info-row text-right">
                        <div class="info-label">{{ __('ایمیل') }}</div>
                        <div class="info-value">{{ $withdrawal->user->email }}</div>
                    </div>
                    
                    <div class="info-row text-right">
                        <div class="info-label">{{ __('شناسه کاربر') }}</div>
                        <div class="info-value">#{{ $withdrawal->user->id }}</div>
                    </div>
                    
                    @if($withdrawal->user->phone)
                    <div class="info-row text-right">
                        <div class="info-label">{{ __('شماره تماس') }}</div>
                        <div class="info-value">{{ $withdrawal->user->phone }}</div>
                    </div>
                    @endif
                    
                    <div class="info-row text-right">
                        <div class="info-label">{{ __('تاریخ عضویت') }}</div>
                        <div class="info-value">
                            {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->user->created_at)->format('Y/m/d') }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- عملیات -->
            @if($withdrawal->status == 0)
            <div class="card detail-card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-cogs ml-2"></i>
                        {{ __('عملیات') }}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="action-buttons text-center">
                        <form action="{{ route('admin.withdrawal.adminAcceptPendingWithdrawal', $withdrawal->id) }}" 
                              method="GET" 
                              style="display: inline;">
                            @csrf
                            <button type="submit" 
                                    class="btn btn-success btn-block mb-3"
                                    onclick="return confirm('{{ __('آیا از تایید این برداشت مطمئن هستید؟') }}')">
                                <i class="fas fa-check ml-2"></i>
                                {{ __('تایید برداشت') }}
                            </button>
                        </form>

                        <form action="{{ route('admin.withdrawal.adminRejectPendingWithdrawal', encrypt($withdrawal->id)) }}" 
                              method="GET" 
                              style="display: inline;">
                            @csrf
                            <button type="submit" 
                                    class="btn btn-danger btn-block mb-3"
                                    onclick="return confirm('{{ __('آیا از رد این برداشت مطمئن هستید؟') }}')">
                                <i class="fas fa-times ml-2"></i>
                                {{ __('رد برداشت') }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- دکمه بازگشت -->
    <div class="row">
        <div class="col-12">
            <div class="text-center">
                <a href="{{ route('admin.withdrawal.adminPendingWithdrawal') }}" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-right ml-2"></i>
                    {{ __('بازگشت به لیست') }}
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        // نمایش پیام موفقیت
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
        });
        
        Toast.fire({
            icon: 'success',
            title: 'کپی شد!'
        });
    }).catch(() => {
        // fallback برای مرورگرهای قدیمی
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('کپی شد!');
    });
}
</script>
@endpush
