@extends('admin.layouts.app')

@section('title', 'عملیات برداشت تومانی')

@push('styles')
<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .detail-card {
        transition: all 0.3s;
    }

    .detail-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .bank-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-control:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }

    .btn-operation {
        border-radius: 5px;
        font-weight: 600;
        padding: 10px 20px;
        transition: all 0.3s;
    }

    .btn-operation:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    }

    .card-number, .sheba-number {
        font-family: monospace;
        letter-spacing: 1px;
        background-color: #f8f9fa;
        padding: 8px;
        border-radius: 5px;
        border: 1px solid #e9ecef;
    }

    .user-info-box {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .amount-box {
        background-color: #e8f4fe;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .bank-info-box {
        background-color: #f0f8ff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('عملیات برداشت تومانی') }}</h5>
                    <div>
                        <a href="{{ route('admin.toman-withdrawal.pending') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-right ml-1"></i> {{ __('بازگشت به لیست') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- اطلاعات کاربر -->
                        <div class="col-md-4">
                            <div class="card detail-card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-user-circle ml-1"></i> {{ __('اطلاعات کاربر') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="user-info-box">
                                        <div class="d-flex align-items-center mb-3">
                                            <div>
                                                <h5 class="mb-1">{{ $withdrawal->user->firstname ?? '' }} {{ $withdrawal->user->lastname ?? '' }}</h5>
                                                <span class="badge badge-info">شناسه: {{ $withdrawal->user->id }}</span>
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <i class="fas fa-envelope text-muted ml-1"></i>
                                            <strong>ایمیل:</strong>
                                            <span>{{ $withdrawal->user->email }}</span>
                                        </div>
                                        <div class="mb-2">
                                            <i class="fas fa-phone text-muted ml-1"></i>
                                            <strong>تلفن:</strong>
                                            <span>{{ $withdrawal->user->phone ?? 'ثبت نشده' }}</span>
                                        </div>
                                        <div>
                                            <i class="fas fa-id-card text-muted ml-1"></i>
                                            <strong>کد ملی:</strong>
                                            <span>{{ $withdrawal->user->national_id ?? 'ثبت نشده' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- اطلاعات مبلغ -->
                        <div class="col-md-4">
                            <div class="card detail-card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-money-bill-wave ml-1"></i> {{ __('اطلاعات مبلغ') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="amount-box">
                                        <div class="mb-3 text-center">
                                            <h4 class="text-success font-weight-bold">{{ number_format($withdrawal->amount) }} تومان</h4>
                                            <small class="text-muted">مبلغ درخواستی</small>
                                        </div>

                                        @if($withdrawal->fee > 0)
                                        <div class="mb-3">
                                            <strong>کارمزد:</strong>
                                            <span class="text-danger">{{ number_format($withdrawal->fee) }} تومان</span>
                                        </div>
                                        @endif

                                        <div class="mb-3">
                                            <strong>مبلغ خالص:</strong>
                                            <span>{{ number_format($withdrawal->amount - $withdrawal->fee) }} تومان</span>
                                        </div>

                                        <div>
                                            <strong>موجودی کاربر:</strong>
                                            <span>{{ number_format($withdrawal->user->toman_balance ?? 0) }} تومان</span>
                                        </div>

                                        <div>
                                            <strong>آی پی درخواست دهنده:</strong>
                                            <span>{{ $withdrawal->ip }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- اطلاعات بانکی -->
                        <div class="col-md-4">
                            <div class="card detail-card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-university ml-1"></i> {{ __('اطلاعات بانکی') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="bank-info-box">
                                        <div class="d-flex align-items-center mb-3">
                                            @if($withdrawal->card->bank->icon)
                                                <img src="{{ env('APP_URL').'/storage/' . $withdrawal->card->bank->icon}}" alt="{{ $withdrawal->card->bank->name }}" class="bank-icon ml-2">
                                            @endif
                                            <h5 class="mb-0">{{ $withdrawal->card->bank->name }}</h5>
                                        </div>

                                        <div class="mb-3">
                                            <strong>شماره کارت:</strong>
                                            <div class="card-number mt-1">
                                                {{ $withdrawal->card->number }}
                                            </div>
                                        </div>

                                        @if($withdrawal->card->sheba)
                                        <div>
                                            <strong>شماره شبا:</strong>
                                            <div class="sheba-number mt-1">
                                                {{ $withdrawal->card->sheba }}
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-tasks ml-1"></i> {{ __('عملیات') }}</h6>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('admin.toman-withdrawal.approve', $withdrawal->id) }}" method="POST">
                                        @csrf
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="form-group">
                                                    <label for="tracking_number">شماره پیگیری تراکنش:</label>
                                                    <input type="text" name="tracking_number" id="tracking_number" class="form-control @error('tracking_number') is-invalid @enderror" required>
                                                    @error('tracking_number')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <small class="form-text text-muted">شماره پیگیری تراکنش بانکی را وارد کنید.</small>
                                                </div>
                                            </div>
                                            <div class="col-md-4 d-flex align-items-end">
                                                <button type="submit" class="btn btn-success btn-operation btn-block">
                                                    <i class="fas fa-check-circle ml-1"></i> تایید و پرداخت
                                                </button>
                                            </div>
                                        </div>
                                    </form>

                                    <hr>

                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <button type="button" class="btn btn-danger btn-operation" id="openRejectModal">
                                                <i class="fas fa-times-circle ml-1"></i> رد درخواست
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال رد درخواست -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">{{ __('رد برداشت تومانی') }}</h5>
                <button type="button" class="close mr-auto ml-0" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.toman-withdrawal.reject', $withdrawal->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>آیا از رد این برداشت تومانی اطمینان دارید؟</p>
                    <div class="form-group">
                        <label for="reject_reason">دلیل رد درخواست:</label>
                        <textarea name="reject_reason" id="reject_reason" class="form-control" rows="3" required></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <strong>توجه:</strong> با رد این درخواست، مبلغ {{ number_format($withdrawal->amount) }} تومان به حساب کاربر بازگردانده خواهد شد.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">انصراف</button>
                    <button type="submit" class="btn btn-danger">رد درخواست</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // فرمت کردن شماره کارت
        $('.card-number').each(function() {
            let cardNumber = $(this).text().trim();
            if (cardNumber.length === 16) {
                let formattedNumber = cardNumber.match(/.{1,4}/g).join('-');
                $(this).text(formattedNumber);
            }
        });

        // انیمیشن برای کارت‌ها
        $('.detail-card').each(function(index) {
            $(this).css({
                'opacity': 0,
                'transform': 'translateY(20px)'
            });

            setTimeout(() => {
                $(this).animate({
                    'opacity': 1
                }, 300);
                $(this).css('transform', 'translateY(0)');
            }, 100 * index);
        });

        // باز کردن مودال رد درخواست با کلیک روی دکمه
        $('#openRejectModal').on('click', function() {
            $('#rejectModal').modal('show');
        });

        // اضافه کردن event listener برای دکمه انصراف
        $('.modal .btn-secondary[data-dismiss="modal"]').on('click', function() {
            $('#rejectModal').modal('hide');
        });

        // اضافه کردن event listener برای کلیک روی دکمه X
        $('.modal .close[data-dismiss="modal"]').on('click', function() {
            $('#rejectModal').modal('hide');
        });

        // اعتبارسنجی فرم رد درخواست
        $('#rejectModal form').on('submit', function(e) {
            if ($('#reject_reason').val().trim() === '') {
                e.preventDefault();
                alert('لطفا دلیل رد درخواست را وارد کنید');
                return false;
            }
            return true;
        });

        // بررسی وضعیت مودال برای debug
        console.log('Modal element exists:', $('#rejectModal').length > 0);
        console.log('jQuery version:', $.fn.jquery);
        console.log('Bootstrap modal function exists:', typeof $('#rejectModal').modal === 'function');
    });
</script>
@endpush
