@extends('admin.layouts.app')

@section('title', 'برداشت‌های تومانی تایید شده')

@section('content')
<div class="container-fluid">
    <!-- کارت‌های آمار -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-success text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('تعداد تایید شده') }}</h5>
                    <h2 class="mb-0">{{ $withdrawals->total() }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-info text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('مجموع مبلغ') }}</h5>
                    <h2 class="mb-0">{{ number_format($withdrawals->sum('amount')) }}</h2>
                    <div class="small text-white-50">تومان</div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول برداشت‌ها -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('برداشت‌های تومانی تایید شده') }}</h5>
            <div>
                <a href="{{ route('admin.toman-withdrawal.pending') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-clock mr-1"></i> {{ __('برداشت‌های در انتظار') }}
                </a>
                <a href="{{ route('admin.toman-withdrawal.rejected') }}" class="btn btn-danger btn-sm">
                    <i class="fas fa-times-circle mr-1"></i> {{ __('برداشت‌های رد شده') }}
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead class="thead-light">
                        <tr>
                            <th>{{ __('شناسه') }}</th>
                            <th>{{ __('کاربر') }}</th>
                            <th>{{ __('مبلغ (تومان)') }}</th>
                            <th>{{ __('کارت بانکی') }}</th>
                            <th>{{ __('شماره پیگیری') }}</th>
                            <th>{{ __('تاریخ درخواست') }}</th>
                            <th>{{ __('تاریخ تایید') }}</th>
                            <th>{{ __('تایید کننده') }}</th>
                            <th>{{ __('جزئیات') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($withdrawals as $withdrawal)
                            <tr>
                                <td>{{ $withdrawal->id }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm ml-3">
                                            <img src="{{ $withdrawal->user->avatar ?? asset('images/default-avatar.png') }}"
                                                 class="avatar-img rounded-circle">
                                        </div>
                                        <div>
                                            <strong>{{ $withdrawal->user->email }}</strong>
                                            <div class="small text-muted">شناسه: {{ $withdrawal->user->id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="amount-col">
                                    {{ number_format($withdrawal->amount) }}
                                </td>
                                <td>
                                    <div>
                                        <span class="badge badge-soft-primary">
                                            {{ $withdrawal->card->bank->name }}
                                        </span>
                                    </div>
                                    <div class="small text-muted mt-1">
                                        {{ $withdrawal->card->number }}
                                    </div>
                                </td>
                                <td>
                                    @if($withdrawal->tracking_number)
                                        <span class="badge badge-success">{{ $withdrawal->tracking_number }}</span>
                                    @else
                                        <span class="badge badge-secondary">ثبت نشده</span>
                                    @endif
                                </td>
                                <td>
                                    <div>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i') }}</div>
                                </td>
                                <td>
                                    <div>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->processed_at)->format('Y/m/d H:i') }}</div>
                                </td>
                                <td>
                                    {{ $withdrawal->admin->email ?? 'نامشخص' }}
                                </td>
                                <td>
                                    <a href="{{ route('admin.toman-withdrawal.show', $withdrawal->id) }}"
                                            class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <img src="{{ asset('images/no-data.svg') }}" alt="بدون داده" style="width: 120px; height: 120px;">
                                    <div class="mt-3 text-muted">{{ __('هیچ برداشت تایید شده‌ای یافت نشد') }}</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($withdrawals->hasPages())
            <div class="card-footer bg-white">
                {{ $withdrawals->links() }}
            </div>
        @endif
    </div>
</div>


@endsection
