@extends('admin.layouts.app')

@section('title', 'برداشت‌های تومانی در انتظار')

@push('styles')
<style>
    /* استایل‌های سفارشی برای جدول برداشت‌ها */
    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
        transition: all 0.2s;
    }

    .avatar-md {
        width: 48px;
        height: 48px;
    }

    .bank-icon {
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .badge-soft-info {
        background-color: rgba(23, 162, 184, 0.15);
        color: #17a2b8;
    }

    .date-badge, .time-badge {
        background-color: rgba(0, 123, 255, 0.1);
        transition: all 0.2s;
    }

    .date-badge:hover, .time-badge:hover {
        background-color: rgba(0, 123, 255, 0.2);
    }

    .card-number, .sheba-number {
        font-family: monospace;
        letter-spacing: 1px;
    }

    .action-buttons .btn {
        transition: all 0.2s;
    }

    .action-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .amount-col {
        position: relative;
    }

    .thead-dark th {
        background-color: #343a40;
        color: white;
        font-weight: 600;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- کارت‌های آمار -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-primary text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('تعداد در انتظار') }}</h5>
                    <h2 class="mb-0">{{ $withdrawals->total() }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-success text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('مجموع مبلغ') }}</h5>
                    <h2 class="mb-0">{{ number_format($withdrawals->sum('amount')) }}</h2>
                    <div class="small text-white-50">تومان</div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول برداشت‌ها -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('برداشت‌های تومانی در انتظار') }}</h5>
            <div>
                <a href="{{ route('admin.toman-withdrawal.approved') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-check-circle mr-1"></i> {{ __('برداشت‌های تایید شده') }}
                </a>
                <a href="{{ route('admin.toman-withdrawal.rejected') }}" class="btn btn-danger btn-sm">
                    <i class="fas fa-times-circle mr-1"></i> {{ __('برداشت‌های رد شده') }}
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-bordered mb-0">
                    <thead class="thead-dark">
                        <tr class="text-center">
                            <th style="width: 80px;">{{ __('شناسه') }}</th>
                            <th>{{ __('اطلاعات کاربر') }}</th>
                            <th>{{ __('مبلغ (تومان)') }}</th>
                            <th>{{ __('اطلاعات بانکی') }}</th>
                            <th>{{ __('تاریخ درخواست') }}</th>
                            <th style="width: 180px;">{{ __('عملیات') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($withdrawals as $withdrawal)
                            <tr>
                                <td class="text-center">
                                    <span class="badge badge-pill badge-dark">{{ $withdrawal->id }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">

                                        <div>
                                            <div class="d-flex align-items-center">
                                                <strong class="text-primary">{{ $withdrawal->user->firstname ?? '' }} {{ $withdrawal->user->lastname ?? '' }}</strong>
                                                <span class="badge badge-soft-info ml-2">شناسه: {{ $withdrawal->user->id }}</span>
                                            </div>
                                            <div class="small mt-1">
                                                <i class="fas fa-envelope text-muted ml-1"></i> {{ $withdrawal->user->email }}
                                            </div>
                                            <div class="small">
                                                <i class="fas fa-phone text-muted ml-1"></i> {{ $withdrawal->user->phone ?? 'ثبت نشده' }}
                                            </div>
                                            <div class="small">
                                                <i class="fas fa-id-card text-muted ml-1"></i> {{ $withdrawal->user->national_id ?? 'ثبت نشده' }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="amount-col text-center">
                                    <div class="h5 mb-0 font-weight-bold text-success">
                                        {{ number_format($withdrawal->amount) }}
                                    </div>
                                    @if($withdrawal->fee > 0)
                                        <div class="small text-muted">
                                            کارمزد: {{ number_format($withdrawal->fee) }}
                                        </div>
                                    @endif
                                    <div class="small text-muted">
                                        موجودی کاربر: {{ number_format($withdrawal->user->toman_balance ?? 0) }}
                                    </div>
                                </td>
                                <td>
                                    <div class="bank-info">
                                        <div class="d-flex align-items-center mb-2">
                                            @if($withdrawal->card->bank->icon)

                                                <img src=" {{ env('APP_URL').'/storage/' . $withdrawal->card->bank->icon}}" alt="{{ $withdrawal->card->bank->name }}" class="bank-icon ml-2" style="width: 24px; height: 24px;">
                                            @endif
                                            <span class="badge badge-soft-info badge-primary">
                                                {{ $withdrawal->card->bank->name }}
                                            </span>
                                        </div>
                                        <div class="card-info">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-credit-card text-muted ml-1"></i>
                                                <span class="card-number">{{ $withdrawal->card->number }}</span>
                                            </div>
                                            <div class="d-flex align-items-center mt-1">
                                                <i class="fas fa-university text-muted ml-1"></i>
                                                <span class="sheba-number">شبا: {{ $withdrawal->card->sheba ?? 'IR-' }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <div class="date-badge bg-light p-2 rounded">
                                            <i class="far fa-calendar-alt text-primary ml-1"></i>
                                            {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d') }}
                                        </div>
                                        <div class="time-badge mt-1 p-1 rounded">
                                            <i class="far fa-clock text-info ml-1"></i>
                                            {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('H:i') }}
                                        </div>
                                        <div class="small text-muted mt-1">
                                            {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->formatDifference() }}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons d-flex flex-column align-items-center">
                                        <a href="{{ route('admin.toman-withdrawal.operation', $withdrawal->id) }}"
                                                class="btn btn-sm btn-success btn-block mb-1">
                                            <i class="fas fa-cogs ml-1"></i> عملیات
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <img src="{{ asset('images/no-data.svg') }}" alt="بدون داده" style="width: 120px; height: 120px;">
                                    <div class="mt-3 text-muted">{{ __('هیچ برداشت در انتظاری یافت نشد') }}</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($withdrawals->hasPages())
            <div class="card-footer bg-white">
                {{ $withdrawals->links('pagination::bootstrap-5') }}
            </div>
        @endif
    </div>
</div>

<!-- مودال‌های تایید، رد و جزئیات -->
@foreach($withdrawals as $withdrawal)
    <!-- مودال تایید -->
    <div class="modal fade" id="approveModal{{ $withdrawal->id }}" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('تایید برداشت تومانی') }}</h5>
                    <button type="button" class="close mr-auto ml-0" data-dismiss="modal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>آیا از تایید این برداشت تومانی اطمینان دارید؟</p>
                    <div class="alert alert-info">
                        <strong>توجه:</strong> با تایید این درخواست، مبلغ {{ number_format($withdrawal->amount) }} تومان به حساب کاربر واریز خواهد شد.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">انصراف</button>
                    <form action="{{ route('admin.toman-withdrawal.approve', $withdrawal->id) }}" method="POST">
                        @csrf
                        <button type="submit" class="btn btn-success">تایید و واریز</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- مودال رد -->
    <div class="modal fade" id="rejectModal{{ $withdrawal->id }}" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('رد برداشت تومانی') }}</h5>
                    <button type="button" class="close mr-auto ml-0" data-dismiss="modal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="{{ route('admin.toman-withdrawal.reject', $withdrawal->id) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <p>آیا از رد این برداشت تومانی اطمینان دارید؟</p>
                        <div class="form-group">
                            <label for="reject_reason">دلیل رد درخواست:</label>
                            <textarea name="reject_reason" id="reject_reason" class="form-control" rows="3" required></textarea>
                        </div>
                        <div class="alert alert-warning">
                            <strong>توجه:</strong> با رد این درخواست، مبلغ {{ number_format($withdrawal->amount) }} تومان به حساب کاربر بازگردانده خواهد شد.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">انصراف</button>
                        <button type="submit" class="btn btn-danger">رد درخواست</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- مودال جزئیات -->
    <div class="modal fade" id="withdrawalDetails{{ $withdrawal->id }}" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('جزئیات برداشت تومانی') }}</h5>
                    <button type="button" class="close mr-auto ml-0" data-dismiss="modal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-bordered mb-0">
                            <tbody>
                                <tr>
                                    <th class="bg-light">{{ __('شناسه برداشت') }}</th>
                                    <td><span class="badge badge-pill badge-dark">{{ $withdrawal->id }}</span></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">{{ __('اطلاعات کاربر') }}</th>
                                    <td>
                                        <div class="d-flex align-items-center">

                                            <div>
                                                <div><strong>{{ $withdrawal->user->firstname ?? '' }} {{ $withdrawal->user->lastname ?? '' }}</strong></div>
                                                <div class="small text-muted">{{ $withdrawal->user->email }} ({{ $withdrawal->user->id }})</div>
                                                <div class="small text-muted">{{ $withdrawal->user->phone ?? 'ثبت نشده' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="bg-light">{{ __('مبلغ') }}</th>
                                    <td>
                                        <span class="text-success font-weight-bold">{{ number_format($withdrawal->amount) }}</span> تومان
                                        <div class="small text-muted mt-1">موجودی کاربر: {{ number_format($withdrawal->user->toman_balance ?? 0) }} تومان</div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="bg-light">{{ __('کارمزد') }}</th>
                                    <td>{{ number_format($withdrawal->fee) }} تومان</td>
                                </tr>
                                <tr>
                                    <th class="bg-light">{{ __('اطلاعات بانکی') }}</th>
                                    <td>
                                        <div class="d-flex align-items-center mb-2">
                                            @if($withdrawal->card->bank->icon)
                                                <img src="{{ asset($withdrawal->card->bank->icon) }}" alt="{{ $withdrawal->card->bank->name }}" class="bank-icon ml-2" style="width: 24px; height: 24px;">
                                            @endif
                                            <span class="badge badge-pill badge-primary">{{ $withdrawal->card->bank->name }}</span>
                                        </div>
                                        <div class="mt-2">
                                            <div><i class="fas fa-credit-card text-muted ml-1"></i> <strong>شماره کارت:</strong> {{ $withdrawal->card->number }}</div>
                                            <div class="mt-1"><i class="fas fa-university text-muted ml-1"></i> <strong>شماره شبا:</strong> {{ $withdrawal->card->sheba ?? 'ثبت نشده' }}</div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="bg-light">{{ __('تاریخ درخواست') }}</th>
                                    <td>
                                        <div><i class="far fa-calendar-alt text-primary ml-1"></i> {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i:s') }}</div>
                                        <div class="small text-muted mt-1">{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->formatDifference() }}</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">بستن</button>
                </div>
            </div>
        </div>
    </div>
@endforeach
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // فعال کردن تولتیپ‌ها
        $('[data-toggle="tooltip"]').tooltip();

        // انیمیشن برای ردیف‌های جدول
        $('.table tbody tr').each(function(index) {
            $(this).css({
                'opacity': 0,
                'transform': 'translateY(20px)'
            });

            setTimeout(() => {
                $(this).animate({
                    'opacity': 1
                }, 300);
                $(this).css('transform', 'translateY(0)');
            }, 100 * index);
        });

        // افکت هاور برای دکمه‌ها
        $('.action-buttons .btn').hover(function() {
            $(this).addClass('shadow-sm');
        }, function() {
            $(this).removeClass('shadow-sm');
        });

        // نمایش شماره کارت به صورت فرمت شده
        $('.card-number').each(function() {
            let cardNumber = $(this).text();
            if (cardNumber.length === 16) {
                let formattedNumber = cardNumber.match(/.{1,4}/g).join('-');
                $(this).text(formattedNumber);
            }
        });
    });
</script>
@endpush
