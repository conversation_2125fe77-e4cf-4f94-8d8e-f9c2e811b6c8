@extends('admin.layouts.app')

@section('title', 'برداشت‌های تومانی رد شده')

@section('content')
<div class="container-fluid">
    <!-- کارت‌های آمار -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-danger text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('تعداد رد شده') }}</h5>
                    <h2 class="mb-0">{{ $withdrawals->total() }}</h2>
                    <div class="small text-white-50">{{ __('تعداد') }}</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card bg-warning text-white mb-4">
                <div class="card-body">
                    <h5>{{ __('مجموع مبلغ') }}</h5>
                    <h2 class="mb-0">{{ number_format($withdrawals->sum('amount')) }}</h2>
                    <div class="small text-white-50">تومان</div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول برداشت‌ها -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('برداشت‌های تومانی رد شده') }}</h5>
            <div>
                <a href="{{ route('admin.toman-withdrawal.pending') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-clock mr-1"></i> {{ __('برداشت‌های در انتظار') }}
                </a>
                <a href="{{ route('admin.toman-withdrawal.approved') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-check-circle mr-1"></i> {{ __('برداشت‌های تایید شده') }}
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead class="thead-light">
                        <tr>
                            <th>{{ __('شناسه') }}</th>
                            <th>{{ __('کاربر') }}</th>
                            <th>{{ __('مبلغ (تومان)') }}</th>
                            <th>{{ __('کارت بانکی') }}</th>
                            <th>{{ __('تاریخ درخواست') }}</th>
                            <th>{{ __('تاریخ رد') }}</th>
                            <th>{{ __('دلیل رد') }}</th>
                            <th>{{ __('جزئیات') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($withdrawals as $withdrawal)
                            <tr>
                                <td>{{ $withdrawal->id }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm ml-3">
                                            <img src="{{ $withdrawal->user->avatar ?? asset('images/default-avatar.png') }}" 
                                                 class="avatar-img rounded-circle">
                                        </div>
                                        <div>
                                            <strong>{{ $withdrawal->user->email }}</strong>
                                            <div class="small text-muted">شناسه: {{ $withdrawal->user->id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="amount-col">
                                    {{ number_format($withdrawal->amount) }}
                                </td>
                                <td>
                                    <div>
                                        <span class="badge badge-soft-primary">
                                            {{ $withdrawal->card->bank->name }}
                                        </span>
                                    </div>
                                    <div class="small text-muted mt-1">
                                        {{ $withdrawal->card->number }}
                                    </div>
                                </td>
                                <td>
                                    <div>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i') }}</div>
                                </td>
                                <td>
                                    <div>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->processed_at)->format('Y/m/d H:i') }}</div>
                                </td>
                                <td>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger" 
                                            data-toggle="tooltip" 
                                            title="{{ $withdrawal->reject_reason }}">
                                        <i class="fas fa-info-circle"></i> مشاهده
                                    </button>
                                </td>
                                <td>
                                    <button type="button" 
                                            class="btn btn-sm btn-info" 
                                            data-toggle="modal" 
                                            data-target="#withdrawalDetails{{ $withdrawal->id }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <img src="{{ asset('images/no-data.svg') }}" alt="بدون داده" style="width: 120px; height: 120px;">
                                    <div class="mt-3 text-muted">{{ __('هیچ برداشت رد شده‌ای یافت نشد') }}</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($withdrawals->hasPages())
            <div class="card-footer bg-white">
                {{ $withdrawals->links() }}
            </div>
        @endif
    </div>
</div>

<!-- مودال‌های جزئیات -->
@foreach($withdrawals as $withdrawal)
    <!-- مودال جزئیات -->
    <div class="modal fade" id="withdrawalDetails{{ $withdrawal->id }}" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('جزئیات برداشت تومانی') }}</h5>
                    <button type="button" class="close mr-auto ml-0" data-dismiss="modal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-bordered mb-0">
                            <tbody>
                                <tr>
                                    <th>{{ __('شناسه برداشت') }}</th>
                                    <td>{{ $withdrawal->id }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('کاربر') }}</th>
                                    <td>{{ $withdrawal->user->email }} ({{ $withdrawal->user->id }})</td>
                                </tr>
                                <tr>
                                    <th>{{ __('مبلغ') }}</th>
                                    <td>{{ number_format($withdrawal->amount) }} تومان</td>
                                </tr>
                                <tr>
                                    <th>{{ __('کارمزد') }}</th>
                                    <td>{{ number_format($withdrawal->fee) }} تومان</td>
                                </tr>
                                <tr>
                                    <th>{{ __('بانک') }}</th>
                                    <td>{{ $withdrawal->card->bank->name }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('شماره کارت') }}</th>
                                    <td>{{ $withdrawal->card->number }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('تاریخ درخواست') }}</th>
                                    <td>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('تاریخ رد') }}</th>
                                    <td>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->processed_at)->format('Y/m/d H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('رد کننده') }}</th>
                                    <td>{{ $withdrawal->admin->email ?? 'نامشخص' }}</td>
                                </tr>
                                <tr>
                                    <th>{{ __('دلیل رد') }}</th>
                                    <td>{{ $withdrawal->reject_reason }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">بستن</button>
                </div>
            </div>
        </div>
    </div>
@endforeach
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // فعال کردن تولتیپ‌ها
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
@endpush
