@extends('admin.layouts.app')

@section('title', 'جزئیات برداشت تومانی')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('جزئیات برداشت تومانی') }}</h5>
                    <div>
                        @if($withdrawal->status == 'approved')
                            <a href="{{ route('admin.toman-withdrawal.approved') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-arrow-right ml-1"></i> {{ __('بازگشت به لیست') }}
                            </a>
                        @elseif($withdrawal->status == 'rejected')
                            <a href="{{ route('admin.toman-withdrawal.rejected') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-arrow-right ml-1"></i> {{ __('بازگشت به لیست') }}
                            </a>
                        @else
                            <a href="{{ route('admin.toman-withdrawal.pending') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-arrow-right ml-1"></i> {{ __('بازگشت به لیست') }}
                            </a>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- اطلاعات کاربر -->
                        <div class="col-md-4">
                            <div class="card detail-card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-user ml-1"></i> {{ __('اطلاعات کاربر') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="user-info">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="avatar avatar-md ml-3">
                                                <img src="{{ $withdrawal->user->avatar ?? asset('images/default-avatar.png') }}" 
                                                     class="avatar-img rounded-circle">
                                            </div>
                                            <div>
                                                <h6 class="mb-1">{{ $withdrawal->user->email }}</h6>
                                                <div class="small text-muted">شناسه: {{ $withdrawal->user->id }}</div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <strong>نام کاربری:</strong> 
                                            <span>{{ $withdrawal->user->username ?? 'ثبت نشده' }}</span>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <strong>شماره موبایل:</strong> 
                                            <span>{{ $withdrawal->user->phone ?? 'ثبت نشده' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- اطلاعات کارت بانکی -->
                        <div class="col-md-4">
                            <div class="card detail-card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-credit-card ml-1"></i> {{ __('اطلاعات کارت بانکی') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="bank-info">
                                        <div class="mb-3 text-center">
                                            @if($withdrawal->card->bank->logo)
                                                <img src="{{ asset('storage/' . $withdrawal->card->bank->logo) }}" 
                                                     alt="{{ $withdrawal->card->bank->name }}" 
                                                     class="bank-logo mb-2" style="max-height: 40px;">
                                            @endif
                                            <h6 class="mb-0">{{ $withdrawal->card->bank->name }}</h6>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <strong>شماره کارت:</strong> 
                                            <span class="text-primary">{{ $withdrawal->card->number }}</span>
                                        </div>
                                        
                                        @if($withdrawal->card->sheba)
                                        <div class="mb-3">
                                            <strong>شماره شبا:</strong> 
                                            <span>{{ $withdrawal->card->sheba }}</span>
                                        </div>
                                        @endif
                                        
                                        <div class="mb-3">
                                            <strong>نام صاحب کارت:</strong> 
                                            <span>{{ $withdrawal->card->owner_name }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- اطلاعات مبلغ -->
                        <div class="col-md-4">
                            <div class="card detail-card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-money-bill-wave ml-1"></i> {{ __('اطلاعات مبلغ') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="amount-box">
                                        <div class="mb-3 text-center">
                                            <h4 class="text-success font-weight-bold">{{ number_format($withdrawal->amount) }} تومان</h4>
                                            <small class="text-muted">مبلغ درخواستی</small>
                                        </div>
                                        
                                        @if($withdrawal->fee > 0)
                                        <div class="mb-3">
                                            <strong>کارمزد:</strong> 
                                            <span class="text-danger">{{ number_format($withdrawal->fee) }} تومان</span>
                                        </div>
                                        @endif
                                        
                                        <div class="mb-3">
                                            <strong>مبلغ خالص:</strong> 
                                            <span>{{ number_format($withdrawal->amount - $withdrawal->fee) }} تومان</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <!-- اطلاعات وضعیت -->
                        <div class="col-md-6">
                            <div class="card detail-card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-info-circle ml-1"></i> {{ __('اطلاعات وضعیت') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="status-info">
                                        <div class="mb-3">
                                            <strong>وضعیت:</strong> 
                                            @if($withdrawal->status == 'pending')
                                                <span class="badge badge-warning">در انتظار تایید</span>
                                            @elseif($withdrawal->status == 'approved')
                                                <span class="badge badge-success">تایید شده</span>
                                            @elseif($withdrawal->status == 'rejected')
                                                <span class="badge badge-danger">رد شده</span>
                                            @endif
                                        </div>
                                        
                                        <div class="mb-3">
                                            <strong>تاریخ درخواست:</strong> 
                                            <span>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i:s') }}</span>
                                        </div>
                                        
                                        @if($withdrawal->status != 'pending')
                                        <div class="mb-3">
                                            <strong>تاریخ پردازش:</strong> 
                                            <span>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->processed_at)->format('Y/m/d H:i:s') }}</span>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <strong>پردازش کننده:</strong> 
                                            <span>{{ $withdrawal->admin->email ?? 'نامشخص' }}</span>
                                        </div>
                                        @endif
                                        
                                        @if($withdrawal->status == 'approved' && $withdrawal->tracking_number)
                                        <div class="mb-3">
                                            <strong>شماره پیگیری:</strong> 
                                            <span class="badge badge-success">{{ $withdrawal->tracking_number }}</span>
                                        </div>
                                        @endif
                                        
                                        @if($withdrawal->status == 'rejected' && $withdrawal->reject_reason)
                                        <div class="mb-3">
                                            <strong>دلیل رد:</strong> 
                                            <div class="alert alert-danger mt-2">
                                                {{ $withdrawal->reject_reason }}
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- تاریخچه تراکنش -->
                        <div class="col-md-6">
                            <div class="card detail-card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-history ml-1"></i> {{ __('اطلاعات تکمیلی') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="transaction-info">
                                        <div class="mb-3">
                                            <strong>شناسه برداشت:</strong> 
                                            <span>{{ $withdrawal->id }}</span>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <strong>آی‌پی درخواست:</strong> 
                                            <span>{{ $withdrawal->ip ?? 'ثبت نشده' }}</span>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <strong>توضیحات کاربر:</strong> 
                                            <div>{{ $withdrawal->description ?? 'بدون توضیحات' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
