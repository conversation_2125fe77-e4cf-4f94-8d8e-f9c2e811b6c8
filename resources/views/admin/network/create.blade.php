@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ isset($network) ? 'ویرایش شبکه' : 'ایجاد شبکه جدید' }}</h3>
                </div>
                <div class="card-body">
                    <form action="{{ isset($network) ? route('admin.networks.update', $network->id) : route('admin.networks.store') }}" 
                          method="POST" 
                          enctype="multipart/form-data">
                        @csrf
                        @if(isset($network))
                            @method('PUT')
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>نام</label>
                                    <input type="text" 
                                           name="name" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           value="{{ old('name', $network->name ?? '') }}"
                                           required>
                                    @error('name')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>نوع</label>
                                    <select name="slug" 
                                            class="form-control @error('slug') is-invalid @enderror"
                                            {{ isset($network) ? 'disabled' : '' }}
                                            required>
                                        <option value="">انتخاب کنید</option>
                                        @foreach($supportedNetworks as $net)
                                            <option value="{{ $net->slug }}" 
                                                    {{ (old('slug', $network->slug ?? '') == $net->slug) ? 'selected' : '' }}>
                                                {{ $net->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('slug')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>تعداد تایید بلاک</label>
                                    <div class="input-group">
                                        <input type="number" 
                                               name="block_confirmation" 
                                               class="form-control @error('block_confirmation') is-invalid @enderror"
                                               value="{{ old('block_confirmation', $network->block_confirmation ?? '') }}">
                                        <button type="button" class="btn btn-info check-block-btn">
                                            <i class="fas fa-sync"></i>
                                            چک بلاک
                                        </button>
                                    </div>
                                    @error('block_confirmation')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>آدرس RPC</label>
                                    <input type="text" 
                                           name="rpc_url" 
                                           class="form-control @error('rpc_url') is-invalid @enderror"
                                           value="{{ old('rpc_url', $network->rpc_url ?? '') }}">
                                    @error('rpc_url')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>آدرس Explorer</label>
                                    <input type="text" 
                                           name="explorer_url" 
                                           class="form-control @error('explorer_url') is-invalid @enderror"
                                           value="{{ old('explorer_url', $network->explorer_url ?? '') }}">
                                    @error('explorer_url')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>لوگو</label>
                                    <input type="file" 
                                           name="logo" 
                                           class="form-control @error('logo') is-invalid @enderror">
                                    @error('logo')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" 
                                               class="custom-control-input" 
                                               id="status" 
                                               name="status" 
                                               value="1"
                                               {{ old('status', $network->status ?? '1') ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="status">فعال</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            {{ isset($network) ? 'بروزرسانی' : 'ایجاد' }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Network slug change handler
    $('select[name="slug"]').on('change', function() {
        const selectedNetwork = $(this).val();
        const networks = @json($supportedNetworks);
        
        const network = networks.find(n => n.slug === selectedNetwork);
        if (network) {
            $('input[name="explorer_url"]').val(network.base_url);
        }
    });

    // Check block handler
    $('.check-block-btn').on('click', function() {
        let btn = $(this);
        let originalText = btn.html();
        
        // Get form data
        let formData = {
            id: '{{ $network->id ?? "" }}',
            rpc_url: $('input[name="rpc_url"]').val(),
            _token: '{{ csrf_token() }}'
        };

        if (!formData.rpc_url) {
            toastr.error('لطفا آدرس RPC را وارد کنید');
            return;
        }

        // Disable button and show loading
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> در حال بررسی...');

        $.ajax({
            url: "{{ route('admin.networks.check-block') }}",
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    toastr.success('بلاک فعلی: ' + response.data);
                    // Set the block number in the input field
                    $('input[name="block_confirmation"]').val(response.data);
                } else {
                    toastr.error(response.message || 'خطا در بررسی بلاک');
                }
            },
            error: function(xhr) {
                let message = 'خطا در ارتباط با سرور';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                toastr.error(message);
            },
            complete: function() {
                // Re-enable button and restore original text
                btn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
@endpush
@endsection
