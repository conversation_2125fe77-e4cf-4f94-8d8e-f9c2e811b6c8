@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">شبکه‌ها</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.networks.create') }}" class="btn btn-primary">
                            ایجاد شبکه جدید
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>لوگو</th>
                                <th>نام</th>
                                <th>نوع</th>
                                <th>وضعیت</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($networks as $network)
                            <tr>
                                <td>
                                    @if($network->logo)
                                        <img src="{{ asset('storage/' . $network->logo) }}" 
                                             alt="{{ $network->name }}" 
                                             height="40">
                                    @endif
                                </td>
                                <td>{{ $network->name }}</td>
                                <td>{{ $network->base_type }}</td>
                                <td>
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" 
                                               class="custom-control-input status-toggle" 
                                               id="status_{{ $network->id }}"
                                               data-id="{{ $network->id }}"
                                               {{ $network->status ? 'checked' : '' }}>
                                        <label class="custom-control-label" 
                                               for="status_{{ $network->id }}"></label>
                                    </div>
                                </td>
                                <td>
                                    <a href="{{ route('admin.networks.edit', $network->id) }}" 
                                       class="btn btn-sm btn-info">
                                        ویرایش
                                    </a>
                                    @if(!in_array($network->base_type, ['COIN_PAYMENT', 'BITCOIN_API', 'BITGO_API']))
                                    <button type="button" 
                                            class="btn btn-sm btn-danger delete-btn"
                                            data-id="{{ $network->id }}">
                                        حذف
                                    </button>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    {{ $networks->links() }}
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    $('.status-toggle').on('change', function() {
        const id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("admin.networks.toggle-status") }}',
            type: 'POST',
            data: {
                id: id,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            }
        });
    });

    $('.delete-btn').on('click', function() {
        const id = $(this).data('id');
        if (confirm('آیا از حذف این شبکه اطمینان دارید؟')) {
            window.location.href = `/admin/networks/${id}/delete`;
        }
    });
});
</script>
@endpush
@endsection