@extends('admin.layouts.app')

@section('title', 'مدیریت مدارک')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
    <li class="breadcrumb-item active">مدیریت مدارک</li>
@endsection

@section('content')
<div class="container-fluid fade-in">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title h3">مدیریت مدارک احراز هویت</h1>
                    <p class="text-muted">بررسی و تایید مدارک ارسالی کاربران</p>
                </div>
                <div class="d-flex">
                    <a href="{{ route('admin.documents.grouped') }}" class="btn btn-outline-success me-2">
                        <i class="fas fa-users me-1"></i> نمایش گروه‌بندی شده
                    </a>
                    <div class="dropdown me-2">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-1"></i> فیلتر وضعیت
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item filter-status" href="#" data-status="all">همه</a></li>
                            <li><a class="dropdown-item filter-status" href="#" data-status="pending">در انتظار بررسی</a></li>
                            <li><a class="dropdown-item filter-status" href="#" data-status="approved">تایید شده</a></li>
                            <li><a class="dropdown-item filter-status" href="#" data-status="rejected">رد شده</a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-file-alt me-1"></i> نوع مدرک
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item filter-type" href="#" data-type="all">همه</a></li>
                            <li><a class="dropdown-item filter-type" href="#" data-type="id">کارت ملی جلو</a></li>
                            <li><a class="dropdown-item filter-type" href="#" data-type="id_back">کارت ملی پشت</a></li>
                            <li><a class="dropdown-item filter-type" href="#" data-type="selfie">سلفی</a></li>
                            <li><a class="dropdown-item filter-type" href="#" data-type="consent">رضایت‌نامه</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آمار کلی -->
    <div class="row mb-4">
        <!-- تعداد کل مدارک -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card primary">
                        <i class="fas fa-file-alt stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value" id="total-documents">{{ $totalDocuments }}</div>
                            <div class="stat-label">تعداد کل مدارک</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مدارک در انتظار بررسی -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card warning">
                        <i class="fas fa-clock stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value" id="pending-documents">{{ $pendingDocuments }}</div>
                            <div class="stat-label">در انتظار بررسی</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مدارک تایید شده -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card success">
                        <i class="fas fa-check-circle stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value" id="approved-documents">{{ $approvedDocuments }}</div>
                            <div class="stat-label">تایید شده</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مدارک رد شده -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card danger">
                        <i class="fas fa-times-circle stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value" id="rejected-documents">{{ $rejectedDocuments }}</div>
                            <div class="stat-label">رد شده</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول مدارک -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">لیست مدارک</h5>
                        <div class="input-group" style="width: 300px;">
                            <input type="text" id="search-input" class="form-control" placeholder="جستجو...">
                            <button class="btn btn-primary" type="button" id="search-button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="documents-table">
                            <thead class="table-light">
                                <tr>
                                    <th width="60">#</th>
                                    <th>کاربر</th>
                                    <th>نوع مدرک</th>
                                    <th>تصویر</th>
                                    <th>تاریخ ارسال</th>
                                    <th>وضعیت</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($documents as $document)
                                <tr>
                                    <td>{{ $document->id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="position-relative me-3">
                                                <img src="{{ $document->user->avatar ?? "https://ui-avatars.com/api/?name={$document->user->firstname}+{$document->user->lastname}&background=5a67d8&color=fff" }}"
                                                     class="rounded-circle" width="40" height="40" alt="{{ $document->user->firstname }} {{ $document->user->lastname }}">
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $document->user->firstname }} {{ $document->user->lastname }}</h6>
                                                <small class="text-muted">{{ $document->user->email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @php
                                            $documentTypeText = '';
                                            $documentTypeIcon = '';

                                            switch($document->name) {
                                                case 'id':
                                                    $documentTypeText = 'کارت ملی جلو';
                                                    $documentTypeIcon = 'fa-id-card';
                                                    break;
                                                case 'id_back':
                                                    $documentTypeText = 'کارت ملی پشت';
                                                    $documentTypeIcon = 'fa-id-card';
                                                    break;
                                                case 'selfie':
                                                    $documentTypeText = 'سلفی';
                                                    $documentTypeIcon = 'fa-camera';
                                                    break;
                                                case 'consent':
                                                    $documentTypeText = 'رضایت‌نامه';
                                                    $documentTypeIcon = 'fa-file-signature';
                                                    break;
                                                default:
                                                    $documentTypeText = $document->name;
                                                    $documentTypeIcon = 'fa-file';
                                            }
                                        @endphp
                                        <div class="d-flex align-items-center">
                                            <i class="fas {{ $documentTypeIcon }} text-primary me-2"></i>
                                            <span>{{ $documentTypeText }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        @if($document->name != 'consent')
                                        <a href="#" class="view-document" data-bs-toggle="modal" data-bs-target="#documentModal" data-document-id="{{ $document->id }}" data-document-url="{{ config('app.url') }}/storage/{{ $document->file->url }}">
                                                <img src="{{ config('app.url') }}/storage/{{ $document->file->url }}" class="img-thumbnail" width="80" alt="مشاهده مدرک">
                                            </a>
                                        @else
                                        <a href="{{ config('app.url') }}/storage/{{ $document->file->url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-file-pdf me-1"></i>
                                                 مشاهده
                                            </a>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span>{{ jdate($document->created_at)->format('Y/m/d') }}</span>
                                            <small class="text-muted">{{ jdate($document->created_at)->ago() }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        @php
                                            $statusClass = '';
                                            $statusText = '';

                                            switch($document->status) {
                                                case 'pending':
                                                    $statusClass = 'bg-warning-subtle text-warning';
                                                    $statusText = 'در انتظار بررسی';
                                                    break;
                                                case 'approved':
                                                    $statusClass = 'bg-success-subtle text-success';
                                                    $statusText = 'تایید شده';
                                                    break;
                                                case 'rejected':
                                                    $statusClass = 'bg-danger-subtle text-danger';
                                                    $statusText = 'رد شده';
                                                    break;
                                            }
                                        @endphp
                                        <div class="d-flex flex-column">
                                            <span class="badge {{ $statusClass }} mb-1">{{ $statusText }}</span>
                                            @if($document->status === 'rejected' && $document->description)
                                                <small class="text-danger">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    دلیل رد: {{ $document->description }}
                                                </small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-2">
                                            <!-- دکمه تایید -->
                                            <form action="{{ route('admin.document.update', $document->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <input type="hidden" name="status" value="approved">
                                                <input type="hidden" name="description" value="">
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('آیا از تایید این مدرک اطمینان دارید؟')"
                                                        title="تایید مدرک">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>

                                            <!-- فرم رد با فیلد توضیحات -->
                                            <form action="{{ route('admin.document.update', $document->id) }}" method="POST" class="d-flex">
                                                @csrf
                                                <input type="hidden" name="status" value="rejected">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text bg-danger-subtle text-danger border-danger">
                                                        <i class="fas fa-exclamation-circle"></i>
                                                    </span>
                                                    <input type="text"
                                                           name="description"
                                                           class="form-control form-control-sm border-danger"
                                                           placeholder="دلیل رد مدرک..."
                                                           required
                                                           style="width: 150px;">
                                                    <button type="submit"
                                                            class="btn btn-sm btn-danger"
                                                            onclick="return confirm('آیا از رد این مدرک اطمینان دارید؟')"
                                                            title="رد مدرک">
                                                        <i class="fas fa-times me-1"></i>
                                                        رد
                                                    </button>
                                                </div>
                                            </form>

                                            <!-- دکمه مشاهده -->
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-primary view-document"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#documentModal"
                                                    data-document-url="{{ config('app.url') }}/storage/{{ $document->file->url }}"
                                                    title="مشاهده مدرک">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="text-muted">
                            نمایش {{ $documents->firstItem() ?? 0 }} تا {{ $documents->lastItem() ?? 0 }} از {{ $documents->total() ?? 0 }} رکورد
                        </div>
                        <div class="pagination-container">
                            {{ $documents->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Document View -->
<div class="modal fade" id="documentModal" tabindex="-1" aria-labelledby="documentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="documentModalLabel">مشاهده مدرک</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="document-image" src="" class="img-fluid" alt="مدارک">
            </div>

        </div>
    </div>
</div>

<!-- Modal for Reject Reason -->
<div class="modal fade" id="rejectReasonModal" tabindex="-1" aria-labelledby="rejectReasonModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectReasonModalLabel">دلیل رد مدرک</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reject-form">
                    <div class="mb-3">
                        <label for="reject-reason" class="form-label">لطفا دلیل رد مدرک را وارد کنید:</label>
                        <textarea class="form-control" id="reject-reason" rows="3" required></textarea>
                    </div>
                    <input type="hidden" id="reject-document-id" value="">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-danger" id="confirm-reject">تایید و رد مدرک</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    function approveDocument(documentId) {
        // نمایش loading
        Swal.fire({
            title: 'در حال پردازش...',
            text: 'لطفا منتظر بمانید',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // ارسال درخواست
        fetch(`/admin/document/${documentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                status: 'approved'
            })
        })
        .then(response => response.json())
        .then(data => {
            Swal.fire({
                icon: 'success',
                title: 'موفق',
                text: 'مدارک با موفقیت تایید شد',
                confirmButtonText: 'باشه'
            }).then(() => {
                window.location.reload();
            });
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'خطا',
                text: 'مشکلی در پردازش درخواست رخ داد',
                confirmButtonText: 'باشه'
            });
        });
    }

    function rejectDocument(documentId, reason) {
        // نمایش loading
        Swal.fire({
            title: 'در حال پردازش...',
            text: 'لطفا منتظر بمانید',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // ارسال درخواست
        fetch(`/admin/document/${documentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                status: 'rejected',
                description: reason
            })
        })
        .then(response => response.json())
        .then(data => {
            Swal.fire({
                icon: 'success',
                title: 'موفق',
                text: 'مدارک با موفقیت رد شد',
                confirmButtonText: 'باشه'
            }).then(() => {
                window.location.reload();
            });
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'خطا',
                text: 'مشکلی در پردازش درخواست رخ داد',
                confirmButtonText: 'باشه'
            });
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // تایید مدرک
        document.querySelectorAll('.approve-document, .approve-document-modal').forEach(button => {
            button.addEventListener('click', function() {
                const documentId = this.getAttribute('data-document-id');
                Swal.fire({
                    title: 'تایید مدرک',
                    text: 'آیا از تایید این مدرک اطمینان دارید؟',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'بله، تایید شود',
                    cancelButtonText: 'انصراف'
                }).then((result) => {
                    if (result.isConfirmed) {
                        approveDocument(documentId);
                    }
                });
            });
        });

        // رد مدرک
        document.querySelectorAll('.reject-document, .reject-document-modal').forEach(button => {
            button.addEventListener('click', function() {
                const documentId = this.getAttribute('data-document-id');
                document.getElementById('reject-document-id').value = documentId;
            });
        });

        // تایید رد مدرک
        document.getElementById('confirm-reject').addEventListener('click', function() {
            const documentId = document.getElementById('reject-document-id').value;
            const reason = document.getElementById('reject-reason').value;

            if (!reason.trim()) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطا',
                    text: 'لطفا دلیل رد مدرک را وارد کنید',
                    confirmButtonText: 'باشه'
                });
                return;
            }

            // بستن مودال دلیل رد
            const rejectModal = bootstrap.Modal.getInstance(document.getElementById('rejectReasonModal'));
            rejectModal.hide();

            rejectDocument(documentId, reason);
        });

        // Initialize tooltips
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(el => new bootstrap.Tooltip(el));

        // Document modal functionality
        const documentModal = document.getElementById('documentModal');
        documentModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const documentId = button.getAttribute('data-document-id');
            const documentUrl = button.getAttribute('data-document-url');

            const documentImage = document.getElementById('document-image');
            documentImage.src = documentUrl;

            const approveButton = document.querySelector('.approve-document-modal');
            const rejectButton = document.querySelector('.reject-document-modal');

            approveButton.setAttribute('data-document-id', documentId);
            rejectButton.setAttribute('data-document-id', documentId);
        });

        // Filter by status
        document.querySelectorAll('.filter-status').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const status = this.getAttribute('data-status');
                filterDocuments('status', status);
            });
        });

        // Filter by type
        document.querySelectorAll('.filter-type').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const type = this.getAttribute('data-type');
                filterDocuments('type', type);
            });
        });

        // Search functionality
        document.getElementById('search-button').addEventListener('click', function() {
            const searchTerm = document.getElementById('search-input').value;
            searchDocuments(searchTerm);
        });

        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const searchTerm = this.value;
                searchDocuments(searchTerm);
            }
        });

        // Functions
        function filterDocuments(filterType, value) {
            let url = new URL(window.location.href);

            if (value === 'all') {
                url.searchParams.delete(filterType);
            } else {
                url.searchParams.set(filterType, value);
            }

            window.location.href = url.toString();
        }

        function searchDocuments(term) {
            let url = new URL(window.location.href);

            if (term.trim() === '') {
                url.searchParams.delete('search');
            } else {
                url.searchParams.set('search', term);
            }

            window.location.href = url.toString();
        }
    });
</script>
@endpush

@push('styles')
<style>
/* استایل سفارشی برای صفحه مدارک */

/* استایل کارت‌های آماری */
.stat-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.stat-card.primary {
    background: linear-gradient(135deg, #5a67d8 0%, #4c51bf 100%);
    color: white;
}

.stat-card.warning {
    background: linear-gradient(135deg, #ecc94b 0%, #d69e2e 100%);
    color: white;
}

.stat-card.success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.stat-card.danger {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    color: white;
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 1.5rem;
    opacity: 0.8;
}

.stat-content {
    flex-grow: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* استایل جدول */
.table > :not(caption) > * > * {
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(90, 103, 216, 0.05);
}

/* استایل برای نشانگرها */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
}

.bg-success-subtle {
    background-color: rgba(72, 187, 120, 0.15);
}

.bg-warning-subtle {
    background-color: rgba(236, 201, 75, 0.15);
}

.bg-danger-subtle {
    background-color: rgba(229, 62, 62, 0.15);
}

.text-success {
    color: #38a169 !important;
}

.text-warning {
    color: #d69e2e !important;
}

.text-danger {
    color: #c53030 !important;
}

/* استایل برای تصویر مدارک */
.img-thumbnail {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.img-thumbnail:hover {
    transform: scale(1.05);
}

/* انیمیشن برای بخش‌های مختلف */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    animation: fadeIn 0.5s ease-in-out;
}

.card:nth-child(2) {
    animation-delay: 0.1s;
}

.card:nth-child(3) {
    animation-delay: 0.2s;
}

.card:nth-child(4) {
    animation-delay: 0.3s;
}
</style>
@endpush
@endsection
