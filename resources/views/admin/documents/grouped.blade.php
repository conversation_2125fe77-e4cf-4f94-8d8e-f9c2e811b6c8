@extends('admin.layouts.app')

@section('title', 'مدیریت مدارک گروه‌بندی شده')

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
    <li class="breadcrumb-item"><a href="{{ route('admin.documents.index') }}">مدیریت مدارک</a></li>
    <li class="breadcrumb-item active">نمایش گروه‌بندی شده</li>
@endsection

@section('content')
<div class="container-fluid fade-in">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title h3">مدیریت مدارک گروه‌بندی شده</h1>
                    <p class="text-muted">نمایش کاربران و وضعیت تکمیل مدارک آن‌ها</p>
                </div>
                <div class="d-flex">
                    <a href="{{ route('admin.documents.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-list me-1"></i> نمایش لیستی
                    </a>
                    <div class="dropdown me-2">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-1"></i> فیلتر وضعیت
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item filter-status" href="#" data-status="all">همه</a></li>
                            <li><a class="dropdown-item filter-status" href="#" data-status="pending">در انتظار بررسی</a></li>
                            <li><a class="dropdown-item filter-status" href="#" data-status="approved">تایید شده</a></li>
                            <li><a class="dropdown-item filter-status" href="#" data-status="rejected">رد شده</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آمار کلی -->
    <div class="row mb-4">
        <!-- تعداد کل کاربران -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card primary">
                        <i class="fas fa-users stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ $totalUsers }}</div>
                            <div class="stat-label">کل کاربران با مدرک</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- کاربران کامل -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card success">
                        <i class="fas fa-check-circle stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ $completeUsers }}</div>
                            <div class="stat-label">مدارک کامل</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- کاربران ناقص -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card warning">
                        <i class="fas fa-exclamation-triangle stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ $incompleteUsers }}</div>
                            <div class="stat-label">مدارک ناقص</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جستجو -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="input-group" style="max-width: 400px;">
                        <input type="text" id="search-input" class="form-control" placeholder="جستجو بر اساس نام، ایمیل، تلفن یا کد ملی...">
                        <button class="btn btn-primary" type="button" id="search-button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- لیست کاربران -->
    <div class="row">
        @foreach($users as $user)
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <img src="{{ $user->avatar ?? "https://ui-avatars.com/api/?name={$user->firstname}+{$user->lastname}&background=5a67d8&color=fff" }}"
                                 class="rounded-circle me-3" width="50" height="50" alt="{{ $user->firstname }} {{ $user->lastname }}">
                            <div>
                                <h5 class="mb-0">{{ $user->firstname }} {{ $user->lastname }}</h5>
                                <small class="text-muted">{{ $user->email }} | {{ $user->phone ?? 'تلفن ثبت نشده' }}</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            @php
                                $hasPendingDocuments = false;
                                foreach($user->document_completion ?? [] as $doc) {
                                    if($doc->status === 'pending') {
                                        $hasPendingDocuments = true;
                                        break;
                                    }
                                }
                            @endphp

                            @if($hasPendingDocuments)
                            <!-- عملیات گروهی -->
                            <div class="me-3">
                                <div class="btn-group" role="group">
                                    <form action="{{ route('admin.document.bulk-approve-user', $user->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit"
                                                class="btn btn-sm btn-success"
                                                onclick="return confirm('آیا از تایید همه مدارک این کاربر اطمینان دارید؟')"
                                                title="تایید همه">
                                            <i class="fas fa-check-double me-1"></i>
                                            تایید همه
                                        </button>
                                    </form>

                                    <button type="button"
                                            class="btn btn-sm btn-danger bulk-reject-user"
                                            data-bs-toggle="modal"
                                            data-bs-target="#bulkRejectReasonModal"
                                            data-user-id="{{ $user->id }}"
                                            data-user-name="{{ $user->firstname }} {{ $user->lastname }}"
                                            title="رد همه">
                                        <i class="fas fa-times-circle me-1"></i>
                                        رد همه
                                    </button>
                                </div>
                            </div>
                            @endif

                            <div class="progress me-3" style="width: 150px; height: 8px;">
                                <div class="progress-bar {{ $user->is_complete ? 'bg-success' : 'bg-warning' }}"
                                     style="width: {{ $user->completion_percentage }}%"></div>
                            </div>
                            <span class="badge {{ $user->is_complete ? 'bg-success' : 'bg-warning' }}">
                                {{ round($user->completion_percentage) }}% تکمیل
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($requiredDocuments as $docType)
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="border rounded p-3 h-100">
                                @php
                                    $documentTypeText = '';
                                    $documentTypeIcon = '';

                                    switch($docType) {
                                        case 'id':
                                            $documentTypeText = 'کارت ملی جلو';
                                            $documentTypeIcon = 'fa-id-card';
                                            break;
                                        case 'id_back':
                                            $documentTypeText = 'کارت ملی پشت';
                                            $documentTypeIcon = 'fa-id-card';
                                            break;
                                        case 'selfie':
                                            $documentTypeText = 'سلفی';
                                            $documentTypeIcon = 'fa-camera';
                                            break;
                                        case 'consent':
                                            $documentTypeText = 'رضایت‌نامه';
                                            $documentTypeIcon = 'fa-file-signature';
                                            break;
                                    }
                                @endphp

                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas {{ $documentTypeIcon }} text-primary me-2"></i>
                                    <strong>{{ $documentTypeText }}</strong>
                                </div>

                                @if(isset($user->document_completion[$docType]))
                                    @php $document = $user->document_completion[$docType]; @endphp
                                    <div class="text-center">
                                        @if($docType != 'consent')
                                            <img src="{{ config('app.url') }}/storage/{{ $document->file->url }}"
                                                 class="img-thumbnail mb-2 view-document"
                                                 width="80" height="60"
                                                 style="cursor: pointer;"
                                                 data-bs-toggle="modal"
                                                 data-bs-target="#documentModal"
                                                 data-document-url="{{ config('app.url') }}/storage/{{ $document->file->url }}"
                                                 alt="مشاهده مدرک">
                                        @else
                                            <a href="{{ config('app.url') }}/storage/{{ $document->file->url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-file-pdf me-1"></i>
                                                مشاهده PDF
                                            </a>
                                        @endif

                                        @php
                                            $statusClass = '';
                                            $statusText = '';

                                            switch($document->status) {
                                                case 'pending':
                                                    $statusClass = 'bg-warning-subtle text-warning';
                                                    $statusText = 'در انتظار بررسی';
                                                    break;
                                                case 'approved':
                                                    $statusClass = 'bg-success-subtle text-success';
                                                    $statusText = 'تایید شده';
                                                    break;
                                                case 'rejected':
                                                    $statusClass = 'bg-danger-subtle text-danger';
                                                    $statusText = 'رد شده';
                                                    break;
                                            }
                                        @endphp

                                        <div class="mt-2">
                                            <span class="badge {{ $statusClass }} d-block">{{ $statusText }}</span>
                                            @if($document->status === 'rejected' && $document->description)
                                                <small class="text-danger d-block mt-1">
                                                    دلیل رد: {{ $document->description }}
                                                </small>
                                            @endif
                                        </div>

                                        <div class="mt-2">
                                            <small class="text-muted d-block">{{ jdate($document->created_at)->format('Y/m/d') }}</small>
                                        </div>

                                        <!-- عملیات -->
                                        <div class="mt-2 d-flex gap-1 justify-content-center">
                                            <form action="{{ route('admin.document.update', $document->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <input type="hidden" name="status" value="approved">
                                                <button type="submit"
                                                        class="btn btn-sm btn-outline-success"
                                                        onclick="return confirm('آیا از تایید این مدرک اطمینان دارید؟')"
                                                        title="تایید">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>

                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger reject-document"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#rejectReasonModal"
                                                    data-document-id="{{ $document->id }}"
                                                    title="رد">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                @else
                                    <div class="text-center text-muted">
                                        <i class="fas fa-times-circle fa-2x mb-2 text-danger"></i>
                                        <div class="small">ارسال نشده</div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>

                    @if(!empty($user->missing_documents))
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>مدارک ناقص:</strong>
                        @foreach($user->missing_documents as $missingDoc)
                            @php
                                $missingDocText = '';
                                switch($missingDoc) {
                                    case 'id': $missingDocText = 'کارت ملی جلو'; break;
                                    case 'id_back': $missingDocText = 'کارت ملی پشت'; break;
                                    case 'selfie': $missingDocText = 'سلفی'; break;
                                    case 'consent': $missingDocText = 'رضایت‌نامه'; break;
                                }
                            @endphp
                            <span class="badge bg-warning text-dark me-1">{{ $missingDocText }}</span>
                        @endforeach
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-between align-items-center mt-4">
        <div class="text-muted">
            نمایش {{ $users->firstItem() ?? 0 }} تا {{ $users->lastItem() ?? 0 }} از {{ $users->total() ?? 0 }} کاربر
        </div>
        <div class="pagination-container">
            {{ $users->links() }}
        </div>
    </div>
</div>

<!-- Modal for Document View -->
<div class="modal fade" id="documentModal" tabindex="-1" aria-labelledby="documentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="documentModalLabel">مشاهده مدرک</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="document-image" src="" class="img-fluid" alt="مدارک">
            </div>
        </div>
    </div>
</div>

<!-- Modal for Reject Reason -->
<div class="modal fade" id="rejectReasonModal" tabindex="-1" aria-labelledby="rejectReasonModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectReasonModalLabel">دلیل رد مدرک</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="reject-form" action="" method="POST">
                    @csrf
                    <input type="hidden" name="status" value="rejected">
                    <div class="mb-3">
                        <label for="reject-reason" class="form-label">لطفا دلیل رد مدرک را وارد کنید:</label>
                        <textarea class="form-control" name="description" id="reject-reason" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-danger" id="confirm-reject">تایید و رد مدرک</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Bulk Reject Reason -->
<div class="modal fade" id="bulkRejectReasonModal" tabindex="-1" aria-labelledby="bulkRejectReasonModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkRejectReasonModalLabel">دلیل رد همه مدارک</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>توجه:</strong> این عملیات تمام مدارک در انتظار بررسی کاربر <span id="bulk-reject-user-name" class="fw-bold"></span> را رد خواهد کرد.
                </div>
                <form id="bulk-reject-form" action="" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="bulk-reject-reason" class="form-label">لطفا دلیل رد همه مدارک را وارد کنید:</label>
                        <textarea class="form-control" name="description" id="bulk-reject-reason" rows="3" required placeholder="مثال: مدارک ارسالی واضح نیست و نیاز به ارسال مجدد دارد"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-danger" id="confirm-bulk-reject">
                    <i class="fas fa-times-circle me-1"></i>
                    تایید و رد همه مدارک
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Document modal functionality
        const documentModal = document.getElementById('documentModal');
        documentModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const documentUrl = button.getAttribute('data-document-url');

            const documentImage = document.getElementById('document-image');
            documentImage.src = documentUrl;
        });

        // Reject document functionality
        const rejectModal = document.getElementById('rejectReasonModal');
        rejectModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const documentId = button.getAttribute('data-document-id');

            const form = document.getElementById('reject-form');
            form.action = `/admin/document/${documentId}`;
        });

        // Confirm reject button
        document.getElementById('confirm-reject').addEventListener('click', function() {
            const form = document.getElementById('reject-form');
            const reason = document.getElementById('reject-reason').value;

            if (!reason.trim()) {
                alert('لطفا دلیل رد مدرک را وارد کنید');
                return;
            }

            if (confirm('آیا از رد این مدرک اطمینان دارید؟')) {
                form.submit();
            }
        });

        // Bulk reject user functionality
        const bulkRejectModal = document.getElementById('bulkRejectReasonModal');
        bulkRejectModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const userName = button.getAttribute('data-user-name');

            const form = document.getElementById('bulk-reject-form');
            const userNameSpan = document.getElementById('bulk-reject-user-name');

            form.action = `/admin/document/bulk-reject-user/${userId}`;
            userNameSpan.textContent = userName;

            // Clear previous reason
            document.getElementById('bulk-reject-reason').value = '';
        });

        // Confirm bulk reject button
        document.getElementById('confirm-bulk-reject').addEventListener('click', function() {
            const form = document.getElementById('bulk-reject-form');
            const reason = document.getElementById('bulk-reject-reason').value;

            if (!reason.trim()) {
                alert('لطفا دلیل رد همه مدارک را وارد کنید');
                return;
            }

            if (confirm('آیا از رد همه مدارک این کاربر اطمینان دارید؟ این عملیات قابل بازگشت نیست.')) {
                form.submit();
            }
        });

        // Search functionality
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');

        function performSearch() {
            const searchTerm = searchInput.value.trim();
            const currentUrl = new URL(window.location);

            if (searchTerm) {
                currentUrl.searchParams.set('search', searchTerm);
            } else {
                currentUrl.searchParams.delete('search');
            }

            window.location.href = currentUrl.toString();
        }

        searchButton.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Filter functionality
        document.querySelectorAll('.filter-status').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const status = this.getAttribute('data-status');
                const currentUrl = new URL(window.location);

                if (status === 'all') {
                    currentUrl.searchParams.delete('status');
                } else {
                    currentUrl.searchParams.set('status', status);
                }

                window.location.href = currentUrl.toString();
            });
        });

        // Initialize tooltips
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(el => new bootstrap.Tooltip(el));
    });
</script>
@endpush

@endsection
