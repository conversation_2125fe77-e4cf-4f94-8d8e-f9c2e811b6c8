@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h3 class="page-title">جزئیات کارت بانکی</h3>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.card.index') }}">مدیریت کارت‌های بانکی</a></li>
                    <li class="breadcrumb-item active">جزئیات کارت</li>
                </ul>
            </div>
            <div class="col-auto">
                <a href="{{ route('admin.card.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>بازگشت به لیست
                </a>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Card Preview -->
        <div class="col-lg-4 col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">پیش‌نمایش کارت</h4>
                </div>
                <div class="card-body">
                    <div class="credit-card-preview">
                        <div class="credit-card {{ $card->status == 'approved' ? 'approved' : ($card->status == 'rejected' ? 'rejected' : 'pending') }}">
                            <div class="credit-card-header">
                                <div class="bank-logo-small">
                                    @if($card->bank && $card->bank->icon)
                                        <img src="{{ asset('storage/' . $card->bank->icon) }}" alt="{{ $card->bank->name }}">
                                    @else
                                        <i class="fas fa-university"></i>
                                    @endif
                                </div>
                                <div class="chip"><i class="fas fa-microchip"></i></div>
                            </div>
                            <div class="credit-card-body">
                                <div class="card-number">
                                    {{ substr($card->number, 0, 4) }} {{ substr($card->number, 4, 4) }} {{ substr($card->number, 8, 4) }} {{ substr($card->number, 12, 4) }}
                                </div>
                                <div class="card-holder">
                                    <span class="label">نام دارنده کارت</span>
                                    <span class="name">{{ $card->user->firstname ?? '' }} {{ $card->user->lastname ?? '' }}</span>
                                </div>
                            </div>
                            <div class="credit-card-footer">
                                <div class="expiry">
                                    <span class="label">وضعیت</span>
                                    <span class="status-badge">
                                        @if($card->status == 'approved')
                                            <i class="fas fa-check-circle"></i> تایید شده
                                        @elseif($card->status == 'rejected')
                                            <i class="fas fa-times-circle"></i> رد شده
                                        @else
                                            <i class="fas fa-clock"></i> در انتظار
                                        @endif
                                    </span>
                                </div>
                                <div class="bank-name-small">
                                    {{ $card->bank->name ?? 'نامشخص' }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    @if($card->status == 'pending')
                    <div class="card-actions mt-4">
                        <div class="row">
                            <div class="col-6">
                                <form action="{{ route('admin.card.update', $card->id) }}" method="POST">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="approved">
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-check-circle me-2"></i> تایید کارت
                                    </button>
                                </form>
                            </div>
                            <div class="col-6">
                                <form action="{{ route('admin.card.update', $card->id) }}" method="POST">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="rejected">
                                    <button type="submit" class="btn btn-danger w-100">
                                        <i class="fas fa-times-circle me-2"></i> رد کارت
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    @if(!$card->sheba)
                    <div class="mt-4">
                        <form action="" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sync-alt me-2"></i> استعلام شماره شبا
                            </button>
                        </form>
                    </div>
                    @endif
                </div>
            </div>
            
            <!-- User Info Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-title">اطلاعات کاربر</h4>
                </div>
                <div class="card-body">
                    <div class="user-info">
                        <div class="user-header">
                            <div class="avatar avatar-lg">
                                <div class="avatar-initial rounded-circle bg-primary">
                                    {{ substr($card->user->firstname ?? 'U', 0, 1) }}
                                </div>
                            </div>
                            <div class="user-details">
                                <h5 class="user-name">{{ $card->user->firstname ?? '' }} {{ $card->user->lastname ?? '' }}</h5>
                                <p class="user-phone">{{ $card->user->phone ?? 'بدون شماره تماس' }}</p>
                            </div>
                        </div>
                        <div class="user-body mt-4">
                            <div class="info-item">
                                <span class="info-label">کد ملی:</span>
                                <span class="info-value">{{ $card->user->national_id ?? 'ثبت نشده' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">ایمیل:</span>
                                <span class="info-value">{{ $card->user->email ?? 'ثبت نشده' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">تاریخ عضویت:</span>
                                <span class="info-value">{{ $card->user->created_at ? jdate($card->user->created_at)->format('Y/m/d') : 'نامشخص' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">وضعیت احراز هویت:</span>
                                <span class="info-value">
                                    @if($card->user && $card->user->is_verified)
                                        <span class="badge bg-success">تایید شده</span>
                                    @else
                                        <span class="badge bg-warning text-dark">تایید نشده</span>
                                    @endif
                                </span>
                            </div>
                        </div>
                        <div class="user-footer mt-4">
                            <a href="{{ route('admin.users.show', $card->user_id) }}" class="btn btn-primary w-100">
                                <i class="fas fa-user me-2"></i> مشاهده پروفایل کاربر
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Card Details -->
        <div class="col-lg-8 col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">جزئیات کارت بانکی</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">شماره کارت:</span>
                                <span class="detail-value card-number-display">
                                    {{ substr($card->number, 0, 4) }}-{{ substr($card->number, 4, 4) }}-{{ substr($card->number, 8, 4) }}-{{ substr($card->number, 12, 4) }}
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">بانک صادرکننده:</span>
                                <span class="detail-value">
                                    <div class="d-flex align-items-center">
                                        @if($card->bank && $card->bank->icon)
                                            <img src="{{ asset('storage/' . $card->bank->icon) }}" 
                                                 alt="{{ $card->bank->name }}" 
                                                 class="bank-icon me-2">
                                        @else
                                            <div class="bank-icon-placeholder me-2">
                                                <i class="fas fa-university"></i>
                                            </div>
                                        @endif
                                        {{ $card->bank->name ?? 'نامشخص' }}
                                    </div>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">شماره شبا:</span>
                                <span class="detail-value">
                                    @if($card->sheba)
                                        <span class="sheba-number">{{ $card->sheba }}</span>
                                    @else
                                        <span class="text-muted">ثبت نشده</span>
                                    @endif
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">وضعیت:</span>
                                <span class="detail-value">
                                    @if($card->status == 'approved')
                                        <span class="badge bg-success">تایید شده</span>
                                    @elseif($card->status == 'rejected')
                                        <span class="badge bg-danger">رد شده</span>
                                    @else
                                        <span class="badge bg-warning text-dark">در انتظار تایید</span>
                                    @endif
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">تاریخ ثبت:</span>
                                <span class="detail-value">{{ $card->created_at ? jdate($card->created_at)->format('Y/m/d H:i:s') : 'نامشخص' }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">آخرین بروزرسانی:</span>
                                <span class="detail-value">{{ $card->updated_at ? jdate($card->updated_at)->format('Y/m/d H:i:s') : 'نامشخص' }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <h5 class="mb-3">تاریخچه تغییرات</h5>
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-point bg-primary">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">ثبت کارت</h6>
                                <p class="text-muted mb-0">{{ $card->created_at ? jdate($card->created_at)->format('Y/m/d H:i:s') : 'نامشخص' }}</p>
                            </div>
                        </div>
                        
                        @if($card->status != 'pending')
                        <div class="timeline-item">
                            <div class="timeline-point {{ $card->status == 'approved' ? 'bg-success' : 'bg-danger' }}">
                                <i class="fas {{ $card->status == 'approved' ? 'fa-check' : 'fa-times' }}"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">{{ $card->status == 'approved' ? 'تایید کارت' : 'رد کارت' }}</h6>
                                <p class="text-muted mb-0">{{ $card->updated_at ? jdate($card->updated_at)->format('Y/m/d H:i:s') : 'نامشخص' }}</p>
                            </div>
                        </div>
                        @endif
                        
                        @if($card->sheba)
                        <div class="timeline-item">
                            <div class="timeline-point bg-info">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">استعلام شماره شبا</h6>
                                <p class="text-muted mb-0">{{ $card->updated_at ? jdate($card->updated_at)->format('Y/m/d H:i:s') : 'نامشخص' }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Other Cards -->
            @if($userCards && $userCards->count() > 0)
            <div class="card mt-4">
                <div class="card-header">
                    <h4 class="card-title">سایر کارت‌های کاربر</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>بانک</th>
                                    <th>شماره کارت</th>
                                    <th>وضعیت</th>
                                    <th>تاریخ ثبت</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($userCards as $userCard)
                                    @if($userCard->id != $card->id)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($userCard->bank && $userCard->bank->icon)
                                                    <img src="{{ asset('storage/' . $userCard->bank->icon) }}" 
                                                         alt="{{ $userCard->bank->name }}" 
                                                         class="bank-icon me-2">
                                                @else
                                                    <div class="bank-icon-placeholder me-2">
                                                        <i class="fas fa-university"></i>
                                                    </div>
                                                @endif
                                                {{ $userCard->bank->name ?? 'نامشخص' }}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="card-number-display">{{ substr($userCard->number, 0, 4) }}-{{ substr($userCard->number, 4, 4) }}-{{ substr($userCard->number, 8, 4) }}-{{ substr($userCard->number, 12, 4) }}</span>
                                        </td>
                                        <td>
                                            @if($userCard->status == 'approved')
                                                <span class="badge bg-success">تایید شده</span>
                                            @elseif($userCard->status == 'rejected')
                                                <span class="badge bg-danger">رد شده</span>
                                            @else
                                                <span class="badge bg-warning text-dark">در انتظار تایید</span>
                                            @endif
                                        </td>
                                        <td>{{ $userCard->created_at ? jdate($userCard->created_at)->format('Y/m/d') : 'نامشخص' }}</td>
                                        <td>
                                            <a href="{{ route('admin.card.show', $userCard->id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> مشاهده
                                            </a>
                                        </td>
                                    </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<style>
    /* Page Header */
    .page-header {
        margin-bottom: 1.5rem;
    }
    .page-title {
        color: #333;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    .breadcrumb {
        background: transparent;
        padding: 0;
        margin: 0;
    }
    
    /* Credit Card */
    .credit-card-preview {
        display: flex;
        justify-content: center;
    }
    .credit-card {
        background: linear-gradient(135deg, #4a6cf7, #6e8efb);
        border-radius: 15px;
        padding: 1.5rem;
        color: white;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
        width: 100%;
        max-width: 400px;
    }
    .credit-card.approved {
        background: linear-gradient(135deg, #3c9d50, #5cb85c);
    }
    .credit-card.rejected {
        background: linear-gradient(135deg, #c9302c, #d9534f);
    }
    .credit-card.pending {
        background: linear-gradient(135deg, #ec971f, #f0ad4e);
    }
    .credit-card::before {
        content: '';
        position: absolute;
        top: -50px;
        right: -50px;
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        z-index: 1;
    }
    .credit-card::after {
        content: '';
        position: absolute;
        bottom: -80px;
        left: -80px;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.05);
        z-index: 1;
    }
    .credit-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }
    .bank-logo-small {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.2);
        padding: 5px;
    }
    .bank-logo-small img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    .bank-logo-small i {
        font-size: 1.5rem;
        color: white;
    }
    .chip {
        width: 40px;
        height: 30px;
        background: linear-gradient(135deg, #ffd700, #ffcc00);
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .chip i {
        color: #333;
        font-size: 0.8rem;
    }
    .credit-card-body {
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 2;
    }
    .card-number {
        font-size: 1.25rem;
        letter-spacing: 2px;
        margin-bottom: 1rem;
        font-family: monospace;
    }
    .card-holder {
        display: flex;
        flex-direction: column;
    }
    .card-holder .label {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 0.25rem;
    }
    .card-holder .name {
        font-size: 1rem;
        text-transform: uppercase;
    }
    .credit-card-footer {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        position: relative;
        z-index: 2;
    }
    .expiry {
        display: flex;
        flex-direction: column;
    }
    .expiry .label {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 0.25rem;
    }
    .status-badge {
        font-size: 0.9rem;
        font-weight: 600;
    }
    .bank-name-small {
        font-size: 1rem;
        font-weight: 600;
        text-align: right;
    }
    
    /* User Info */
    .user-info {
        padding: 1rem 0;
    }
    .user-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    .avatar-lg {
        width: 64px;
        height: 64px;
    }
    .user-details {
        margin-right: 1rem;
    }
    .user-name {
        margin-bottom: 0.25rem;
        font-size: 1.25rem;
    }
    .user-phone {
        color: #6c757d;
        margin-bottom: 0;
    }
    .info-item {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    .info-item:last-child {
        border-bottom: none;
    }
    .info-label {
        color: #6c757d;
        font-weight: 500;
    }
    .info-value {
        font-weight: 600;
    }
    
    /* Card Details */
    .detail-item {
        margin-bottom: 1.5rem;
    }
    .detail-label {
        display: block;
        color: #6c757d;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }
    .detail-value {
        font-weight: 600;
        font-size: 1rem;
    }
    .card-number-display {
        font-family: monospace;
        letter-spacing: 1px;
    }
    .sheba-number {
        font-family: monospace;
        font-size: 0.9rem;
    }
    .bank-icon {
        width: 24px;
        height: 24px;
        object-fit: contain;
    }
    .bank-icon-placeholder {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 0.75rem;
    }
    
    /* Timeline */
    .timeline {
        position: relative;
        padding-left: 3rem;
        margin-bottom: 1rem;
    }
    .timeline:before {
        content: '';
        position: absolute;
        left: 0.85rem;
        top: 0;
        height: 100%;
        width: 2px;
        background-color: #e9ecef;
    }
    .timeline-item {
        position: relative;
        padding-bottom: 1.5rem;
    }
    .timeline-item:last-child {
        padding-bottom: 0;
    }
    .timeline-point {
        position: absolute;
        left: -3rem;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        z-index: 2;
    }
    .timeline-content {
        padding-left: 0.5rem;
    }
    .bg-primary {
        background-color: #4a6cf7 !important;
    }
    .bg-success {
        background-color: #5cb85c !important;
    }
    .bg-danger {
        background-color: #d9534f !important;
    }
    .bg-info {
        background-color: #5bc0de !important;
    }
</style>
@endsection
