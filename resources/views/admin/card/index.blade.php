@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h3 class="page-title">مدیریت کارت‌های بانکی</h3>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
                    <li class="breadcrumb-item active">مدیریت کارت‌های بانکی</li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="row">
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">تعداد کل کارت‌ها</h5>
                            <h2 class="mb-0">{{ $cards->total() }}</h2>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-credit-card fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">کارت‌های تایید شده</h5>
                            <h2 class="mb-0">{{ $cards->where('status', 'approved')->count() }}</h2>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-gradient-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">در انتظار تایید</h5>
                            <h2 class="mb-0">{{ $cards->where('status', 'pending')->count() }}</h2>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-gradient-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">کارت‌های رد شده</h5>
                            <h2 class="mb-0">{{ $cards->where('status', 'rejected')->count() }}</h2>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search & Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('admin.card.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">جستجو</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="جستجو بر اساس شماره کارت..." value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">وضعیت</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">همه</option>
                        <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>تایید شده</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>در انتظار تایید</option>
                        <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>رد شده</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="bank_id" class="form-label">بانک</label>
                    <select class="form-select" id="bank_id" name="bank_id">
                        <option value="">همه بانک‌ها</option>
                        @foreach($banks as $bank)
                            <option value="{{ $bank->id }}" {{ request('bank_id') == $bank->id ? 'selected' : '' }}>{{ $bank->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>جستجو
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Cards Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">لیست کارت‌های بانکی</h4>
                        <div class="view-options">
                            <button type="button" class="btn btn-outline-primary" id="grid-view-btn">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button type="button" class="btn btn-outline-primary active" id="list-view-btn">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Grid View (Hidden by Default) -->
                    <div class="row" id="grid-view" style="display: none;">
                        @forelse($cards as $card)
                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-4">
                            <div class="card-item">
                                <div class="credit-card {{ $card->status == 'approved' ? 'approved' : ($card->status == 'rejected' ? 'rejected' : 'pending') }}">
                                    <div class="credit-card-header">
                                        <div class="bank-logo-small">
                                            @if($card->bank && $card->bank->icon)
                                                <img src="{{ asset('storage/' . $card->bank->icon) }}" alt="{{ $card->bank->name }}" style="width: 120px;">
                                            @else
                                                <i class="fas fa-university"></i>
                                            @endif
                                        </div>
                                        <div class="chip"><i class="fas fa-microchip"></i></div>
                                    </div>
                                    <div class="credit-card-body">
                                        <div class="card-number">
                                            {{ substr($card->number, 0, 4) }} {{ substr($card->number, 4, 4) }} {{ substr($card->number, 8, 4) }} {{ substr($card->number, 12, 4) }}
                                        </div>
                                        <div class="card-holder">
                                            <span class="label">نام دارنده کارت</span>
                                            <span class="name">{{ $card->user->firstname ?? '' }} {{ $card->user->lastname ?? '' }}</span>
                                        </div>
                                    </div>
                                    <div class="credit-card-footer">
                                        <div class="expiry">
                                            <span class="label">وضعیت</span>
                                            <span class="status-badge">
                                                @if($card->status == 'approved')
                                                    <i class="fas fa-check-circle"></i> تایید شده
                                                @elseif($card->status == 'rejected')
                                                    <i class="fas fa-times-circle"></i> رد شده
                                                @else
                                                    <i class="fas fa-clock"></i> در انتظار
                                                @endif
                                            </span>
                                        </div>
                                        <div class="bank-name-small">
                                            {{ $card->bank->name ?? 'نامشخص' }}
                                        </div>
                                    </div>
                                    <div class="card-actions">
                                        <a href="{{ route('admin.card.show', $card->id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> مشاهده
                                        </a>
                                        @if($card->status == 'pending')
                                        <form action="{{ route('admin.card.update', $card->id) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="approved">
                                            <button type="submit" class="btn btn-sm btn-success">
                                                <i class="fas fa-check"></i> تایید
                                            </button>
                                        </form>
                                        <form action="{{ route('admin.card.update', $card->id) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="rejected">
                                            <button type="submit" class="btn btn-sm btn-danger">
                                                <i class="fas fa-times"></i> رد
                                            </button>
                                        </form>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="col-12">
                            <div class="empty-state">
                                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                <h5>هیچ کارت بانکی یافت نشد</h5>
                                <p class="text-muted">با استفاده از فیلترهای بالا می‌توانید جستجوی دقیق‌تری انجام دهید.</p>
                            </div>
                        </div>
                        @endforelse
                    </div>
                    
                    <!-- List View -->
                    <div class="table-responsive" id="list-view">
                        <table class="table table-hover table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th width="60">#</th>
                                    <th>کاربر</th>
                                    <th>بانک</th>
                                    <th>شماره کارت</th>
                                    <th>شماره شبا</th>
                                    <th>وضعیت</th>
                                    <th>تاریخ ثبت</th>
                                    <th width="200">عملیات</th>
                                </tr>
                            </thead>\
                            
                            <tbody>
                                @forelse($cards as $key => $card)
                                <tr>
                                    <td>{{ $cards->firstItem() + $key }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar avatar-sm me-2">
                                                <div class="avatar-initial rounded-circle bg-primary">
                                                    {{ substr($card->user->firstname ?? 'U', 0, 1) }}
                                                </div>
                                            </div>
                                            <div>
                                                <span class="fw-bold">{{ $card->user->firstname ?? '' }} {{ $card->user->lastname ?? '' }}</span>
                                                <small class="d-block text-muted">{{ $card->user->phone ?? '' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($card->bank && $card->bank->icon)
                                                <img src="{{ asset('storage/' . $card->bank->icon) }}" 
                                                     alt="{{ $card->bank->name }}" 
                                                     style="width: 120px;"
                                                     class="bank-icon me-2">
                                            @else
                                                <div class="bank-icon-placeholder me-2">
                                                    <i class="fas fa-university"></i>
                                                </div>
                                            @endif
                                            <span>{{ $card->bank->name ?? 'نامشخص' }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="card-number-display">{{ substr($card->number, 0, 4) }}-{{ substr($card->number, 4, 4) }}-{{ substr($card->number, 8, 4) }}-{{ substr($card->number, 12, 4) }}</span>
                                    </td>
                                    <td>
                                        @if($card->sheba)
                                            <span class="sheba-number">{{ $card->sheba }}</span>
                                        @else
                                            <form action="" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-sync-alt"></i> استعلام شبا
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                    <td>
                                        @if($card->status == 'approved')
                                            <span class="badge bg-success">تایید شده</span>
                                        @elseif($card->status == 'rejected')
                                            <span class="badge bg-danger">رد شده</span>
                                        @else
                                            <span class="badge bg-warning text-dark">در انتظار تایید</span>
                                        @endif
                                    </td>
                                    <td>{{ $card->created_at ? jdate($card->created_at)->format('Y/m/d') : 'N/A' }}</td>
                                    <td>
                                        <div class="d-flex gap-2">
                                            <a href="{{ route('admin.card.show', $card->id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> مشاهده
                                            </a>
                                            @if($card->status == 'pending')
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton{{ $card->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ $card->id }}">
                                                    <li>
                                                        <form action="{{ route('admin.card.update', $card->id) }}" method="POST">
                                                            @csrf
                                                            @method('PUT')
                                                            <input type="hidden" name="status" value="approved">
                                                            <button type="submit" class="dropdown-item text-success">
                                                                <i class="fas fa-check me-2"></i> تایید کارت
                                                            </button>
                                                        </form>
                                                    </li>
                                                    <li>
                                                        <form action="{{ route('admin.card.update', $card->id) }}" method="POST">
                                                            @csrf
                                                            @method('PUT')
                                                            <input type="hidden" name="status" value="rejected">
                                                            <button type="submit" class="dropdown-item text-danger">
                                                                <i class="fas fa-times me-2"></i> رد کارت
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-5">
                                        <div class="empty-state">
                                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                            <h5>هیچ کارت بانکی یافت نشد</h5>
                                            <p class="text-muted">با استفاده از فیلترهای بالا می‌توانید جستجوی دقیق‌تری انجام دهید.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($cards->hasPages())
                <div class="card-footer">
                    <div class="d-flex justify-content-center">
                        {{ $cards->withQueryString()->links() }}
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
    /* Modern Theme Variables */
    :root {
        --primary-gradient: linear-gradient(135deg, #6e8efb, #4a6cf7);
        --success-gradient: linear-gradient(135deg, #2ecc71, #27ae60);
        --warning-gradient: linear-gradient(135deg, #f1c40f, #f39c12);
        --danger-gradient: linear-gradient(135deg, #e74c3c, #c0392b);
        --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
        --hover-transform: translateY(-5px);
        --transition-speed: 0.3s;
    }

    /* Page Header Enhancement */
    .page-header {
        margin-bottom: 2rem;
        position: relative;
        padding: 2rem;
        background: white;
        border-radius: 15px;
        box-shadow: var(--card-shadow);
    }
    
    .page-title {
        font-size: 1.8rem;
        font-weight: 700;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 0.8rem;
    }

    /* Enhanced Stats Cards */
    .card {
        border: none;
        border-radius: 15px;
        transition: all var(--transition-speed) ease;
    }

    .card:hover {
        transform: var(--hover-transform);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
    }

    .bg-gradient-primary { background: var(--primary-gradient); }
    .bg-gradient-success { background: var(--success-gradient); }
    .bg-gradient-warning { background: var(--warning-gradient); }
    .bg-gradient-danger { background: var(--danger-gradient); }

    .card-icon {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 65px;
        height: 65px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
        transition: transform var(--transition-speed) ease;
    }

    .card:hover .card-icon {
        transform: rotate(360deg);
    }

    /* Modernized Credit Card Design */
    .credit-card {
        background: var(--primary-gradient);
        border-radius: 20px;
        padding: 2rem;
        color: white;
        box-shadow: var(--card-shadow);
        position: relative;
        overflow: hidden;
        transition: all var(--transition-speed) ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        backdrop-filter: blur(10px);
    }

    .credit-card.approved { background: var(--success-gradient); }
    .credit-card.rejected { background: var(--danger-gradient); }
    .credit-card.pending { background: var(--warning-gradient); }

    .credit-card::before,
    .credit-card::after {
        content: '';
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: pulse 3s infinite;
    }

    .credit-card::before {
        width: 150px;
        height: 150px;
        top: -50px;
        right: -50px;
        animation-delay: 0s;
    }

    .credit-card::after {
        width: 200px;
        height: 200px;
        bottom: -80px;
        left: -80px;
        animation-delay: 1.5s;
    }

    @keyframes pulse {
        0% { transform: scale(1); opacity: 0.5; }
        50% { transform: scale(1.2); opacity: 0.2; }
        100% { transform: scale(1); opacity: 0.5; }
    }

    /* Enhanced Search & Filter Section */
    .search-filter-card {
        background: white;
        border-radius: 15px;
        box-shadow: var(--card-shadow);
        margin-bottom: 2rem;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #eef2f7;
        padding: 0.75rem 1rem;
        transition: all var(--transition-speed) ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #6e8efb;
        box-shadow: 0 0 0 0.2rem rgba(110, 142, 251, 0.1);
    }

    /* Table Enhancements */
    .table {
        border-collapse: separate;
        border-spacing: 0 0.5rem;
    }

    .table tbody tr {
        background: white;
        box-shadow: var(--card-shadow);
        border-radius: 15px;
        transition: transform var(--transition-speed) ease;
    }

    .table tbody tr:hover {
        transform: translateX(5px);
    }

    .table th {
        border: none;
        background: #f8f9fa;
        padding: 1rem;
    }

    .table td {
        border: none;
        padding: 1rem;
        vertical-align: middle;
    }

    /* Status Badge Enhancement */
    .badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
    }

    /* Animation Classes */
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Responsive Enhancements */
    @media (max-width: 768px) {
        .page-header {
            padding: 1.5rem;
        }
        
        .credit-card {
            margin-bottom: 1rem;
        }
    }
</style>

@push('scripts')
<script>
    $(document).ready(function() {
        // Enhanced View Toggle with Animation
        function toggleView(view) {
            if (view === 'grid') {
                $('#grid-view').fadeIn(300);
                $('#list-view').fadeOut(300);
                $('#grid-view-btn').addClass('active');
                $('#list-view-btn').removeClass('active');
            } else {
                $('#list-view').fadeIn(300);
                $('#grid-view').fadeOut(300);
                $('#list-view-btn').addClass('active');
                $('#grid-view-btn').removeClass('active');
            }
            localStorage.setItem('cardViewPreference', view);
        }

        $('#grid-view-btn').click(() => toggleView('grid'));
        $('#list-view-btn').click(() => toggleView('list'));

        // Load saved preference with animation
        const viewPreference = localStorage.getItem('cardViewPreference');
        if (viewPreference === 'grid') {
            toggleView('grid');
        }

        // Add entrance animation to cards
        $('.credit-card').each(function(i) {
            $(this).addClass('fade-in').css({
                'animation-delay': `${i * 0.1}s`
            });
        });

        // Enhanced hover effects
        $('.credit-card').hover(function() {
            $(this).find('.bank-logo-small').css({
                'transform': 'scale(1.1) rotate(5deg)',
                'transition': 'all 0.3s ease'
            });
            $(this).find('.chip').css({
                'transform': 'scale(1.1)',
                'transition': 'all 0.3s ease'
            });
        }, function() {
            $(this).find('.bank-logo-small, .chip').css({
                'transform': 'scale(1) rotate(0deg)',
                'transition': 'all 0.3s ease'
            });
        });

        // Add smooth scroll to top after search
        $('form').submit(function() {
            $('html, body').animate({
                scrollTop: 0
            }, 500);
        });

        // Enhanced dropdown animations
        $('.dropdown-toggle').on('show.bs.dropdown', function() {
            $(this).find('.dropdown-menu').first().stop(true, true).slideDown(200);
        });

        $('.dropdown-toggle').on('hide.bs.dropdown', function() {
            $(this).find('.dropdown-menu').first().stop(true, true).slideUp(200);
        });
    });
</script>
@endpush
@endsection
