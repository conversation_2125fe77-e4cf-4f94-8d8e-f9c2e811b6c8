@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h3 class="page-title">مدیریت بانک‌ها</h3>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
                    <li class="breadcrumb-item active">مدیریت بانک‌ها</li>
                </ul>
            </div>
            <div class="col-auto">
                <a href="{{ route('admin.banks.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-2"></i>ایجاد بانک جدید
                </a>
            </div>
        </div>
    </div>



    <!-- Banks Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">لیست بانک‌های ایران</h4>
                        <div class="view-options">
                            <button type="button" class="btn btn-outline-primary" id="grid-view-btn">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button type="button" class="btn btn-outline-primary active" id="list-view-btn">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Grid View (Hidden by Default) -->
                    <div class="row" id="grid-view" style="display: none;">
                        @forelse($banks as $bank)
                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-4">
                            <div class="bank-card">
                                <div class="bank-card-header">
                                    <div class="bank-logo-lg">
                                        @if($bank->icon)
                                            <img src="{{ asset('storage/' . $bank->icon) }}" alt="{{ $bank->name }}">
                                        @else
                                            <div class="no-logo-lg">
                                                <i class="fas fa-university"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <h5 class="bank-name">{{ $bank->name }}</h5>
                                </div>
                                <div class="bank-card-body">
                                    <div class="bank-info">
                                        <div class="info-item">
                                            <span class="info-label">پیشوند کارت:</span>
                                            <span class="info-value">{{ $bank->prefix }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="bank-card-footer">
                                    <a href="{{ route('admin.banks.edit', $bank->id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-edit"></i> ویرایش
                                    </a>
                                    <form action="{{ route('admin.banks.destroy', $bank->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger"
                                                onclick="return confirm('آیا از حذف این بانک اطمینان دارید؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="col-12">
                            <div class="empty-state">
                                <i class="fas fa-university fa-3x text-muted mb-3"></i>
                                <h5>هیچ بانکی یافت نشد</h5>
                                <p class="text-muted">می‌توانید با کلیک بر روی دکمه «ایجاد بانک جدید» اولین بانک را اضافه کنید.</p>
                                <a href="{{ route('admin.banks.create') }}" class="btn btn-primary mt-3">
                                    <i class="fas fa-plus-circle me-2"></i>ایجاد بانک جدید
                                </a>
                            </div>
                        </div>
                        @endforelse
                    </div>

                    <!-- List View -->
                    <div class="table-responsive" id="list-view">
                        <table class="table table-hover table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th width="80">#</th>
                                    <th width="100">لوگو</th>
                                    <th>نام بانک</th>
                                    <th>پیشوند کارت</th>
                                    <th>وضعیت</th>
                                    <th>تاریخ ایجاد</th>
                                    <th width="200">عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($banks as $key => $bank)
                                <tr>
                                    <td>{{ $banks->firstItem() + $key }}</td>
                                    <td>
                                        <div class="bank-logo-container">
                                            @if($bank->icon)
                                                <img src="{{ asset('storage/' . $bank->icon) }}"
                                                    alt="{{ $bank->name }}"
                                                    class="bank-logo">
                                            @else
                                                <div class="no-logo">
                                                    <i class="fas fa-university"></i>
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="fw-bold">{{ $bank->name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ $bank->prefix }}</span>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox"
                                                data-id="{{ $bank->id }}"
                                                {{ $bank->status ? 'checked' : '' }}>
                                        </div>
                                    </td>
                                    <td>{{ $bank->created_at ? $bank->created_at->format('Y/m/d') : 'N/A' }}</td>
                                    <td>
                                        <div class="d-flex gap-2">
                                            <a href="{{ route('admin.banks.edit', $bank->id) }}"
                                            class="btn btn-sm btn-info">
                                                <i class="fas fa-edit"></i> ویرایش
                                            </a>
                                            <form action="{{ route('admin.banks.destroy', $bank->id) }}"
                                                method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                        class="btn btn-sm btn-danger"
                                                        onclick="return confirm('آیا از حذف این بانک اطمینان دارید؟')">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="empty-state">
                                            <i class="fas fa-database fa-3x text-muted mb-3"></i>
                                            <h5>هیچ بانکی یافت نشد</h5>
                                            <p class="text-muted">می‌توانید با کلیک بر روی دکمه «ایجاد بانک جدید» اولین بانک را اضافه کنید.</p>
                                            <a href="{{ route('admin.banks.create') }}" class="btn btn-primary mt-3">
                                                <i class="fas fa-plus-circle me-2"></i>ایجاد بانک جدید
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($banks->hasPages())
                <div class="card-footer">
                    <div class="d-flex justify-content-center">
                        {{ $banks->withQueryString()->links('pagination::bootstrap-5') }}
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
    /* Page Header */
    .page-header {
        margin-bottom: 1.5rem;
    }
    .page-title {
        color: #333;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    .breadcrumb {
        background: transparent;
        padding: 0;
        margin: 0;
    }

    /* Stats Cards */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #6e8efb, #4a6cf7);
    }
    .bg-gradient-success {
        background: linear-gradient(135deg, #5cb85c, #3c9d50);
    }
    .bg-gradient-info {
        background: linear-gradient(135deg, #5bc0de, #3db5d8);
    }
    .bg-gradient-warning {
        background: linear-gradient(135deg, #f0ad4e, #ec971f);
    }
    .card-icon {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* View Options */
    .view-options .btn {
        padding: 0.375rem 0.75rem;
        margin-right: 0.5rem;
    }
    .view-options .btn.active {
        background-color: #4a6cf7;
        color: white;
        border-color: #4a6cf7;
    }

    /* Bank Card (Grid View) */
    .bank-card {
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .bank-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .bank-card-header {
        padding: 1.5rem;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-bottom: 1px solid #e9ecef;
    }

    .bank-logo-lg {
        width: 100px;
        height: 100px;
        margin: 0 auto 1rem;
        border-radius: 15px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        padding: 10px;
    }

    .bank-logo-lg img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .no-logo-lg {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #e9ecef;
        color: #6c757d;
        font-size: 2rem;
    }

    .bank-name {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0;
        color: #333;
    }

    .bank-card-body {
        padding: 1.5rem;
        flex-grow: 1;
    }

    .bank-info {
        margin-bottom: 1rem;
    }

    .info-item {
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 8px;
    }

    .info-label {
        font-weight: 500;
        color: #6c757d;
    }

    .info-value {
        font-weight: 600;
        color: #333;
    }

    .bank-card-footer {
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
    }

    /* Bank Logo (List View) */
    .bank-logo-container {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }
    .bank-logo {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    .no-logo {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #e9ecef;
        color: #6c757d;
        font-size: 1.5rem;
    }

    /* Status Toggle */
    .form-switch .form-check-input {
        width: 3em;
        height: 1.5em;
        cursor: pointer;
    }

    /* Empty State */
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
    }
</style>

@push('scripts')
<script>
    $(document).ready(function() {
        // View toggle functionality
        $('#grid-view-btn').click(function() {
            $(this).addClass('active');
            $('#list-view-btn').removeClass('active');
            $('#grid-view').show();
            $('#list-view').hide();
            // Save preference to localStorage
            localStorage.setItem('bankViewPreference', 'grid');
        });

        $('#list-view-btn').click(function() {
            $(this).addClass('active');
            $('#grid-view-btn').removeClass('active');
            $('#grid-view').hide();
            $('#list-view').show();
            // Save preference to localStorage
            localStorage.setItem('bankViewPreference', 'list');
        });

        // Load saved preference if exists
        const viewPreference = localStorage.getItem('bankViewPreference');
        if (viewPreference === 'grid') {
            $('#grid-view-btn').click();
        }

        // Status toggle functionality
        $('.status-toggle').on('change', function() {
            const bankId = $(this).data('id');
            const isChecked = $(this).prop('checked');
            const self = this;

            $.ajax({
                url: '{{ route("admin.banks.toggle-status") }}',
                type: 'POST',
                data: {
                    id: bankId,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                    } else {
                        toastr.error('خطا در تغییر وضعیت');
                        // Revert the toggle if there was an error
                        $(self).prop('checked', !isChecked);
                    }
                },
                error: function() {
                    toastr.error('خطا در ارتباط با سرور');
                    // Revert the toggle if there was an error
                    $(self).prop('checked', !isChecked);
                }
            });
        });

        // Add animation effects
        $('.bank-card').hover(function() {
            $(this).find('.bank-logo-lg').css('transform', 'scale(1.05)');
        }, function() {
            $(this).find('.bank-logo-lg').css('transform', 'scale(1)');
        });
    });
</script>
@endpush
@endsection
