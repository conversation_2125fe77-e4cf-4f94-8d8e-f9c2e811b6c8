@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h3 class="page-title">{{ isset($bank) ? 'ویرایش بانک' : 'ایجاد بانک جدید' }}</h3>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.banks.index') }}">مدیریت بانک‌ها</a></li>
                    <li class="breadcrumb-item active">{{ isset($bank) ? 'ویرایش بانک' : 'ایجاد بانک جدید' }}</li>
                </ul>
            </div>
            <div class="col-auto">
                <a href="{{ route('admin.banks.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>بازگشت به لیست
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Form Card -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">اطلاعات بانک</h4>
                </div>
                <div class="card-body">
                    <form action="{{ isset($bank) ? route('admin.banks.update', $bank->id) : route('admin.banks.store') }}"
                          method="POST"
                          enctype="multipart/form-data"
                          id="bank-form">
                        @csrf
                        @if(isset($bank))
                            @method('PUT')
                        @endif

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">نام بانک <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-university"></i></span>
                                        <input type="text"
                                               name="name"
                                               id="name"
                                               class="form-control @error('name') is-invalid @enderror"
                                               value="{{ old('name', $bank->name ?? '') }}"
                                               required>
                                    </div>
                                    @error('name')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="prefix" class="form-label">پیشوند کارت <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-credit-card"></i></span>
                                        <input type="text"
                                               name="prefix"
                                               id="prefix"
                                               class="form-control @error('prefix') is-invalid @enderror"
                                               value="{{ old('prefix', $bank->prefix ?? '') }}"
                                               required
                                               maxlength="6">
                                    </div>
                                    @error('prefix')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @else
                                        <small class="form-text text-muted">پیشوند 6 رقمی کارت‌های بانکی (مثال: 603799)</small>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="icon" class="form-label">لوگوی بانک</label>
                                    <div class="custom-file-upload">
                                        <input type="file"
                                               name="icon"
                                               id="icon"
                                               class="form-control @error('icon') is-invalid @enderror"
                                               accept="image/*"
                                               onchange="previewImage(this)">
                                        <div class="upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <span>برای آپلود لوگو کلیک کنید یا فایل را اینجا رها کنید</span>
                                        </div>
                                    </div>
                                    @error('icon')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">فرمت‌های مجاز: JPG, PNG, GIF, SVG - حداکثر سایز: 2MB</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas {{ isset($bank) ? 'fa-save' : 'fa-plus-circle' }} me-2"></i>
                                {{ isset($bank) ? 'بروزرسانی بانک' : 'ایجاد بانک' }}
                            </button>
                            <a href="{{ route('admin.banks.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>انصراف
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">پیش‌نمایش بانک</h4>
                </div>
                <div class="card-body">
                    <div class="bank-preview">
                        <div class="bank-preview-card">
                            <div class="bank-preview-header">
                                <div class="bank-preview-logo" id="logo-preview">
                                    @if(isset($bank) && $bank->icon)
                                        <img src="{{ asset('storage/' . $bank->icon) }}" alt="{{ $bank->name }}">
                                    @else
                                        <i class="fas fa-university"></i>
                                    @endif
                                </div>
                                <h5 class="bank-preview-name" id="name-preview">{{ $bank->name ?? 'نام بانک' }}</h5>
                            </div>
                            <div class="bank-preview-body">
                                <div class="bank-preview-info">
                                    <div class="info-preview-item">
                                        <span class="info-preview-label">پیشوند کارت:</span>
                                        <span class="info-preview-value" id="prefix-preview">{{ $bank->prefix ?? '------' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bank-card-preview mt-4">
                        <div class="credit-card">
                            <div class="credit-card-header">
                                <div class="bank-logo-small" id="card-logo-preview">
                                    @if(isset($bank) && $bank->icon)
                                        <img src="{{ asset('storage/' . $bank->icon) }}" alt="{{ $bank->name }}">
                                    @else
                                        <i class="fas fa-university"></i>
                                    @endif
                                </div>
                                <div class="chip"><i class="fas fa-microchip"></i></div>
                            </div>
                            <div class="credit-card-body">
                                <div class="card-number">
                                    <span id="card-prefix-preview">{{ $bank->prefix ?? '603799' }}</span><span>** **** ****</span>
                                </div>
                                <div class="card-holder">
                                    <span class="label">نام دارنده کارت</span>
                                    <span class="name">محمد محمدی</span>
                                </div>
                            </div>
                            <div class="credit-card-footer">
                                <div class="expiry">
                                    <span class="label">تاریخ انقضا</span>
                                    <span class="date">04/05</span>
                                </div>
                                <div class="bank-name-small" id="card-bank-name">
                                    {{ $bank->name ?? 'بانک' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Page Header */
    .page-header {
        margin-bottom: 1.5rem;
    }
    .page-title {
        color: #333;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    .breadcrumb {
        background: transparent;
        padding: 0;
        margin: 0;
    }

    /* Custom File Upload */
    .custom-file-upload {
        border: 2px dashed #ddd;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
        position: relative;
    }
    .custom-file-upload:hover {
        border-color: #4a6cf7;
    }
    .custom-file-upload input[type="file"] {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }
    .upload-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .upload-icon i {
        font-size: 2.5rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    /* Bank Preview */
    .bank-preview {
        display: flex;
        justify-content: center;
        margin-bottom: 1.5rem;
    }
    .bank-preview-card {
        background-color: #fff;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        width: 100%;
        overflow: hidden;
    }
    .bank-preview-header {
        padding: 1.5rem;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-bottom: 1px solid #e9ecef;
    }
    .bank-preview-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 1rem;
        border-radius: 15px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        padding: 10px;
    }
    .bank-preview-logo img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    .bank-preview-logo i {
        font-size: 2rem;
        color: #6c757d;
    }
    .bank-preview-name {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0;
        color: #333;
    }
    .bank-preview-body {
        padding: 1.5rem;
    }
    .bank-preview-info {
        margin-bottom: 1rem;
    }
    .info-preview-item {
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 8px;
    }
    .info-preview-label {
        font-weight: 500;
        color: #6c757d;
    }
    .info-preview-value {
        font-weight: 600;
        color: #333;
    }

    /* Credit Card Preview */
    .credit-card {
        background: linear-gradient(135deg, #4a6cf7, #6e8efb);
        border-radius: 15px;
        padding: 1.5rem;
        color: white;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }
    .credit-card::before {
        content: '';
        position: absolute;
        top: -50px;
        right: -50px;
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        z-index: 1;
    }
    .credit-card::after {
        content: '';
        position: absolute;
        bottom: -80px;
        left: -80px;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.05);
        z-index: 1;
    }
    .credit-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }
    .bank-logo-small {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.2);
        padding: 5px;
    }
    .bank-logo-small img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    .bank-logo-small i {
        font-size: 1.5rem;
        color: white;
    }
    .chip {
        width: 40px;
        height: 30px;
        background: linear-gradient(135deg, #ffd700, #ffcc00);
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .chip i {
        color: #333;
        font-size: 0.8rem;
    }
    .credit-card-body {
        margin-bottom: 1.5rem;
        position: relative;
        z-index: 2;
    }
    .card-number {
        font-size: 1.25rem;
        letter-spacing: 2px;
        margin-bottom: 1rem;
        font-family: monospace;
    }
    .card-holder {
        display: flex;
        flex-direction: column;
    }
    .card-holder .label {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 0.25rem;
    }
    .card-holder .name {
        font-size: 1rem;
        text-transform: uppercase;
    }
    .credit-card-footer {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        position: relative;
        z-index: 2;
    }
    .expiry {
        display: flex;
        flex-direction: column;
    }
    .expiry .label {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 0.25rem;
    }
    .expiry .date {
        font-size: 1rem;
        font-family: monospace;
    }
    .bank-name-small {
        font-size: 1rem;
        font-weight: 600;
        text-align: right;
    }
</style>

@push('scripts')
<script>
    // Preview functionality
    $(document).ready(function() {
        // Update name preview
        $('#name').on('input', function() {
            const name = $(this).val() || 'نام بانک';
            $('#name-preview').text(name);
            $('#card-bank-name').text(name);
        });

        // Update prefix preview
        $('#prefix').on('input', function() {
            const prefix = $(this).val() || '------';
            $('#prefix-preview').text(prefix);
            $('#card-prefix-preview').text(prefix);
        });

        // Drag and drop functionality
        const dropArea = $('.custom-file-upload');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.on(eventName, preventDefaults);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.on(eventName, highlight);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.on(eventName, unhighlight);
        });

        function highlight() {
            dropArea.addClass('border-primary');
        }

        function unhighlight() {
            dropArea.removeClass('border-primary');
        }

        dropArea.on('drop', handleDrop);

        function handleDrop(e) {
            const dt = e.originalEvent.dataTransfer;
            const files = dt.files;

            if (files.length) {
                $('#icon').prop('files', files);
                previewImage($('#icon')[0]);
            }
        };
    });

    // Image preview function
    function previewImage(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                // Create image element
                const img = $('<img>');
                img.attr('src', e.target.result);
                img.attr('alt', 'Bank Logo');

                // Update all preview areas
                $('#logo-preview').html(img);
                $('#card-logo-preview').html(img.clone());
            }

            reader.readAsDataURL(input.files[0]);
        }
    }
</script>
@endpush
@endsection
