

@extends('admin.layouts.app')

@push('styles')
<style>
.user-profile {
    padding: 2rem 0;
}

.profile-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.profile-header .user-avatar {
    width: 120px;
    height: 120px;
    border-radius: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    margin-bottom: 1rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.stat-card .icon {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.stat-card .icon.purple {
    background: rgba(149, 76, 233, 0.1);
    color: #954ce9;
}

.stat-card .icon.blue {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.stat-card .icon.green {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.stat-card .icon i {
    font-size: 1.5rem;
}

.stat-card h3 {
    color: #1a202c; /* رنگ متن تیره برای خوانایی بهتر */
    font-weight: 600;
}

.stat-card p {
    color: #64748b;
}

.data-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
    overflow: hidden;
}

.data-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 1.5rem;
}

.data-card .card-body {
    padding: 1.5rem;
}

.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: #64748b;
    border-bottom-width: 1px;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
}

.badge.bg-success-soft {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.badge.bg-warning-soft {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.badge.bg-danger-soft {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.wallet-address {
    font-family: monospace;
    background: #f1f5f9;
    padding: 0.5rem;
    border-radius: 5px;
    font-size: 0.9rem;
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 20px;
    border: none;
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.05);
    padding: 1.5rem;
}
</style>
@endpush

@section('content')
<div class="container-fluid user-profile">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="row align-items-center">
            <div class="col-auto">
                <img src="{{ $user->avatar_url ?? 'https://ui-avatars.com/api/?name=' . urlencode($user->firstname . ' ' . $user->lastname) }}" 
                     class="user-avatar" alt="{{ $user->firstname }}">
            </div>
            <div class="col">
                <h1 class="h3 mb-2">{{ $user->firstname }} {{ $user->lastname }}</h1>
                <p class="mb-0">{{ $user->email }}</p>
            </div>
            <div class="col-auto">
                <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-light btn-action me-2">
                    <i class="fas fa-edit"></i> ویرایش
                </a>
                <a href="{{ route('admin.users.index') }}" class="btn btn-light btn-action">
                    <i class="fas fa-arrow-right"></i> بازگشت
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="stat-card">
                <div class="icon purple">
                    <i class="fas fa-wallet"></i>
                </div>
                <h3 class="h4 mb-2 text-dark">${{ number_format($totalBalanceUSD, 2) }}</h3>
                <p class="text-muted mb-0">کل دارایی</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stat-card">
                <div class="icon blue">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <h3 class="h4 mb-2 text-dark">{{ $user->transactions->count() }}</h3>
                <p class="text-muted mb-0">تعداد تراکنش‌ها</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stat-card">
                <div class="icon green">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <h3 class="h4 mb-2 text-dark">{{ $user->tickets->count() }}</h3>
                <p class="text-muted mb-0">تعداد تیکت‌ها</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Personal Information -->
        <div class="col-md-6 mb-4">
            <div class="data-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">اطلاعات شخصی</h5>
                    <span class="badge {{ $user->status == 'active' ? 'bg-success-soft' : 'bg-danger-soft' }}">
                        {{ $user->status == 'active' ? 'فعال' : 'غیرفعال' }}
                    </span>
                </div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <th width="200">موبایل</th>
                            <td>{{ $user->phone }}</td>
                        </tr>
                        <tr>
                            <th>کد ملی</th>
                            <td>{{ $user->national_id }}</td>
                        </tr>
                        <tr>
                            <th>جنسیت</th>
                            <td>
                                @if($user->gender == 'male')
                                    مرد
                                @elseif($user->gender == 'female')
                                    زن
                                @else
                                    نامشخص
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>نقش‌ها</th>
                            <td>
                                @foreach($user->roles as $role)
                                    <span class="badge bg-info-soft me-1">{{ $role->name }}</span>
                                @endforeach
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Bank Cards -->
        <div class="col-md-6 mb-4">
            <div class="data-card">
                <div class="card-header">
                    <h5 class="mb-0">کارت‌های بانکی</h5>
                </div>
                <div class="card-body">
                    @if($user->cards->count() > 0)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>بانک</th>
                                        <th>شماره کارت</th>
                                        <th>وضعیت</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->cards as $card)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ asset('images/banks/' . $card->bank?->logo) }}" 
                                                         width="30" class="me-2" alt="{{ $card->bank?->name }}">
                                                    {{ $card->bank?->name ?: 'نامشخص' }}
                                                </div>
                                            </td>
                                            <td class="wallet-address">{{ $card->number }}</td>
                                            <td>
                                                @switch($card->status)
                                                    @case('active')
                                                        <span class="badge bg-success-soft">فعال</span>
                                                        @break
                                                    @case('pending')
                                                        <span class="badge bg-warning-soft">در انتظار تایید</span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-danger-soft">غیرفعال</span>
                                                @endswitch
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info mb-0">هیچ کارت بانکی ثبت نشده است.</div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Transactions -->
        <div class="col-md-12 mb-4">
            <div class="data-card">
                <div class="card-header">
                    <h5 class="mb-0">آخرین تراکنش‌ها</h5>
                </div>
                <div class="card-body">
                    @if($user->transactions->count() > 0)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>تاریخ</th>
                                        <th>نوع</th>
                                        <th>مبلغ</th>
                                        <th>ارز</th>
                                        <th>وضعیت</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->transactions->take(10) as $transaction)
                                        <tr>
                                            <td>{{ jdate($transaction->created_at) }}</td>
                                            <td>{{ $transaction->type }}</td>
                                            <td>{{ number_format($transaction->amount) }}</td>
                                            <td>{{ $transaction->currency?->name ?: 'نامشخص' }}</td>
                                            <td>
                                                @switch($transaction->status)
                                                    @case('done')
                                                        <span class="badge bg-success-soft">انجام شده</span>
                                                        @break
                                                    @case('pending')
                                                        <span class="badge bg-warning-soft">در حال انجام</span>
                                                        @break
                                                    @case('failed')
                                                        <span class="badge bg-danger-soft">ناموفق</span>
                                                        @break
                                                @endswitch
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info mb-0">هیچ تراکنشی ثبت نشده است.</div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Documents -->
        <div class="col-md-12 mb-4">
            <div class="data-card">
                <div class="card-header">
                    <h5 class="mb-0">مدارک احراز هویت</h5>
                </div>
                <div class="card-body">
                    @if($user->documents->count() > 0)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>نوع مدرک</th>
                                        <th>تصویر</th>
                                        <th>تاریخ ارسال</th>
                                        <th>وضعیت</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->documents as $document)
                                        <tr>
                                            <td>
                                                @php
                                                    $documentTypeText = '';
                                                    $documentTypeIcon = '';
                                                    switch($document->name) {
                                                        case 'national_card':
                                                            $documentTypeText = 'کارت ملی';
                                                            $documentTypeIcon = 'fa-id-card';
                                                            break;
                                                        case 'birth_certificate':
                                                            $documentTypeText = 'شناسنامه';
                                                            $documentTypeIcon = 'fa-file-alt';
                                                            break;
                                                        case 'selfie':
                                                            $documentTypeText = 'عکس سلفی';
                                                            $documentTypeIcon = 'fa-camera';
                                                            break;
                                                    }
                                                @endphp
                                                <div class="d-flex align-items-center">
                                                    <i class="fas {{ $documentTypeIcon }} text-primary me-2"></i>
                                                    <span>{{ $documentTypeText }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="#" class="view-document" data-bs-toggle="modal" data-bs-target="#documentModal" data-document-url="{{ config('app.url') }}/storage/{{ $document->file->url }}">
                                                    <img src="{{ config('app.url') }}/storage/{{ $document->file->url }}" class="img-thumbnail" width="80" alt="مشاهده مدرک">
                                                </a>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <span>{{ jdate($document->created_at)->format('Y/m/d') }}</span>
                                                    <small class="text-muted">{{ jdate($document->created_at)->ago() }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                @php
                                                    $statusClass = '';
                                                    $statusText = '';
                                                    
                                                    switch($document->status) {
                                                        case 'pending':
                                                            $statusClass = 'bg-warning-subtle text-warning';
                                                            $statusText = 'در انتظار بررسی';
                                                            break;
                                                        case 'approved':
                                                            $statusClass = 'bg-success-subtle text-success';
                                                            $statusText = 'تایید شده';
                                                            break;
                                                        case 'rejected':
                                                            $statusClass = 'bg-danger-subtle text-danger';
                                                            $statusText = 'رد شده';
                                                            break;
                                                    }
                                                @endphp
                                                <div class="d-flex flex-column">
                                                    <span class="badge {{ $statusClass }} mb-1">{{ $statusText }}</span>
                                                    @if($document->status === 'rejected' && $document->description)
                                                        <small class="text-danger">
                                                            <i class="fas fa-info-circle me-1"></i>
                                                            دلیل رد: {{ $document->description }}
                                                        </small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-primary view-document" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#documentModal" 
                                                        data-document-url="{{ config('app.url') }}/storage/{{ $document->file->url }}" 
                                                        title="مشاهده مدرک">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info mb-0">هیچ مدرکی ثبت نشده است.</div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Modal for Document View -->
        <div class="modal fade" id="documentModal" tabindex="-1" aria-labelledby="documentModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="documentModalLabel">مشاهده مدرک</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img id="document-image" src="" class="img-fluid" alt="مدارک">
                    </div>
                </div>
            </div>
        </div>

        <!-- Wallet Addresses -->
        <div class="col-md-12 mb-4">
            <div class="data-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آدرس‌های کیف پول</h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createWalletModal">
                        <i class="fas fa-plus"></i> ایجاد کیف پول جدید
                    </button>
                </div>
                <div class="card-body">
                    @if($user->walletAddressHistories->count() > 0)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>آدرس</th>
                                        <th>نوع ارز</th>
                                        <th>شبکه</th>
                                        <th>مو</th>
                                        <th>تاریخ ایجاد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->walletAddressHistories as $wallet)
                                        <tr>
                                            <td>{{ $wallet->address }}</td>
                                            <td>{{ $wallet->coin?->name ?: $wallet->coin_type }}</td>
                                            <td>{{ $wallet->network?->name ?: '-' }}</td>
                                            <td>{{ $wallet->memo ?: '-' }}</td>
                                            <td>{{ jdate($wallet->created_at)->format('Y/m/d H:i') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info mb-0">هیچ آدرس کیف پولی ثبت نشده است.</div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Modal for creating new wallet -->
        <div class="modal fade" id="createWalletModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">ایجاد کیف پول جدید</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="{{ route('admin.users.create-wallet', $user->id) }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">نوع ارز</label>
                                <select name="coin_type" class="form-select" required>
                                    <option value="">انتخاب کنید</option>
                                    @foreach($coins as $coin)
                                        <option value="{{ $coin->coin_type }}">
                                            {{ $coin->name }} ({{ $coin->coin_type }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">شبکه</label>
                                <select name="network_id" class="form-select" required>
                                    <option value="">انتخاب کنید</option>
                                    @foreach($networks as $network)
                                        <option value="{{ $network->id }}">
                                            {{ $network->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                            <button type="submit" class="btn btn-primary">ایجاد کیف پول</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Tickets -->
        <div class="col-md-6 mb-4">
            <div class="data-card">
                <div class="card-header">
                    <h5 class="mb-0">آخرین تیکت‌ها</h5>
                </div>
                <div class="card-body">
                    @if($user->tickets->count() > 0)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>موضوع</th>
                                        <th>دپارتمان</th>
                                        <th>وضعیت</th>
                                        <th>آخرین بروزرسانی</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->tickets->take(5) as $ticket)
                                        <tr>
                                            <td>{{ $ticket->subject }}</td>
                                            <td>{{ $ticket->unit?->name ?: 'نامشخص' }}</td>
                                            <td>
                                                @switch($ticket->status)
                                                    @case('open')
                                                        <span class="badge bg-success-soft">باز</span>
                                                        @break
                                                    @case('closed')
                                                        <span class="badge bg-secondary-soft">بسته</span>
                                                        @break
                                                    @case('pending')
                                                        <span class="badge bg-warning-soft">در انتظار پاسخ</span>
                                                        @break
                                                @endswitch
                                            </td>
                                            <td>{{ jdate($ticket->updated_at)->format('Y/m/d H:i') }}</td>
                                            <td>
                                                <a href="{{ route('admin.tickets.show', $ticket->id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> مشاهده
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info mb-0">هیچ تیکتی ثبت نشده است.</div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Wallets and Balances -->
        <div class="col-md-12 mb-4">
            <div class="data-card">
                <div class="card-header">
                    <h5 class="mb-0">کیف پول‌ها و دارایی‌ها</h5>
                    <h6>کل دارایی: ${{ number_format($totalBalanceUSD, 2) }}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ارز</th>
                                    <th>موجودی</th>
                                    <th>معادل دلار</th>
                                    <th>آدرس</th>
                                    <th>وضعیت برداشت</th>
                                    <th>وضعیت واریز</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($coins as $coin)
                                    @php
                                        $wallet = $wallets->where('coin_id', $coin->id)->first();
                                    @endphp
                                    <tr>
                                        <td>
                                            @if($coin->coin_icon)
                                                <img src="{{ asset('storage/'.$coin->coin_icon) }}" width="25" height="25" alt="{{ $coin->coin_type }}">
                                            @endif
                                            {{ $coin->coin_type }}
                                        </td>
                                        <td>{{ $wallet ? number_format($wallet->balance, 8) : '0.00000000' }}</td>
                                        <td>${{ $wallet ? number_format(get_coin_usd_value($wallet->balance, $coin->coin_type), 2) : '0.00' }}</td>
                                        <td>{{ $wallet->address ?? '-' }}</td>
                                        <td>{!! $coin->is_withdrawal ? '<span class="badge bg-success-soft">فعال</span>' : '<span class="badge bg-danger-soft">غیرفعال</span>' !!}</td>
                                        <td>{!! $coin->is_deposit ? '<span class="badge bg-success-soft">فعال</span>' : '<span class="badge bg-danger-soft">غیرفعال</span>' !!}</td>
                                        <td>
                                            <div class="btn-group">
                                                @if($wallet)
                                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#adjustBalanceModal{{ $wallet->id }}">
                                                        <i class="fas fa-coins"></i> تنظیم موجودی
                                                    </button>
                                                @else
                                                    <form action="{{ route('admin.users.CreateWalletbyCoin', $user->id) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        <input type="hidden" name="coin_type" value="{{ $coin->id }}">
                                                        <button type="submit" class="btn btn-sm btn-success">
                                                            <i class="fas fa-plus"></i> ایجاد کیف پول
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Balance Adjustment Modals -->
        @foreach($wallets as $wallet)
            <div class="modal fade" id="adjustBalanceModal{{ $wallet->id }}" tabindex="-1" aria-labelledby="adjustBalanceModalLabel{{ $wallet->id }}" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="adjustBalanceModalLabel{{ $wallet->id }}">تنظیم موجودی {{ $wallet->coin_type }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form action="{{ route('admin.wallets.adjust-balance', $wallet->id) }}" method="POST" class="adjust-balance-form">
                            @csrf
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">نوع عملیات</label>
                                    <select name="operation" class="form-select" required>
                                        <option value="add">افزایش موجودی</option>
                                        <option value="subtract">کاهش موجودی</option>
                                        <option value="set">تنظیم مقدار دقیق</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">مقدار</label>
                                    <input type="number" step="0.00000001" name="amount" class="form-control" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">توضیحات</label>
                                    <textarea name="description" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                                <button type="submit" class="btn btn-primary">اعمال تغییرات</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.adjust-balance-form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const formData = new FormData(this);
            
            submitBtn.disabled = true;
            
            // Add loading animation
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> در حال پردازش...';
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const modal = this.closest('.modal');
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    modalInstance.hide();
                    
                    // Show success toast instead of alert
                    toastr.success(data.message);
                    
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    toastr.error(data.message || 'خطا در انجام عملیات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                toastr.error('خطا در ارتباط با سرور');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'اعمال تغییرات';
            });
        });
    });
});

    document.addEventListener('DOMContentLoaded', function() {
        // Document modal functionality
        const documentModal = document.getElementById('documentModal');
        documentModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const documentUrl = button.getAttribute('data-document-url');
            const documentImage = document.getElementById('document-image');
            documentImage.src = documentUrl;
        });
    });
</script>
@endpush
