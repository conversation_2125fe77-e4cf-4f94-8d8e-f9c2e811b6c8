@extends('admin.layouts.app')

@section('title', 'مدیریت کاربران')

@section('breadcrumb')
    <li class="breadcrumb-item active">مدیریت کاربران</li>
@endsection

@section('content')
<div class="container-fluid">
    <!-- عنوان صفحه -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="page-title h3">مدیریت کاربران</h1>
            <p class="text-muted">مدیریت و مشاهده لیست کاربران سیستم</p>
        </div>
    </div>

    <!-- کارت‌های آماری -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card primary">
                        <i class="fas fa-users stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ number_format($totalUsers) }}</div>
                            <div class="stat-label">کل کاربران</div>
                            <div class="stat-change">
                                <i class="fas fa-arrow-up"></i>
                                <span>8.2% افزایش از ماه قبل</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card success">
                        <i class="fas fa-user-check stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ number_format($activeUsers) }}</div>
                            <div class="stat-label">کاربران فعال</div>
                            <div class="stat-change">
                                <i class="fas fa-arrow-up"></i>
                                <span>12.1% افزایش از ماه قبل</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card info">
                        <i class="fas fa-user-plus stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ number_format($newUsers) }}</div>
                            <div class="stat-label">کاربران جدید</div>
                            <div class="stat-change">
                                <i class="fas fa-arrow-up"></i>
                                <span>5.3% افزایش از هفته قبل</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body p-0">
                    <div class="stat-card warning">
                        <i class="fas fa-signal stat-icon"></i>
                        <div class="stat-content">
                            <div class="stat-value">{{ number_format($onlineUsers) }}</div>
                            <div class="stat-label">کاربران آنلاین</div>
                            <div class="stat-change">
                                <i class="fas fa-arrow-down"></i>
                                <span>2.1% کاهش از ساعت قبل</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جستجوی پیشرفته -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">جستجوی پیشرفته</h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#searchCollapse">
                        <i class="fas fa-filter me-1"></i> نمایش / پنهان کردن
                    </button>
                </div>
                <div class="collapse show" id="searchCollapse">
                    <div class="card-body">
                        <form id="advancedSearch" class="row g-3">
                            <!-- ردیف اول -->
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="searchName" name="name" placeholder="نام و نام خانوادگی">
                                    <label for="searchName">نام و نام خانوادگی</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="email" class="form-control" id="searchEmail" name="email" placeholder="ایمیل">
                                    <label for="searchEmail">ایمیل</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="searchPhone" name="phone" placeholder="شماره موبایل">
                                    <label for="searchPhone">شماره موبایل</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="searchNationalId" name="national_id" placeholder="کد ملی">
                                    <label for="searchNationalId">کد ملی</label>
                                </div>
                            </div>

                            <!-- ردیف دوم -->
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <select class="form-select" id="searchRole" name="role">
                                        <option value="">همه</option>
                                        @foreach($roles as $role)
                                            <option value="{{ $role->id }}">{{ $role->name }}</option>
                                        @endforeach
                                    </select>
                                    <label for="searchRole">نقش</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <select class="form-select" id="searchStatus" name="status">
                                        <option value="">همه</option>
                                        <option value="approved">فعال</option>
                                        <option value="pending">در انتظار تایید</option>
                                        <option value="rejected">رد شده</option>
                                    </select>
                                    <label for="searchStatus">وضعیت</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <select class="form-select" id="searchVerification" name="verification">
                                        <option value="">همه</option>
                                        <option value="verified">احراز هویت شده</option>
                                        <option value="unverified">احراز هویت نشده</option>
                                        <option value="pending">در انتظار احراز هویت</option>
                                    </select>
                                    <label for="searchVerification">وضعیت احراز هویت</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <select class="form-select" id="searchOnlineStatus" name="online_status">
                                        <option value="">همه</option>
                                        <option value="online">آنلاین</option>
                                        <option value="offline">آفلاین</option>
                                    </select>
                                    <label for="searchOnlineStatus">وضعیت آنلاین</label>
                                </div>
                            </div>

                            <!-- ردیف سوم -->
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="searchDateFrom" name="date_from">
                                    <label for="searchDateFrom">تاریخ عضویت از</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="searchDateTo" name="date_to">
                                    <label for="searchDateTo">تاریخ عضویت تا</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="number" class="form-control" id="searchBalanceMin" name="balance_min" placeholder="حداقل موجودی">
                                    <label for="searchBalanceMin">حداقل موجودی (تومان)</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating">
                                    <input type="number" class="form-control" id="searchBalanceMax" name="balance_max" placeholder="حداکثر موجودی">
                                    <label for="searchBalanceMax">حداکثر موجودی (تومان)</label>
                                </div>
                            </div>

                            <!-- دکمه‌های جستجو -->
                            <div class="col-12 d-flex justify-content-end gap-2 mt-4">
                                <button type="reset" class="btn btn-outline-secondary">
                                    <i class="fas fa-redo me-1"></i> پاک کردن فیلترها
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> جستجو
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نمودارهای آماری -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">روند ثبت نام کاربران</h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary active" data-period="month">ماهانه</button>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-period="week">هفتگی</button>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-period="day">روزانه</button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="user-registration-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">وضعیت احراز هویت کاربران</h5>
                </div>
                <div class="card-body">
                    <div id="verification-status-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول کاربران -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">لیست کاربران</h5>
                    <div class="d-flex gap-2">
                        <div class="dropdown">
                            <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-1"></i> عملیات گروهی
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-download me-2 text-primary"></i> خروجی اکسل</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-envelope me-2 text-primary"></i> ارسال ایمیل گروهی</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-sms me-2 text-primary"></i> ارسال پیامک گروهی</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i> حذف انتخاب شده</a></li>
                            </ul>
                        </div>
                        <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> کاربر جدید
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="usersTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="40">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAll">
                                        </div>
                                    </th>
                                    <th width="60">#</th>
                                    <th>کاربر</th>
                                    <th>اطلاعات تماس</th>
                                    <th>احراز هویت</th>
                                    <th>موجودی</th>
                                    <th>فعالیت</th>
                                    <th>وضعیت</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($users as $user)
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input user-checkbox" type="checkbox" value="{{ $user->id }}">
                                        </div>
                                    </td>
                                    <td>{{ $user->id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="position-relative me-3">
                                                <img src="{{ $user->avatar ?? "https://ui-avatars.com/api/?name={$user->firstname}+{$user->lastname}&background=5a67d8&color=fff" }}"
                                                     class="rounded-circle" width="45" height="45" alt="{{ $user->firstname }} {{ $user->lastname }}">
                                                @php
                                                    $isOnline = isset($user->sessions) && $user->sessions->where('last_activity', '>=', now()->subMinutes(15)->timestamp)->count() > 0;
                                                @endphp
                                                @if($isOnline)
                                                    <span class="position-absolute bottom-0 end-0 translate-middle p-1 bg-success border border-light rounded-circle"
                                                          data-bs-toggle="tooltip" title="آنلاین"></span>
                                                @endif
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $user->firstname }} {{ $user->lastname }}</h6>
                                                <div class="d-flex align-items-center">
                                                    @foreach($user->roles as $role)
                                                        <span class="badge rounded-pill bg-info-subtle text-info me-1">{{ $role->name }}</span>
                                                    @endforeach
                                                </div>
                                                <small class="text-muted">{{ $user->national_id ?? 'بدون کد ملی' }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="user-contact-info">
                                            @if($user->email)
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="fas fa-envelope text-muted me-2"></i>
                                                    <span>{{ $user->email }}</span>
                                                </div>
                                            @endif
                                            @if($user->phone)
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-phone text-muted me-2"></i>
                                                    <span dir="ltr">{{ $user->phone }}</span>
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @php
                                            $hasIdDoc = false;
                                            $hasSelfieDoc = false;
                                            $hasConsentDoc = false;

                                            if(isset($user->documents)) {
                                                foreach($user->documents as $doc) {
                                                    if($doc->name == 'id' && $doc->status == 'approved') $hasIdDoc = true;
                                                    if($doc->name == 'selfie' && $doc->status == 'approved') $hasSelfieDoc = true;
                                                    if($doc->name == 'consent' && $doc->status == 'approved') $hasConsentDoc = true;
                                                }
                                            }

                                            $verificationStatus = 'unverified';
                                            $verificationBadgeClass = 'bg-danger-subtle text-danger';
                                            $verificationText = 'احراز نشده';

                                            if($hasIdDoc && $hasSelfieDoc && $hasConsentDoc) {
                                                $verificationStatus = 'verified';
                                                $verificationBadgeClass = 'bg-success-subtle text-success';
                                                $verificationText = 'احراز شده';
                                            } elseif($hasIdDoc || $hasSelfieDoc || $hasConsentDoc) {
                                                $verificationStatus = 'partial';
                                                $verificationBadgeClass = 'bg-warning-subtle text-warning';
                                                $verificationText = 'ناقص';
                                            }
                                        @endphp

                                        <div class="verification-status">
                                            <span class="badge {{ $verificationBadgeClass }} mb-2">{{ $verificationText }}</span>
                                            <div class="verification-progress">
                                                <div class="d-flex align-items-center justify-content-between mb-1">
                                                    <small>کارت ملی</small>
                                                    <i class="fas fa-{{ $hasIdDoc ? 'check text-success' : 'times text-danger' }}"></i>
                                                </div>
                                                <div class="d-flex align-items-center justify-content-between mb-1">
                                                    <small>سلفی</small>
                                                    <i class="fas fa-{{ $hasSelfieDoc ? 'check text-success' : 'times text-danger' }}"></i>
                                                </div>
                                                <div class="d-flex align-items-center justify-content-between">
                                                    <small>رضایت‌نامه</small>
                                                    <i class="fas fa-{{ $hasConsentDoc ? 'check text-success' : 'times text-danger' }}"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="user-balance">
                                            @php
                                                $irrBalance = $user->irrAmount ?? 0;
                                                $walletCount = 0;
                                                $hasWallets = false;

                                                if(isset($user->wallets)) {
                                                    if(is_object($user->wallets) && method_exists($user->wallets, 'count')) {
                                                        $walletCount = $user->wallets->count();
                                                    } elseif(is_array($user->wallets)) {
                                                        $walletCount = count($user->wallets);
                                                    }
                                                    $hasWallets = $walletCount > 0;
                                                }
                                            @endphp

                                            <div class="d-flex align-items-center mb-1">
                                                <i class="fas fa-wallet text-primary me-2"></i>
                                                <span>{{ number_format($irrBalance) }} تومان</span>
                                            </div>

                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-coins text-warning me-2"></i>
                                                <span>{{ $walletCount }} کیف پول</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="user-activity">
                                            @php
                                                $lastActivity = null;
                                                $transactionCount = 0;

                                                if(isset($user->sessions)) {
                                                    if(is_object($user->sessions) && method_exists($user->sessions, 'count') && $user->sessions->count() > 0) {
                                                        $lastSession = $user->sessions->sortByDesc('last_activity')->first();
                                                        if($lastSession) {
                                                            $lastActivity = Carbon\Carbon::createFromTimestamp($lastSession->last_activity);
                                                        }
                                                    } elseif(is_array($user->sessions) && count($user->sessions) > 0) {
                                                        $sessions = collect($user->sessions)->sortByDesc('last_activity');
                                                        $lastSession = $sessions->first();
                                                        if($lastSession) {
                                                            $lastActivity = Carbon\Carbon::createFromTimestamp($lastSession->last_activity);
                                                        }
                                                    }
                                                }

                                                if(isset($user->transactions)) {
                                                    if(is_object($user->transactions) && method_exists($user->transactions, 'count')) {
                                                        $transactionCount = $user->transactions->count();
                                                    } elseif(is_array($user->transactions)) {
                                                        $transactionCount = count($user->transactions);
                                                    }
                                                }
                                            @endphp

                                            <div class="d-flex align-items-center mb-1">
                                                <i class="fas fa-clock text-muted me-2"></i>
                                                <span>{{ $lastActivity ? $lastActivity->diffForHumans() : 'بدون فعالیت' }}</span>
                                            </div>

                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-exchange-alt text-muted me-2"></i>
                                                <span>{{ $transactionCount }} تراکنش</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column align-items-start">
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" role="switch" id="status_{{ $user->id }}"
                                                    {{ $user->status == 'approved' ? 'checked' : '' }}
                                                    data-user-id="{{ $user->id }}">
                                                <label class="form-check-label" for="status_{{ $user->id }}">
                                                    {{ $user->status == 'approved' ? 'فعال' : 'غیرفعال' }}
                                                </label>
                                            </div>
                                            <small class="text-muted">عضویت: {{ $user->created_at->format('Y/m/d') }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('admin.users.show', $user->id) }}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="مشاهده پروفایل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" title="ویرایش">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li>
                                                        <a class="dropdown-item" href="#">
                                                            <i class="fas fa-key text-info me-2"></i> تغییر رمز عبور
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#">
                                                            <i class="fas fa-wallet text-primary me-2"></i> مدیریت کیف پول
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#">
                                                            <i class="fas fa-exchange-alt text-success me-2"></i> تراکنش‌ها
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#">
                                                            <i class="fas fa-id-card text-warning me-2"></i> مدارک هویتی
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#">
                                                            <i class="fas fa-history text-secondary me-2"></i> تاریخچه فعالیت
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#">
                                                            <i class="fas fa-envelope text-info me-2"></i> ارسال ایمیل
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form action="{{ route('admin.users.destroy', $user->id) }}"
                                                              method="POST"
                                                              class="delete-form">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="dropdown-item text-danger">
                                                                <i class="fas fa-trash me-2"></i> حذف
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div class="text-muted">
                            نمایش {{ $users->firstItem() ?? 0 }} تا {{ $users->lastItem() ?? 0 }} از {{ $users->total() ?? 0 }} رکورد
                        </div>
                        <div class="pagination-container">
                            {{ $users->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(el => new bootstrap.Tooltip(el));

    // Handle select all checkbox
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('tbody .form-check-input');

    selectAll.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Handle delete confirmation
    document.querySelectorAll('.delete-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            Swal.fire({
                title: 'آیا مطمئن هستید؟',
                text: "این عملیات قابل بازگشت نیست!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'بله، حذف شود',
                cancelButtonText: 'انصراف'
            }).then((result) => {
                if (result.isConfirmed) {
                    this.submit();
                }
            });
        });
    });

    // Handle status toggle with loading state
    document.querySelectorAll('.form-check-input[role="switch"]').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const userId = this.dataset.userId;
            const isActive = this.checked;

            // Show loading state
            this.disabled = true;

            // Simulate AJAX request
            setTimeout(() => {
                fetch(`/api/users/${userId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({ is_active: isActive })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        toastr.success('وضعیت کاربر با موفقیت تغییر کرد');
                    } else {
                        this.checked = !this.checked;
                        toastr.error('خطا در تغییر وضعیت کاربر');
                    }
                })
                .catch(error => {
                    this.checked = !this.checked;
                    toastr.error('خطا در ارتباط با سرور');
                })
                .finally(() => {
                    this.disabled = false;
                });
            }, 500);
        });
    });

    // Advanced search form
    const searchForm = document.getElementById('advancedSearch');
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        // Add your search logic here
        toastr.info('جستجو در حال انجام...');
    });

    // User Registration Chart
    const userRegistrationChart = new ApexCharts(document.querySelector('#user-registration-chart'), {
        series: [{
            name: 'کاربران جدید',
            data: {!! json_encode($monthlyRegistrations['data']) !!}
        }],
        chart: {
            height: 300,
            type: 'area',
            fontFamily: 'Vazirmatn, sans-serif',
            toolbar: {
                show: false
            },
            zoom: {
                enabled: false
            }
        },
        colors: ['#5a67d8'],
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.2,
                stops: [0, 90, 100]
            }
        },
        xaxis: {
            categories: {!! json_encode($monthlyRegistrations['labels']) !!},
            labels: {
                style: {
                    colors: '#718096',
                    fontSize: '12px'
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: '#718096',
                    fontSize: '12px'
                },
                formatter: function (value) {
                    return value.toLocaleString('fa-IR');
                }
            }
        },
        grid: {
            borderColor: '#f1f1f1',
            strokeDashArray: 4,
            xaxis: {
                lines: {
                    show: true
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            }
        }
    });
    userRegistrationChart.render();

    // Verification Status Chart
    const verificationStatusChart = new ApexCharts(document.querySelector('#verification-status-chart'), {
        series: [
            {{ $verificationStats['notVerified'] }},
            {{ $verificationStats['fullyVerified'] }},
            {{ $verificationStats['pendingVerification'] }}
        ],
        chart: {
            type: 'donut',
            height: 300,
            fontFamily: 'Vazirmatn, sans-serif',
        },
        labels: ['احراز نشده', 'احراز شده', 'در انتظار تایید'],
        colors: ['#e53e3e', '#48bb78', '#ecc94b'],
        legend: {
            position: 'bottom',
            horizontalAlign: 'center',
            fontSize: '14px',
            markers: {
                width: 10,
                height: 10,
                radius: 2
            },
            itemMargin: {
                horizontal: 10,
                vertical: 5
            }
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '65%',
                    labels: {
                        show: true,
                        name: {
                            show: true,
                            fontSize: '16px',
                            fontWeight: 600,
                            offsetY: -10
                        },
                        value: {
                            show: true,
                            fontSize: '20px',
                            fontWeight: 400,
                            formatter: function(val) {
                                return val + '%';
                            }
                        },
                        total: {
                            show: true,
                            label: 'مجموع',
                            color: '#718096',
                            formatter: function(w) {
                                return w.globals.seriesTotals.reduce((a, b) => a + b, 0) + ' کاربر';
                            }
                        }
                    }
                }
            }
        },
        dataLabels: {
            enabled: false
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    height: 250
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    });
    verificationStatusChart.render();

    // Period buttons for registration chart
    document.querySelectorAll('[data-period]').forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            document.querySelectorAll('[data-period]').forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            const period = this.dataset.period;
            let newData, newCategories;

            if (period === 'month') {
                newData = {!! json_encode($monthlyRegistrations['data']) !!};
                newCategories = {!! json_encode($monthlyRegistrations['labels']) !!};
            } else if (period === 'week') {
                newData = {!! json_encode($weeklyRegistrations['data']) !!};
                newCategories = {!! json_encode($weeklyRegistrations['labels']) !!};
            } else if (period === 'day') {
                newData = {!! json_encode($dailyRegistrations['data']) !!};
                newCategories = {!! json_encode($dailyRegistrations['labels']) !!};
            }

            userRegistrationChart.updateOptions({
                xaxis: {
                    categories: newCategories
                }
            });

            userRegistrationChart.updateSeries([{
                name: 'کاربران جدید',
                data: newData
            }]);
        });
    });
});
</script>
@endpush

@push('styles')
<style>
/* استایل سفارشی برای صفحه کاربران */

/* استایل جدول */
.table > :not(caption) > * > * {
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(90, 103, 216, 0.05);
}

/* استایل برای بخش پیجینیشن */
.pagination-container .pagination {
    margin-bottom: 0;
}

.pagination-container .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination-container .page-link {
    color: var(--primary-color);
    border-radius: 5px;
    margin: 0 2px;
}

.pagination-container .page-link:hover {
    background-color: rgba(90, 103, 216, 0.1);
}

/* استایل برای فرم جستجو */
.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > .form-control:focus,
.form-floating > .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(90, 103, 216, 0.25);
}

/* استایل برای سویچ وضعیت */
.form-check-input:checked {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(72, 187, 120, 0.25);
}

/* استایل برای دکمه‌های عملیات */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: var(--btn-border-radius);
}

/* استایل برای نشانگرها */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
}

.bg-info-subtle {
    background-color: rgba(66, 153, 225, 0.15);
}

.bg-success-subtle {
    background-color: rgba(72, 187, 120, 0.15);
}

.bg-warning-subtle {
    background-color: rgba(236, 201, 75, 0.15);
}

.bg-danger-subtle {
    background-color: rgba(229, 62, 62, 0.15);
}

.text-info {
    color: #3182ce !important;
}

.text-success {
    color: #38a169 !important;
}

.text-warning {
    color: #d69e2e !important;
}

.text-danger {
    color: #c53030 !important;
}

/* استایل برای آواتار کاربران */
.position-relative .position-absolute {
    width: 10px;
    height: 10px;
}

/* استایل برای بخش احراز هویت */
.verification-status {
    font-size: 0.85rem;
}

.verification-progress {
    background-color: rgba(247, 250, 252, 0.8);
    border-radius: 8px;
    padding: 0.5rem;
}

/* استایل برای بخش موجودی */
.user-balance {
    font-size: 0.9rem;
}

/* استایل برای بخش فعالیت */
.user-activity {
    font-size: 0.9rem;
}

/* استایل برای بخش اطلاعات تماس */
.user-contact-info {
    font-size: 0.9rem;
}

/* استایل برای نمودارها */
#user-registration-chart, #verification-status-chart {
    direction: ltr;
}

/* استایل برای دکمه‌های نمودار */
.btn-group .btn-sm {
    padding: 0.25rem 0.75rem;
}

/* انیمیشن برای بخش‌های مختلف */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    animation: fadeIn 0.5s ease-in-out;
}

.card:nth-child(2) {
    animation-delay: 0.1s;
}

.card:nth-child(3) {
    animation-delay: 0.2s;
}

.card:nth-child(4) {
    animation-delay: 0.3s;
}
</style>
@endpush
@endsection
