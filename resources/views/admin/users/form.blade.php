@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">{{ isset($user) ? 'ویرایش کاربر' : 'کاربر جدید' }}</h1>
        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> بازگشت
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form action="{{ isset($user) ? route('admin.users.update', $user->id) : route('admin.users.store') }}" 
                  method="POST">
                @csrf
                @if(isset($user))
                    @method('PUT')
                @endif

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">نام</label>
                            <input type="text" name="firstname" class="form-control" 
                                   value="{{ old('firstname', $user->firstname ?? '') }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">نام خانوادگی</label>
                            <input type="text" name="lastname" class="form-control" 
                                   value="{{ old('lastname', $user->lastname ?? '') }}">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">ایمیل</label>
                            <input type="email" name="email" class="form-control" 
                                   value="{{ old('email', $user->email ?? '') }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">موبایل</label>
                            <input type="text" name="phone" class="form-control" 
                                   value="{{ old('phone', $user->phone ?? '') }}">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">نقش‌ها</label>
                    <select name="roles[]" class="form-select" multiple>
                        @foreach($roles as $role)
                            <option value="{{ $role->id }}" 
                                {{ isset($user) && $user->hasRole($role->name) ? 'selected' : '' }}>
                                {{ $role->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> ذخیره
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
