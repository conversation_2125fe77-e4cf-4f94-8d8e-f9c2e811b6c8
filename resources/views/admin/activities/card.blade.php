@extends('admin.layouts.app')

@section('title', 'فعالیت‌های کاربران')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-history text-primary"></i>
            فعالیت‌های کاربران
        </h1>
    </div>

    <!-- Search & Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ route('admin.activities.index') }}" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">جستجو</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="جستجو بر اساس آدرس یا IP..." value="{{ request('search') }}">
                </div>
                <div class="col-md-3">
                    <label for="method" class="form-label">متد</label>
                    <select class="form-select" id="method" name="method">
                        <option value="">همه</option>
                        <option value="GET" {{ request('method') == 'GET' ? 'selected' : '' }}>GET</option>
                        <option value="POST" {{ request('method') == 'POST' ? 'selected' : '' }}>POST</option>
                        <option value="PUT" {{ request('method') == 'PUT' ? 'selected' : '' }}>PUT</option>
                        <option value="DELETE" {{ request('method') == 'DELETE' ? 'selected' : '' }}>DELETE</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">از تاریخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">تا تاریخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                </div>
                <div class="col-md-12 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>جستجو
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Activities Grid View -->
    <div class="row">
        @forelse($activities as $activity)
        <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span class="method-badge {{ strtolower($activity->method) }}">{{ $activity->method }}</span>
                    <small class="text-muted">{{ jdate($activity->created_at)->format('Y/m/d H:i:s') }}</small>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="avatar avatar-sm me-2">
                                <div class="avatar-initial rounded-circle bg-primary">
                                    {{ substr($activity->user->firstname ?? 'U', 0, 1) }}
                                </div>
                            </div>
                            <div>
                                <span class="fw-bold">{{ $activity->user->firstname ?? '' }} {{ $activity->user->lastname ?? '' }}</span>
                                <small class="d-block text-muted">{{ $activity->user->email ?? '' }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted d-block mb-1">آدرس:</small>
                        <div class="address-box">{{ $activity->address }}</div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted d-block mb-1">IP:</small>
                        <div class="ip-box">{{ $activity->ip }}</div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <a href="{{ route('admin.activities.show', $activity->id) }}" class="btn btn-sm btn-primary w-100">
                        <i class="fas fa-eye me-1"></i> مشاهده جزئیات
                    </a>
                </div>
            </div>
        </div>
        @empty
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="empty-state">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5>هیچ فعالیتی یافت نشد</h5>
                        <p class="text-muted">فعالیت‌های کاربران در اینجا نمایش داده می‌شود.</p>
                    </div>
                </div>
            </div>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($activities->hasPages())
    <div class="d-flex justify-content-center mt-4">
        {{ $activities->withQueryString()->links('pagination::bootstrap-5') }}
    </div>
    @endif
</div>
@endsection

@push('styles')
<style>
    .method-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-weight: bold;
        font-size: 0.8rem;
        color: white;
    }
    .method-badge.get {
        background-color: #36b9cc;
    }
    .method-badge.post {
        background-color: #1cc88a;
    }
    .method-badge.put {
        background-color: #f6c23e;
    }
    .method-badge.delete {
        background-color: #e74a3b;
    }
    
    .address-box, .ip-box {
        background-color: #f8f9fc;
        padding: 0.5rem;
        border-radius: 0.5rem;
        font-family: monospace;
        font-size: 0.85rem;
        word-break: break-all;
    }
    
    .avatar {
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .avatar-initial {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }
    
    .empty-state {
        padding: 2rem;
    }
</style>
@endpush
