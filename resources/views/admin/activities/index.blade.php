@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">لاگ فعالیت‌ها</h1>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>کاربر</th>
                            <th>عملیات</th>
                            <th>آدرس</th>
                            <th>IP</th>
                            <th>تاریخ</th>
                            <th>عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($activities as $activity)
                            <tr>
                                <td>{{ $activity->user->firstname ?? '' }} {{ $activity->user->lastname ?? '' }}</td>
                                <td>{{ $activity->method }}</td>
                                <td>{{ $activity->address }}</td>
                                <td>{{ $activity->ip }}</td>
                                <td>{{ jdate($activity->created_at)->format('Y/m/d H:i:s') }}</td>
                                <td>
                                    <a href="{{ route('admin.activities.show', $activity->id) }}" 
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                {{ $activities->links() }}
            </div>
        </div>
    </div>
</div>
@endsection