@extends('admin.layouts.app')

@section('styles')
<style>
    .detail-card {
        border-radius: 1rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        border: none;
    }
    .parameter-box {
        background: #f8f9fc;
        border-radius: 0.5rem;
        font-family: monospace;
    }
    .info-label {
        color: #4e73df;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    .info-value {
        background: #f8f9fc;
        padding: 0.75rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .method-indicator {
        position: absolute;
        top: -15px;
        right: 20px;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        color: white;
        font-weight: bold;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .GET { background-color: #36b9cc; }
    .POST { background-color: #1cc88a; }
    .PUT { background-color: #f6c23e; }
    .DELETE { background-color: #e74a3b; }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-info-circle text-primary"></i>
            جزئیات فعالیت
        </h1>
        <a href="{{ route('admin.activities.index') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-right fa-sm"></i> بازگشت به لیست
        </a>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card detail-card position-relative">
                <span class="method-indicator {{ $activity->method }}">
                    {{ $activity->method }}
                </span>
                
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <div class="info-label">
                                    <i class="fas fa-user"></i> کاربر
                                </div>
                                <div class="info-value">
                                    {{ $activity->user->firstname ?? '' }} {{ $activity->user->lastname ?? '' }}
                                    <small class="d-block text-muted mt-1">
                                        {{ $activity->user->email ?? 'ایمیل ثبت نشده' }}
                                    </small>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="info-label">
                                    <i class="fas fa-link"></i> آدرس
                                </div>
                                <div class="info-value">
                                    {{ $activity->address }}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-4">
                                <div class="info-label">
                                    <i class="fas fa-globe"></i> IP
                                </div>
                                <div class="info-value">
                                    {{ $activity->ip }}
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="info-label">
                                    <i class="fas fa-calendar"></i> تاریخ و زمان
                                </div>
                                <div class="info-value">
                                    {{ jdate($activity->created_at)->format('Y/m/d H:i:s') }}
                                    <small class="d-block text-muted mt-1">
                                        {{ jdate($activity->created_at)->ago() }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($activity->description)
                    <div class="mb-4">
                        <div class="info-label">
                            <i class="fas fa-align-left"></i> توضیحات
                        </div>
                        <div class="info-value">
                            {{ $activity->description }}
                        </div>
                    </div>
                    @endif

                    @if($activity->parameters)
                    <div>
                        <div class="info-label">
                            <i class="fas fa-code"></i> پارامترها
                        </div>
                        <pre class="parameter-box p-3">{{ json_encode(json_decode($activity->parameters), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
