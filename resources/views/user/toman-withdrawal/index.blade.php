@extends('layouts.user')

@section('title', 'برداشت تومانی')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">درخواست برداشت تومانی</h5>
                </div>
                <div class="card-body">
                    <form id="toman-withdrawal-form" action="{{ route('user.accounting.toman-withdrawal.store') }}" method="POST">
                        @csrf
                        
                        <div class="form-group">
                            <label for="card_id">انتخاب کارت بانکی</label>
                            <select name="card_id" id="card_id" class="form-control @error('card_id') is-invalid @enderror" required>
                                <option value="">انتخاب کنید</option>
                                @foreach($cards as $card)
                                    <option value="{{ $card->id }}" {{ old('card_id') == $card->id ? 'selected' : '' }}>
                                        {{ $card->bank->name }} - {{ substr($card->number, 0, 4) }}...{{ substr($card->number, -4) }}
                                    </option>
                                @endforeach
                            </select>
                            @error('card_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            
                            @if(count($cards) == 0)
                                <div class="mt-2 text-danger">
                                    شما هیچ کارت بانکی تایید شده‌ای ندارید. 
                                    <a href="{{ route('user.card.create') }}">افزودن کارت بانکی</a>
                                </div>
                            @endif
                        </div>
                        
                        <div class="form-group">
                            <label for="amount">مبلغ (تومان)</label>
                            <div class="input-group">
                                <input type="number" name="amount" id="amount" class="form-control @error('amount') is-invalid @enderror" 
                                       value="{{ old('amount') }}" min="50000" step="1000" required>
                                <div class="input-group-append">
                                    <span class="input-group-text">تومان</span>
                                </div>
                            </div>
                            <small class="form-text text-muted">حداقل مبلغ برداشت: ۵۰,۰۰۰ تومان</small>
                            @error('amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="form-group">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>موجودی فعلی:</span>
                                <span class="font-weight-bold">{{ number_format(auth()->user()->toman_balance) }} تومان</span>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle mr-1"></i>
                            درخواست برداشت شما پس از تایید ادمین، به حساب بانکی انتخاب شده واریز خواهد شد.
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">ثبت درخواست برداشت</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">تاریخچه برداشت‌های تومانی</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>شناسه</th>
                                    <th>تاریخ</th>
                                    <th>مبلغ (تومان)</th>
                                    <th>کارت بانکی</th>
                                    <th>وضعیت</th>
                                    <th>توضیحات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($withdrawals as $withdrawal)
                                    <tr>
                                        <td>{{ $withdrawal->id }}</td>
                                        <td>{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i') }}</td>
                                        <td>{{ number_format($withdrawal->amount) }}</td>
                                        <td>
                                            {{ $withdrawal->card->bank->name }} - 
                                            {{ substr($withdrawal->card->number, 0, 4) }}...{{ substr($withdrawal->card->number, -4) }}
                                        </td>
                                        <td>
                                            @if($withdrawal->status == 'pending')
                                                <span class="badge badge-warning">در انتظار تایید</span>
                                            @elseif($withdrawal->status == 'approved')
                                                <span class="badge badge-success">تایید شده</span>
                                            @elseif($withdrawal->status == 'rejected')
                                                <span class="badge badge-danger">رد شده</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($withdrawal->status == 'rejected' && $withdrawal->reject_reason)
                                                <button type="button" class="btn btn-sm btn-link" data-toggle="tooltip" title="{{ $withdrawal->reject_reason }}">
                                                    <i class="fa fa-info-circle"></i> دلیل رد
                                                </button>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-5">
                                            <div class="empty-state">
                                                <img src="{{ asset('images/no-data.svg') }}" alt="بدون داده" style="width: 120px; height: 120px;">
                                                <p class="mt-3 text-muted">هیچ برداشت تومانی ثبت نشده است</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($withdrawals->hasPages())
                    <div class="card-footer">
                        {{ $withdrawals->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        // اعتبارسنجی فرم
        $('#toman-withdrawal-form').on('submit', function(e) {
            const amount = parseInt($('#amount').val());
            const balance = {{ auth()->user()->toman_balance }};
            
            if (amount > balance) {
                e.preventDefault();
                alert('موجودی کافی نیست');
                return false;
            }
            
            if (amount < 50000) {
                e.preventDefault();
                alert('حداقل مبلغ برداشت ۵۰,۰۰۰ تومان است');
                return false;
            }
            
            return true;
        });
        
        // فعال کردن تولتیپ‌ها
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
@endpush
