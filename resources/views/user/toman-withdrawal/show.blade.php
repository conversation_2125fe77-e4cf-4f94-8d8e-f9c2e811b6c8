@extends('layouts.user')

@section('title', 'جزئیات برداشت تومانی')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">جزئیات برداشت تومانی</h5>
                    <a href="{{ route('user.accounting.toman-withdrawal.index') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fa fa-arrow-right ml-1"></i> بازگشت به لیست
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted">شناسه برداشت:</label>
                                <div class="font-weight-bold">{{ $withdrawal->id }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted">تاریخ درخواست:</label>
                                <div class="font-weight-bold">{{ \Hekmatinasser\Verta\Verta::instance($withdrawal->created_at)->format('Y/m/d H:i') }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted">مبلغ:</label>
                                <div class="font-weight-bold">{{ number_format($withdrawal->amount) }} تومان</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted">کارمزد:</label>
                                <div class="font-weight-bold">{{ number_format($withdrawal->fee) }} تومان</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted">وضعیت:</label>
                                <div>
                                    @if($withdrawal->status == 'pending')
                                        <span class="badge badge-warning">در انتظار تایید</span>
                                    @elseif($withdrawal->status == 'approved')
                                        <span class="badge badge-success">تایید شده</span>
                                    @elseif($withdrawal->status == 'rejected')
                                        <span class="badge badge-danger">رد شده</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <label class="text-muted">تاریخ پردازش:</label>
                                <div class="font-weight-bold">
                                    @if($withdrawal->processed_at)
                                        {{ \Hekmatinasser\Verta\Verta::instance($withdrawal->processed_at)->format('Y/m/d H:i') }}
                                    @else
                                        -
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">اطلاعات کارت بانکی</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="text-muted">بانک:</label>
                                        <div class="font-weight-bold">{{ $withdrawal->card->bank->name }}</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item mb-3">
                                        <label class="text-muted">شماره کارت:</label>
                                        <div class="font-weight-bold">{{ $withdrawal->card->number }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($withdrawal->status == 'rejected' && $withdrawal->reject_reason)
                        <div class="alert alert-danger">
                            <h6 class="alert-heading">دلیل رد درخواست:</h6>
                            <p class="mb-0">{{ $withdrawal->reject_reason }}</p>
                        </div>
                    @endif

                    @if($withdrawal->status == 'pending')
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle mr-1"></i>
                            درخواست برداشت شما در حال بررسی است و پس از تایید، مبلغ به حساب بانکی شما واریز خواهد شد.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
