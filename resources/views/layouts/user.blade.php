<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#5a67d8">
    <title>پنل کاربری | @yield('title', 'Exchangim')</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="{{ asset('css/user.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    @stack('styles')
    <style>
        :root {
            --primary-color: #5a67d8;
            --secondary-color: #4c51bf;
            --dark-color: #1a202c;
            --light-color: #f7fafc;
            --success-color: #48bb78;
            --warning-color: #ecc94b;
            --danger-color: #e53e3e;
            --info-color: #4299e1;
            --sidebar-width: 280px;
            --header-height: 70px;
            --card-border-radius: 15px;
            --btn-border-radius: 10px;
            --transition-speed: 0.3s;
        }

        body {
            font-family: 'Vazirmatn', sans-serif;
            background-color: #f0f2f5;
            overflow-x: hidden;
        }

        /* Improved Wrapper */
        .wrapper {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* Enhanced Main Content */
        .main-content {
            flex: 1;
            margin-right: var(--sidebar-width);
            padding: 1.5rem;
            transition: margin var(--transition-speed) ease;
        }

        /* Responsive Main Content */
        @media (max-width: 768px) {
            .main-content {
                margin-right: 0;
            }
        }

        /* Card Styling */
        .card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 1.5rem;
            border-radius: var(--card-border-radius) var(--card-border-radius) 0 0 !important;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-footer {
            background-color: white;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 1.5rem;
            border-radius: 0 0 var(--card-border-radius) var(--card-border-radius) !important;
        }

        /* Button Styling */
        .btn {
            border-radius: var(--btn-border-radius);
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        /* Custom Navigation */
        .custom-nav {
            background-color: white;
            border-radius: var(--card-border-radius);
            padding: 0.75rem 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Sidebar Styling */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--dark-color);
            color: white;
            height: 100vh;
            position: fixed;
            top: 0;
            right: 0;
            padding: 1.5rem;
            overflow-y: auto;
            z-index: 1000;
            transition: transform var(--transition-speed) ease;
        }

        .sidebar-brand {
            margin-bottom: 2rem;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.7);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .nav-link.active {
            color: white;
            background-color: var(--primary-color);
        }

        .nav-link i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }

        /* Responsive Sidebar */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }
        }

        /* Custom Scrollbar */
        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--dark-color);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        /* Avatar */
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Utilities */
        .fs-12 {
            font-size: 12px;
        }

        .fs-14 {
            font-size: 14px;
        }

        .fs-16 {
            font-size: 16px;
        }

        .fw-medium {
            font-weight: 500;
        }

        .text-truncate-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-brand">
                <div class="d-flex align-items-center">
                    <div class="bg-primary d-flex align-items-center justify-content-center rounded-circle" style="width: 40px; height: 40px;">
                        <i class="fas fa-exchange-alt text-white"></i>
                    </div>
                    <h1 class="h5 ms-3 mb-0 text-white">Exchangim</h1>
                </div>
            </div>

            <div class="nav flex-column">
                <!-- داشبورد -->
                <div class="nav-item">
                    <a href="{{ route('user.index') }}" class="nav-link {{ request()->routeIs('user.index') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>داشبورد</span>
                    </a>
                </div>

                <!-- کیف پول -->
                <div class="nav-item">
                    <a href="{{ route('user.wallet.index') }}" class="nav-link {{ request()->routeIs('user.wallet.*') ? 'active' : '' }}">
                        <i class="fas fa-wallet"></i>
                        <span>کیف پول‌ها</span>
                    </a>
                </div>

                <!-- تراکنش‌ها -->
                <div class="nav-item">
                    <a href="{{ route('user.transaction.index') }}" class="nav-link {{ request()->routeIs('user.transaction.*') ? 'active' : '' }}">
                        <i class="fas fa-exchange-alt"></i>
                        <span>تراکنش‌ها</span>
                    </a>
                </div>

                <!-- کارت‌های بانکی -->
                <div class="nav-item">
                    <a href="{{ route('user.card.index') }}" class="nav-link {{ request()->routeIs('user.card.*') ? 'active' : '' }}">
                        <i class="fas fa-credit-card"></i>
                        <span>کارت‌های بانکی</span>
                    </a>
                </div>

                <!-- برداشت -->
                <div class="nav-item">
                    <a href="{{ route('user.accounting.withdraw.index') }}" class="nav-link {{ request()->routeIs('user.accounting.withdraw.*') ? 'active' : '' }}">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>برداشت ارزی</span>
                    </a>
                </div>

                <!-- برداشت تومانی -->
                <div class="nav-item">
                    <a href="{{ route('user.accounting.toman-withdrawal.index') }}" class="nav-link {{ request()->routeIs('user.accounting.toman-withdrawal.*') ? 'active' : '' }}">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>برداشت تومانی</span>
                    </a>
                </div>

                <!-- اسناد -->
                <div class="nav-item">
                    <a href="{{ route('user.document.index') }}" class="nav-link {{ request()->routeIs('user.document.*') ? 'active' : '' }}">
                        <i class="fas fa-file-alt"></i>
                        <span>اسناد هویتی</span>
                    </a>
                </div>

                <!-- پشتیبانی -->
                <div class="nav-item">
                    <a href="{{ route('user.support.index') }}" class="nav-link {{ request()->routeIs('user.support.*') ? 'active' : '' }}">
                        <i class="fas fa-headset"></i>
                        <span>پشتیبانی</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <nav class="custom-nav">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <!-- دکمه تاگل سایدبار -->
                        <button class="btn sidebar-toggle d-lg-none me-3">
                            <i class="fas fa-bars"></i>
                        </button>

                        <!-- بردکرامب -->
                        <nav aria-label="breadcrumb" class="d-none d-md-block">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="{{ route('user.index') }}" class="text-decoration-none">داشبورد</a></li>
                                @yield('breadcrumb')
                            </ol>
                        </nav>
                    </div>

                    <div class="d-flex align-items-center">
                        <!-- اعلان‌ها -->
                        <div class="dropdown me-3">
                            <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-0 translate-middle badge rounded-pill bg-danger">
                                    3
                                </span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end">
                                <h6 class="dropdown-header">اعلان‌ها</h6>
                                <a class="dropdown-item" href="#">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-money-bill-wave text-success me-2"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-2">
                                            <p class="mb-0 fs-14">واریز موفق</p>
                                            <p class="text-muted mb-0 fs-12">۱۰ دقیقه پیش</p>
                                        </div>
                                    </div>
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-center" href="#">مشاهده همه</a>
                            </div>
                        </div>

                        <!-- پروفایل کاربر -->
                        <div class="dropdown">
                            <a class="nav-link d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                                <div class="avatar me-2">
                                    <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name ?? 'User') }}&background=5a67d8&color=fff"
                                         class="rounded-circle" width="40" height="40" alt="پروفایل">
                                </div>
                                <div class="d-none d-md-block">
                                    <span class="fw-medium">{{ auth()->user()->name ?? 'کاربر' }}</span>
                                    <i class="fas fa-chevron-down ms-1 fs-12"></i>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-user me-2 text-primary"></i>
                                        پروفایل
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-cog me-2 text-primary"></i>
                                        تنظیمات
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form action="{{ route('logout') }}" method="POST">
                                        @csrf
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-sign-out-alt me-2"></i>
                                            خروج
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Flash Messages -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <!-- Main Content -->
            @yield('content')
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        // Sidebar Toggle
        document.querySelector('.sidebar-toggle')?.addEventListener('click', () => {
            document.querySelector('.sidebar').classList.toggle('show');
        });

        // Toastr Configuration
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: "toast-top-right",
            timeOut: 5000
        };

        @if(session('toast-success'))
            toastr.success("{{ session('toast-success') }}");
        @endif

        @if(session('toast-error'))
            toastr.error("{{ session('toast-error') }}");
        @endif
    </script>
    @stack('scripts')
</body>
</html>
