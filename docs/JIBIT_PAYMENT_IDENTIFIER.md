# Jibit Payment Identifier Integration

این مستند نحوه پیاده‌سازی و استفاده از سیستم Jibit Payment Identifier را بر اساس API واقعی جیبیت شرح می‌دهد.

## درباره Jibit Payment Identifier

Jibit Payment Identifier (PIP) سیستمی است که به کاربران اجازه می‌دهد با استفاده از شناسه‌های پرداخت منحصر به فرد، پرداخت‌های بانک به بانک انجام دهند. این سیستم بر اساس API رسمی جیبیت پیاده‌سازی شده است.

## نصب و پیکربندی

### 1. متغیرهای محیطی

فایل `.env` خود را با متغیرهای زیر به‌روزرسانی کنید:

```env
# Jibit Payment Identifier Configuration
JIBIT_PIP_BASE_URL=https://napi.jibit.ir/pip
JIBIT_PIP_API_KEY=your_api_key_here
JIBIT_PIP_SECRET_KEY=your_secret_key_here
JIBIT_PIP_CALLBACK_URL=https://yourdomain.com/api/jibit/callback
```

### 2. اجرای Migration

```bash
php artisan migrate
```

### 3. تنظیم Cron Job

برای بررسی خودکار پرداخت‌های معلق، Cron Job زیر را اضافه کنید:

```bash
# هر 5 دقیقه یکبار
*/5 * * * * cd /path/to/your/project && php artisan jibit:check-pending-payments
```

## نحوه کارکرد سیستم

1. **ایجاد Payment ID**: کاربر درخواست شارژ کیف پول می‌دهد
2. **ثبت در جیبیت**: شناسه پرداخت در سیستم جیبیت ثبت می‌شود
3. **پرداخت توسط کاربر**: کاربر از طریق اپلیکیشن جیبیت پرداخت انجام می‌دهد
4. **تایید پرداخت**: سیستم پرداخت را تایید و موجودی کاربر را شارژ می‌کند

## API Endpoints

### ایجاد Payment ID جدید

**POST** `/api/user/jibit-payment/create`

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "description": "شارژ کیف پول تومانی"
}
```

**پیش‌نیازها:**
- کاربر باید نام کامل داشته باشد
- کاربر باید شماره موبایل معتبر داشته باشد
- کاربر باید حداقل یک کارت بانکی تایید شده با IBAN داشته باشد

**Response:**
```json
{
    "success": true,
    "data": {
        "payment_id": 123,
        "payment_identifier": "payId_from_jibit",
        "merchant_reference_number": "MRN_1704067200_ABC12345",
        "user_token": "user_token_from_jibit",
        "registry_status": "WAITING_FOR_USER",
        "currency": "T",
        "user_ibans": [
            "**************************",
            "**************************"
        ]
    },
    "message": "شناسه پرداخت با موفقیت ایجاد شد. کاربر می‌تواند با هر مبلغی پرداخت کند."
}
```

**نکات مهم:**
- مبلغ پرداخت توسط کاربر در اپلیکیشن جیبیت تعیین می‌شود
- پس از ایجاد Payment ID، کاربر باید از طریق اپلیکیشن جیبیت با استفاده از `user_token` پرداخت را انجام دهد
- سیستم تمام IBAN های تایید شده کاربر را به جیبیت ارسال می‌کند

### بررسی وضعیت Payment ID

**GET** `/api/user/jibit-payment/{merchantReferenceNumber}/payment-id-status`

**Response:**
```json
{
    "success": true,
    "data": {
        "payId": "jibit_pay_id",
        "merchantReferenceNumber": "MRN_1704067200_ABC12345",
        "registryStatus": "VERIFIED",
        "userFullName": "نام کاربر",
        "userMobile": "***********",
        "userIban": "**************************",
        "destinationIban": "**************************",
        "destinationOwnerName": "نام مقصد",
        "destinationBank": "MELI"
    }
}
```

### بررسی پرداخت‌های انجام شده

**GET** `/api/user/jibit-payment/{merchantReferenceNumber}/check-payments`

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "externalReferenceNumber": "ext_ref_123",
            "merchantReferenceNumber": "MRN_1704067200_ABC12345",
            "amount": 50000,
            "status": "SUCCESSFUL",
            "bankReferenceNumber": "bank_ref_456",
            "bank": "MELI",
            "paymentId": "payment_id_789"
        }
    ]
}
```

### بررسی وضعیت پرداخت محلی

**GET** `/api/user/jibit-payment/{paymentIdentifier}/status`

**Response:**
```json
{
    "success": true,
    "data": {
        "payment_id": 123,
        "payment_identifier": "MRN_1704067200_ABC12345",
        "amount": 50000,
        "currency": "T",
        "status": "paid",
        "description": "شارژ کیف پول تومانی",
        "created_at": "2024-01-01T12:00:00Z",
        "paid_at": "2024-01-01T12:05:00Z",
        "reference_number": "*********",
        "trace_number": "*********"
    }
}
```

### تاریخچه پرداخت‌ها

**GET** `/api/user/jibit-payment/history?per_page=15`

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 123,
            "payment_identifier": "PIP_1704067200_ABC12345",
            "amount": 50000,
            "status": "paid",
            "created_at": "2024-01-01T12:00:00Z"
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 5,
        "per_page": 15,
        "total": 75
    }
}
```

### آمار پرداخت‌ها

**GET** `/api/user/jibit-payment/stats`

**Response:**
```json
{
    "success": true,
    "data": {
        "total_payments": 10,
        "successful_payments": 8,
        "pending_payments": 1,
        "failed_payments": 1,
        "total_amount_paid": 500000
    }
}
```

### لغو پرداخت

**POST** `/api/user/jibit-payment/{paymentIdentifier}/cancel`

**Response:**
```json
{
    "success": true,
    "message": "پرداخت با موفقیت لغو شد"
}
```

## Callback و Webhook

### Callback URL

**POST** `/api/jibit/callback`

این endpoint برای دریافت نتیجه پرداخت از جیبیت استفاده می‌شود.

### Webhook URL

**POST** `/api/jibit/webhook`

این endpoint برای دریافت اعلان‌های real-time از جیبیت استفاده می‌شود.

## وضعیت‌های Payment ID

### Registry Status
- `WAITING_FOR_USER`: در انتظار تکمیل اطلاعات توسط کاربر
- `WAITING_FOR_VERIFICATION`: در انتظار تایید
- `VERIFIED`: تایید شده و آماده پرداخت
- `REJECTED`: رد شده
- `REVIEWING`: در حال بررسی

### Payment Status
- `IN_PROGRESS`: در حال پردازش
- `WAITING_FOR_MERCHANT_VERIFY`: در انتظار تایید فروشنده
- `SUCCESSFUL`: موفق
- `FAILED`: ناموفق

### وضعیت‌های محلی
- `pending`: در انتظار پرداخت
- `paid`: پرداخت شده
- `failed`: ناموفق
- `expired`: منقضی شده
- `cancelled`: لغو شده

## محدودیت‌ها

- حداقل مبلغ: 10,000 تومان
- حداکثر مبلغ: 50,000,000 تومان
- مدت زمان انقضا: 24 ساعت
- کاربر باید IBAN معتبر داشته باشد
- کاربر باید در اپلیکیشن جیبیت ثبت‌نام کرده باشد

## نکات مهم

1. **IBAN کاربر**: کاربران باید IBAN معتبر در پروفایل خود داشته باشند
2. **اپلیکیشن جیبیت**: کاربران باید اپلیکیشن جیبیت را نصب کرده باشند
3. **User Token**: پس از ایجاد Payment ID، کاربر باید با User Token در اپلیکیشن جیبیت پرداخت کند
4. **بررسی خودکار**: سیستم به صورت خودکار پرداخت‌ها را بررسی و موجودی را شارژ می‌کند

## نکات امنیتی

1. همیشه از HTTPS استفاده کنید
2. API Key و Secret Key را محرمانه نگه دارید
3. Access Token ها را cache کنید تا از تولید مکرر جلوگیری شود
4. IP whitelist را در پنل جیبیت فعال کنید
5. Callback URL ها را امن کنید

## عیب‌یابی

### لاگ‌ها

تمام فعالیت‌های مربوط به Jibit در فایل‌های لاگ Laravel ثبت می‌شوند:

```bash
tail -f storage/logs/laravel.log | grep Jibit
```

### Command های مفید

```bash
# بررسی پرداخت‌های معلق
php artisan jibit:check-pending-payments

# بررسی پرداخت‌های قدیمی‌تر از 60 دقیقه
php artisan jibit:check-pending-payments --older-than=60

# محدود کردن تعداد پرداخت‌های بررسی شده
php artisan jibit:check-pending-payments --limit=100
```

## پشتیبانی

در صورت بروز مشکل، لطفاً موارد زیر را بررسی کنید:

1. صحت API Key و Secret Key
2. دسترسی به URL های Jibit
3. صحت Callback URL
4. وضعیت شبکه و فایروال

برای اطلاعات بیشتر به مستندات رسمی Jibit مراجعه کنید:
- https://dashboard.jibit.ir/assets/documents/Jibit_Payment_Identifier_Documentation.pdf
- https://napi.jibit.ir/pip/swagger-ui.html
