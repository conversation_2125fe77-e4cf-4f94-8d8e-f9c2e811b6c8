<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;
use App\Models\Transaction;
use App\Models\TomanWithdrawal;

class TomanTransactionRule implements ValidationRule
{
    protected $amount;
    protected $type;

    public function __construct($amount, $type)
    {
        $this->amount = $amount;
        $this->type = $type;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = Auth::user();
        $tomanCurrencyId = 6; // Assuming 6 is the ID for Toman/IRR

        if ($this->type == 'deposit') {
            // Get today's deposits
            $todayDeposits = $user->transactions()
                ->where('type', 'deposit')
                ->where('currency_id', $tomanCurrencyId)
                ->where('status', 'done')
                ->whereDate('created_at', date('Y-m-d'))
                ->sum('amount');

            // Check level-based limits for deposits
            if ($user->level == 1) {
                $maxDailyDeposit = intval(env('L1_MAX_DAILY_DEPOSIT', 5000000)); // Default 5,000,000 Toman
                if (intval($todayDeposits) + intval($this->amount) > $maxDailyDeposit) {
                    $fail('واریز در سطح ۱ تا سقف '.number_format($maxDailyDeposit).' تومان روزانه.');
                }
            } elseif ($user->level == 2) {
                $maxDailyDeposit = intval(env('L2_MAX_DAILY_DEPOSIT', 20000000)); // Default 20,000,000 Toman
                if (intval($todayDeposits) + intval($this->amount) > $maxDailyDeposit) {
                    $fail('واریز در سطح ۲ تا سقف '.number_format($maxDailyDeposit).' تومان روزانه.');
                }
            } elseif ($user->level == 3) {
                $maxDailyDeposit = intval(env('L3_MAX_DAILY_DEPOSIT', 50000000)); // Default 50,000,000 Toman
                if (intval($todayDeposits) + intval($this->amount) > $maxDailyDeposit) {
                    $fail('واریز در سطح ۳ تا سقف '.number_format($maxDailyDeposit).' تومان روزانه.');
                }
            }
            // Higher levels may have no limits or different limits
        } elseif ($this->type == 'withdraw') {
            // Get today's withdrawals
            $todayWithdrawals = TomanWithdrawal::where('user_id', $user->id)
                ->where('status', '!=', 'rejected')
                ->whereDate('created_at', date('Y-m-d'))
                ->sum('amount');

            // Check level-based limits for withdrawals
            if ($user->level == 1) {
                $maxDailyWithdrawal = intval(env('L1_MAX_DAILY_WITHDRAW', 2000000)); // Default 2,000,000 Toman
                if (intval($todayWithdrawals) + intval($this->amount) > $maxDailyWithdrawal) {
                    $fail('برداشت در سطح ۱ تا سقف '.number_format($maxDailyWithdrawal).' تومان روزانه.');
                }
            } elseif ($user->level == 2) {
                $maxDailyWithdrawal = intval(env('L2_MAX_DAILY_WITHDRAW', 10000000)); // Default 10,000,000 Toman
                if (intval($todayWithdrawals) + intval($this->amount) > $maxDailyWithdrawal) {
                    $fail('برداشت در سطح ۲ تا سقف '.number_format($maxDailyWithdrawal).' تومان روزانه.');
                }
            } elseif ($user->level == 3) {
                $maxDailyWithdrawal = intval(env('L3_MAX_DAILY_WITHDRAW', 30000000)); // Default 30,000,000 Toman
                if (intval($todayWithdrawals) + intval($this->amount) > $maxDailyWithdrawal) {
                    $fail('برداشت در سطح ۳ تا سقف '.number_format($maxDailyWithdrawal).' تومان روزانه.');
                }
            }
            // Higher levels may have no limits or different limits

            // Check if user has enough balance
            if ($user->toman_balance < $this->amount) {
                $fail('موجودی کافی نیست');
            }
        }
    }
}
