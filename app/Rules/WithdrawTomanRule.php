<?php

namespace App\Rules;

use App\Models\Card;
use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;

class WithdrawTomanRule implements ValidationRule
{
    protected $amount;
    protected $card_id;

    public function __construct($amount, $card_id)
    {
        $this->amount = $amount;
        $this->card_id = $card_id;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if user has enough toman balance
        $user = User::find(Auth::id());
        if (!$user || $user->toman_balance < $this->amount) {
            $fail('موجودی تومانی کافی نیست');
            return;
        }

        // Check if card belongs to user and is approved
        $card = Card::where('id', $this->card_id)
            ->where('user_id', Auth::id())
            ->first();
        
        if (!$card) {
            $fail('کارت بانکی نامعتبر است');
            return;
        }
        
        if ($card->status !== 'approved') {
            $fail('کارت بانکی شما هنوز تایید نشده است');
            return;
        }
    }
}
