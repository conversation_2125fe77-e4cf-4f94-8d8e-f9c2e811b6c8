<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;

class TradeRule implements ValidationRule
{
    protected $amount;

    protected $currency_id;

    public function __construct($amount, $currency_id)
    {
        $this->amount = $amount;
        $this->currency_id = $currency_id;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = Auth::user()->id;

    }
}
