<?php

namespace App\Rules\User;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;

class TransactionRule implements ValidationRule
{
    protected $amount;

    protected $type;

    protected $currency_id;

    public function __construct($amount, $type, $currency_id)
    {
        $this->amount = $amount;
        $this->type = $type;
        $this->currency_id = $currency_id;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = Auth::user();

        switch ($this->type) {
            case 'buy':
                $buyTransactions = $user->transactions()->where('type', 'buy')->where('currency_id', $this->currency_id)->where('status', 'done');

                if ($user->level == 1) {
                    $fail('برای خرید ارزء سطح کاربری خود را ارتقاء دهید.');
                }

                // We're removing the 48-hour rule to allow multiple purchases within the same day
                // as long as the user hasn't reached their daily limit

                // Check daily buy limits for level 2
                if ($user->level == 2) {
                    $todayBuyAmount = intval($buyTransactions->whereDate('created_at', date('Y-m-d'))->sum('price'));
                    $maxDailyBuy = intval(env('L2_MAX_DAILY_BUY', 0));

                    if ($todayBuyAmount + intval($this->amount) > $maxDailyBuy) {
                        $remainingToday = max(0, $maxDailyBuy - $todayBuyAmount);
                        $fail("شما به سقف خرید روزانه رسیده‌اید. محدودیت خرید در سطح ۲ تا سقف " . number_format($maxDailyBuy) . " تومان روزانه است. مقدار باقیمانده امروز: " . number_format($remainingToday) . " تومان");
                    }
                }

                // Check daily buy limits for level 3
                if ($user->level == 3) {
                    $todayBuyAmount = intval($buyTransactions->whereDate('created_at', date('Y-m-d'))->sum('price'));
                    $maxDailyBuy = intval(env('L3_MAX_DAILY_BUY', 0));

                    if ($todayBuyAmount + intval($this->amount) > $maxDailyBuy) {
                        $remainingToday = max(0, $maxDailyBuy - $todayBuyAmount);
                        $fail("شما به سقف خرید روزانه رسیده‌اید. محدودیت خرید در سطح ۳ تا سقف " . number_format($maxDailyBuy) . " تومان روزانه است. مقدار باقیمانده امروز: " . number_format($remainingToday) . " تومان");
                    }
                }

                if (intval($this->amount) > $user->toman_balance) {
                    $fail("موجودی تومانی کافی نیست. موجودی فعلی: " . number_format($user->toman_balance) . " تومان");
                }
                break;

            case 'sell':
                $sellTransactions = $user->transactions()->where('type', 'sell')->where('currency_id', $this->currency_id)->where('status', 'done');

                if ($user->level == 1) {
                    $fail('شما شرایط فروش ندارید.');
                }

                // Check daily sell limits for level 2
                if ($user->level == 2) {
                    $todaySellAmount = intval($sellTransactions->whereDate('created_at', date('Y-m-d'))->sum('price'));
                    $maxDailySell = intval(env('L2_MAX_DAILY_SELL', 0));

                    if ($todaySellAmount + intval($this->amount) > $maxDailySell) {
                        $remainingToday = max(0, $maxDailySell - $todaySellAmount);
                        $fail("شما به سقف فروش روزانه رسیده‌اید. محدودیت فروش در سطح ۲ تا سقف " . number_format($maxDailySell) . " تومان روزانه است. مقدار باقیمانده امروز: " . number_format($remainingToday) . " تومان");
                    }
                }

                // Check daily sell limits for level 3
                if ($user->level == 3) {
                    $todaySellAmount = intval($sellTransactions->whereDate('created_at', date('Y-m-d'))->sum('price'));
                    $maxDailySell = intval(env('L3_MAX_DAILY_SELL', 0));

                    if ($todaySellAmount + intval($this->amount) > $maxDailySell) {
                        $remainingToday = max(0, $maxDailySell - $todaySellAmount);
                        $fail("شما به سقف فروش روزانه رسیده‌اید. محدودیت فروش در سطح ۳ تا سقف " . number_format($maxDailySell) . " تومان روزانه است. مقدار باقیمانده امروز: " . number_format($remainingToday) . " تومان");
                    }
                }

                // Check if user has enough cryptocurrency balance
                $wallet = $user->wallets()->where('coin_id', $this->currency_id)->first();
                if (!$wallet || $wallet->balance < $this->amount) {
                    $currentBalance = $wallet ? $wallet->balance : 0;
                    $fail("موجودی ارز شما کافی نیست. موجودی فعلی: " . number_format($currentBalance, 8));
                }
                break;
        }
    }
}
