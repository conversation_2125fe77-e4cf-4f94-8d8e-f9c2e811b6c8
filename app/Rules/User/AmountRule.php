<?php

namespace App\Rules\User;

use App\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class AmountRule implements ValidationRule
{
    protected $amount;

    protected $user_id;

    protected $type;

    public function __construct($amount, $user_id, $type)
    {
        $this->amount = $amount;
        $this->user_id = $user_id;
        $this->type = $type;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = User::find($this->user_id);
        if ($this->type == 'decrease') {
            if (is_int($this->amount)) {
                if ($user->getIrrAmount() < $this->amount) {
                    $fail('مقدار کاهش بیشتر از میزان موجودی کاربر می باشد');
                }
            } else {
                $fail('مقدار amount باید از نوع (integer) باشد.');
            }
        }
    }
}
