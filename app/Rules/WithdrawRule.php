<?php

namespace App\Rules;

use App\Models\Card;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;

class WithdrawRule implements ValidationRule
{
    protected $amount;

    protected $card_id;

    public function __construct($amount, $card_id)
    {
        $this->amount = $amount;
        $this->card_id = $card_id;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = Auth::user();
        if ($this->amount > intval($user->balance)) {
            $fail('مبلغ درخواستی بیشتر از موجودی شماست');
        }
        $card = Card::findOrFail($this->card_id);
        switch ($card->status) {
            case 'rejected':
                $fail('کارت انتخاب شده رد شده است.');
                break;
            case 'pending':
                $fail('کارت انتخاب شده به تایید نرسیده است.');
                break;
        }
    }
}
