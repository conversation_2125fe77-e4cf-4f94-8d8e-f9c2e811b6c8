<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Level extends Model
{
    use HasFactory;

    protected $guarded = [];

    public $timestamps = false;

    public function levelItems()
    {
        return $this->belongsToMany(
            related: LevelItem::class,
            table: 'level_type_values',
            foreignPivotKey: 'level_id',
            relatedPivotKey: 'level_item_id'
        )->distinct();
    }
}
