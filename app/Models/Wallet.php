<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Wallet extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'balance',
        'referral_balance',
        'status',
        'is_primary',
        'coin_type',
        'coin_id',
        'key',
        'type'
    ];

    public function user(){
        return $this->hasOne(User::class,'id','user_id');
    }

    public function co_users() {
        return $this->hasMany(WalletCoUser::class);
    }
    public function coin()
    {
        return $this->belongsTo(Coin::class,'coin_id');
    }

    // اضافه کردن رابطه transactions
    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class, 'wallet_id');
    }

    public function credit(float $amount): bool|int
    {
        return $this->where('balance', '>=', $amount)->decrement('balance', $amount);
    }

    public function debit(float $amount): bool|int
    {
        return $this->increment('balance', $amount);
    }

 

    // public function currency(): BelongsTo
    // {
    //     return $this->belongsTo(Currency::class);
    // }
}
