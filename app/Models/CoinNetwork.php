<?php

namespace App\Models;

use App\Models\Coin;
use App\Models\Network;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CoinNetwork extends Model
{
    use HasFactory;
    protected $fillable = [
        'uid',
        'network_id',
        'currency_id',
        'type',
        'contract_address',
        'withdrawal_fees_type',
        'withdrawal_fees',
        'status',
    ];

    public function coin()
    {
        return $this->belongsTo(Coin::class, "currency_id");
    }
    
    public function network()
    {
        return $this->belongsTo(Network::class);
    }
}
