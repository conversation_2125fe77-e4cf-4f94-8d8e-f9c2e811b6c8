<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\CurrencyProviders\TronProvider;
use App\Models\Kucoin\SubAccount;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use Has<PERSON><PERSON>Tokens, HasFactory, HasRoles, Notifiable;

    protected $with = ['transactions','cards','alerts','documents'];

    protected $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'options' => 'array',
        ];
    }

    public function documents(): HasMany
    {
        return $this->HasMany(Document::class);
    }

    public function cards(): HasMany
    {
        return $this->HasMany(Card::class);
    }

    public function getWalletsAttribute()
    {
        $currencies = Currency::where('crypto',true)->where('active','yes')->get();
        $array = [];
        foreach($currencies as $currency){
            $arrayItem = $currency->only(['id','name','code','icon','buy','sell']);
            $balance = 0;
            if($currency->id == 5){
                $userWallet = UserWallet::where('user_id', $this->id)->where('currency_id',5)->first();
                if($userWallet){
                    // $tronProvider = new TronProvider();
                    // $balance = (double)$tronProvider->wallet($this->id)->first()->balance;
                }
            }
            $arrayItem['balance'] = $balance;
            $arrayItem['amount'] = $balance * 650000;
            $array[] = $arrayItem;
        }
        return $array;
    }

    public function alerts(): HasMany
    {
        return $this->hasMany(Alert::class, 'user_id');
    }

    public function account(): HasOne
    {
        return $this->hasOne(SubAccount::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class)->with('currency');
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class, 'model_has_roles', 'model_id', 'role_id');
    }

    // public function getBalanceAttribute()
    // {
    //     return $this->transactions()->sum('amount');
    // }

    public function getIrrAmount()
    {
        return $this->transactions()
            ->where('currency_id', 3)
            ->where('type', 'increase')
            ->where('status', 'done')
            ->sum('amount')
        - $this->transactions()
            ->where('currency_id', 3)
            ->where('type', 'decrease')
            ->where('status', 'done')
            ->sum('amount');
    }

    public function getIrrAmountAttribute(){
        return $this->getIrrAmount();
    }

    public function getTomanAmount()
    {
        return $this->transactions()
            ->where('currency_id', 3) // Assuming 3 is the ID for Toman/IRR
            ->where('type', 'increase')
            ->where('status', 'done')
            ->sum('amount')
        - $this->transactions()
            ->where('currency_id', 3)
            ->where('type', 'decrease')
            ->where('status', 'done')
            ->sum('amount');
    }

    public function getTomanBalanceAttribute()
    {
        return $this->attributes['toman_balance'] ?? 0;
    }

    protected $appends = ['irrAmount','wallets','tomanBalance'];

    public function activities()
    {
        return $this->hasMany(Activity::class);
    }

    public function tickets(): HasMany
    {
        return $this->hasMany(Support::class);
    }

    public function sessions()
    {
        return $this->hasMany(Session::class);
    }

    public function walletAddressHistories()
    {
        return $this->hasMany(WalletAddressHistory::class);
    }

    public function wallets(): HasMany
    {
        return $this->hasMany(Wallet::class, 'user_id');
    }

    public function logins(): HasMany
    {
        return $this->hasMany(Login::class);
    }

    public function depositTransactions(): HasManyThrough
    {
        return $this->hasManyThrough(
            DepositeTransaction::class,
            Wallet::class,
            'user_id', // Foreign key on wallets table
            'receiver_wallet_id', // Foreign key on deposite_transactions table
            'id', // Local key on users table
            'id' // Local key on wallets table
        );
    }

}
