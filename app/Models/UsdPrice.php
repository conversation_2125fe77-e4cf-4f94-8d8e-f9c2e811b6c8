<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UsdPrice extends Model
{
    use HasFactory;

    protected $fillable = [
        'buy_price',
        'sell_price',
        'is_active'
    ];

    /**
     * Get the active USD price
     * 
     * @return UsdPrice|null
     */
    public static function getActive()
    {
        return self::where('is_active', true)->latest()->first();
    }
    
    /**
     * Convert Toman to USD
     * 
     * @param float $tomanAmount
     * @param string $type 'buy' or 'sell'
     * @return float
     */
    public static function tomanToUsd($tomanAmount, $type = 'buy')
    {
        $usdPrice = self::getActive();
        
        if (!$usdPrice) {
            return 0;
        }
        
        $rate = ($type == 'buy') ? $usdPrice->buy_price : $usdPrice->sell_price;
        
        return $tomanAmount / $rate;
    }
    
    /**
     * Convert USD to Toman
     * 
     * @param float $usdAmount
     * @param string $type 'buy' or 'sell'
     * @return float
     */
    public static function usdToToman($usdAmount, $type = 'buy')
    {
        $usdPrice = self::getActive();
        
        if (!$usdPrice) {
            return 0;
        }
        
        $rate = ($type == 'buy') ? $usdPrice->buy_price : $usdPrice->sell_price;
        
        return $usdAmount * $rate;
    }
}
