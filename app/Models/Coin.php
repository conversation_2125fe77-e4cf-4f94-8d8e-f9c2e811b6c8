<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Coin extends Model
{
    protected $fillable = [
        'name',
        'coin_type',
        'currency_type',
        'currency_id',
        'network',
        'decimal',
        'status',
        'is_withdrawal',
        'is_deposit',
        'is_buy',
        'is_sell',
        'coin_icon',
        'is_base',
        'is_currency',
        'is_primary',
        'is_wallet',
        'is_demo_trade',
        'is_transferable',
        'is_virtual_amount',
        'trade_status',
        'sign',
        'minimum_buy_amount',
        'minimum_sell_amount',
        'minimum_withdrawal',
        'maximum_withdrawal',
        'maximum_buy_amount',
        'maximum_sell_amount',
        'max_send_limit',
        'withdrawal_fees',
        'withdrawal_fees_type',
        'coin_price',
        'admin_approval',
        'ico_id',
        'is_listed',
        'last_block_number',
        'last_timestamp',
        'sync_rate_status',
        'convert_status',
        'min_convert_amount',
        'max_convert_amount',
        'convert_fee_type',
        'convert_fee',
        'market_cap',
        'to_block_number',
        'from_block_number',
    ];

    protected $casts = [
        'status' => 'boolean',
        'is_withdrawal' => 'boolean',
        'is_deposit' => 'boolean',
        'is_buy' => 'boolean',
        'is_sell' => 'boolean',
        'is_base' => 'boolean',
        'is_currency' => 'boolean',
        'is_primary' => 'boolean',
        'is_wallet' => 'boolean',
        'is_demo_trade' => 'boolean',
        'is_transferable' => 'boolean',
        'is_virtual_amount' => 'boolean',
        'trade_status' => 'boolean',
        'is_listed' => 'boolean',
        'admin_approval' => 'boolean',
        'sync_rate_status' => 'boolean',
        'convert_status' => 'boolean',
        'minimum_buy_amount' => 'decimal:8',
        'minimum_sell_amount' => 'decimal:8',
        'minimum_withdrawal' => 'decimal:8',
        'maximum_withdrawal' => 'decimal:8',
        'maximum_buy_amount' => 'decimal:8',
        'maximum_sell_amount' => 'decimal:8',
        'max_send_limit' => 'decimal:8',
        'withdrawal_fees' => 'decimal:8',
        'coin_price' => 'decimal:8',
        'min_convert_amount' => 'decimal:8',
        'max_convert_amount' => 'decimal:8',
        'convert_fee' => 'decimal:8',
        'market_cap' => 'decimal:8',
    ];

    public function setCoinTypeAttribute($value)
    {
        $this->attributes['coin_type'] = strtoupper($value);
    }

    public function coin_pair_usdt()
    {
        return $this->belongsTo(CoinPair::class, 'id', 'child_coin_id');
    }

    public function coin_network()
    {
        return $this->hasMany(CoinNetwork::class, 'currency_id');
    }

    public function coin_setting()
    {
        return $this->belongsTo(CoinSetting::class, 'coin_id');
    }
}
