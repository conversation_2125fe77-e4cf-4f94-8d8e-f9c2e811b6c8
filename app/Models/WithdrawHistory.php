<?php

namespace App\Models;

use App\Models\User;
use App\Models\Wallet;
use App\Models\Network;
use Illuminate\Database\Eloquent\Model;

class WithdrawHistory extends Model
{
    protected $fillable = [
        'receiver_wallet_id',
        'user_id',
        'wallet_id',
        'confirmations',
        'status',
        'address',
        'address_type',
        'amount',
        'fees',
        'transaction_hash',
        'message',
        'btc',
        'doller',
        'coin_type',
        'used_gas',
        'network_type',
        'network_id',
        'updated_by',
        'automatic_withdrawal',
        'memo'
    ];
    public function senderWallet(){
        return $this->belongsTo(Wallet::class,'wallet_id','id');
    }
    public function coin()
    {
        return $this->belongsTo(Coin::class, 'coin_type', 'coin_type');
    }
    public function receiverWallet(){
        return $this->belongsTo(Wallet::class,'receiver_wallet_id','id');
    }
    public function wallet()
    {
        return $this->belongsTo(Wallet::class,'wallet_id');
    }
    public function users(){
        return $this->belongsTo(User::class,'wallet_id');
    }

    public function user(){
        return $this->belongsTo(User::class);
    }
   
    public function network(){
        return $this->belongsTo(Network::class, 'network_id');
    }
}
