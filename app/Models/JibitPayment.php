<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class JibitPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'payment_identifier',
        'psp_switching_url',
        'amount',
        'currency',
        'description',
        'status',
        'reference_number',
        'trace_number',
        'paid_at',
        'callback_data',
        'request_data',
        'response_data',
        'ip_address',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'callback_data' => 'array',
        'request_data' => 'array',
        'response_data' => 'array',
    ];

    /**
     * Get the user that owns the payment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment is paid
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Check if payment is failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if payment is expired
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired';
    }

    /**
     * Mark payment as paid
     */
    public function markAsPaid(array $callbackData = []): void
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'callback_data' => $callbackData,
            'reference_number' => $callbackData['referenceNumber'] ?? null,
            'trace_number' => $callbackData['traceNumber'] ?? null,
        ]);
    }

    /**
     * Mark payment as failed
     */
    public function markAsFailed(array $callbackData = []): void
    {
        $this->update([
            'status' => 'failed',
            'callback_data' => $callbackData,
        ]);
    }

    /**
     * Mark payment as expired
     */
    public function markAsExpired(): void
    {
        $this->update([
            'status' => 'expired',
        ]);
    }

    /**
     * Scope for pending payments
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for paid payments
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope for failed payments
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for user payments
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
