<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DepositeTransaction extends Model
{
    protected $fillable = [
        'address',
        'fees',
        'sender_wallet_id',
        'receiver_wallet_id',
        'address_type',
        'coin_type',
        'amount',
        'btc',
        'doller',
        'transaction_id',
        'status',
        'confirmations',
        'from_address',
        'updated_by',
        'network_type',
        'is_admin_receive',
        'received_amount',
        'network_id',
        'block_number',
        'coin_id',
        'notification_status',
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'btc' => 'decimal:8',
        'doller' => 'decimal:8',
        'fees' => 'decimal:8',
        'received_amount' => 'decimal:8',
        'confirmations' => 'integer',
        'block_number' => 'integer',
        'is_admin_receive' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function senderWallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class, 'sender_wallet_id', 'id');
    }

    public function receiverWallet(): BelongsTo
    {
        return $this->belongsTo(Wallet::class, 'receiver_wallet_id', 'id');
    }

    // رابطه کاربر از طریق receiver wallet
    public function user()
    {
        return $this->hasOneThrough(
            User::class,
            Wallet::class,
            'id', // Foreign key on wallets table
            'id', // Foreign key on users table
            'receiver_wallet_id', // Local key on deposite_transactions table
            'user_id' // Local key on wallets table
        );
    }

    public function coin(): BelongsTo
    {
        return $this->belongsTo(Coin::class, 'coin_id', 'id');
    }

    public function network(): BelongsTo
    {
        return $this->belongsTo(Network::class, 'network_id', 'id');
    }

    // Scope برای فیلتر کردن بر اساس کاربر
    public function scopeForUser($query, $userId)
    {
        return $query->whereHas('receiverWallet', function ($q) use ($userId) {
            $q->where('user_id', $userId);
        });
    }

    // Scope برای فیلتر کردن بر اساس وضعیت
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Scope برای فیلتر کردن بر اساس نوع کوین
    public function scopeByCoinType($query, $coinType)
    {
        return $query->where('coin_type', $coinType);
    }

    // Scope برای فیلتر کردن بر اساس بازه تاریخ
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }
}
