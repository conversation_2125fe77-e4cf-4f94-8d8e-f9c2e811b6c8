<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LevelItem extends Model
{
    use HasFactory;

    protected $guarded = [];

    public $timestamps = false;

    public function levelTypes()
    {
        return $this->belongsToMany(
            related: LevelType::class,
            table: 'level_type_values',
            foreignPivotKey: 'level_item_id',
            relatedPivotKey: 'level_type_id'
        )
        ->withPivot('value')
        ->distinct();
    }
}
