<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = ['color'];

    public $timestamps = false;

    public function getColorAttribute()
    {
        return match($this->name) {
            'admin' => 'primary',
            'support' => 'info',
            'user' => 'success',
            default => 'secondary'
        };
    }
}
