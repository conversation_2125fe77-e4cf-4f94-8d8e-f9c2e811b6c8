<?php

namespace App\Models;

use App\Contracts\CurrencyProvider;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Validation\ValidationException;

class Currency extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function wallet(): HasMany
    {
        return $this->HasMany(Wallet::class);
    }

    public function provider(): CurrencyProvider
    {
        $provider = app($this->provider);
        if (! is_a($provider, CurrencyProvider::class, true)) {
            throw ValidationException::withMessages(['ارز انتخاب شده معتبر نیست.']);
        }

        return $provider;
    }
}
