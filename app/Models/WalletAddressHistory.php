<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WalletAddressHistory extends Model
{
    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function coin()
    {
        return $this->belongsTo(Coin::class);
    }

    public function network()
    {
        return $this->belongsTo(Network::class);
    }
}