<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\DepositTransactionRequest;
use App\Http\Resources\DepositTransactionResource;
use App\Services\Api\DepositTransactionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\DepositeTransaction;
use Illuminate\Support\Facades\Auth;

class DepositTransactionController extends Controller
{
    public function __construct(
        protected DepositTransactionService $service
    ) {}

    /**
     * نمایش لیست تراکنش‌های واریزی کاربر
     *
     * @param DepositTransactionRequest $request
     * @return JsonResponse
     */
    public function index(DepositTransactionRequest $request): JsonResponse
    {
        try {
            $result = $this->service->getUserDepositTransactions($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'لیست تراکنش‌های واریزی با موفقیت دریافت شد',
                'data' => [
                    'transactions' => DepositTransactionResource::collection($result['transactions']),
                    'pagination' => $result['pagination'],
                    'summary' => $result['summary']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت لیست تراکنش‌ها',
                'error' => config('app.debug') ? $e->getMessage() : 'خطای داخلی سرور'
            ], 500);
        }
    }

    /**
     * نمایش جزئیات یک تراکنش واریزی
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $transaction = DepositeTransaction::with(['coin', 'network', 'senderWallet', 'receiverWallet', 'user'])
                ->forUser($user->id)
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'جزئیات تراکنش با موفقیت دریافت شد',
                'data' => new DepositTransactionResource($transaction)
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'تراکنش مورد نظر یافت نشد یا شما به آن دسترسی ندارید',
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت جزئیات تراکنش',
                'error' => config('app.debug') ? $e->getMessage() : 'خطای داخلی سرور'
            ], 500);
        }
    }

    /**
     * دریافت خلاصه آمار تراکنش‌های واریزی کاربر
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function summary(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Get basic summary without filters
            $result = $this->service->getUserDepositTransactions([
                'per_page' => 1, // We only need summary, not transactions
                'page' => 1,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'خلاصه آمار تراکنش‌ها با موفقیت دریافت شد',
                'data' => $result['summary']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت خلاصه آمار',
                'error' => config('app.debug') ? $e->getMessage() : 'خطای داخلی سرور'
            ], 500);
        }
    }

    /**
     * دریافت لیست کوین‌هایی که کاربر تراکنش واریزی دارد
     *
     * @return JsonResponse
     */
    public function availableCoins(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $coins = DepositeTransaction::with('coin')
                ->forUser($user->id)
                ->select('coin_id', 'coin_type')
                ->distinct()
                ->get()
                ->map(function ($transaction) {
                    return [
                        'id' => $transaction->coin_id,
                        'coin_type' => $transaction->coin_type,
                        'name' => $transaction->coin->name ?? $transaction->coin_type,
                        'icon' => $transaction->coin->coin_icon ?? null,
                    ];
                })
                ->filter(function ($coin) {
                    return !is_null($coin['id']);
                })
                ->values();

            return response()->json([
                'success' => true,
                'message' => 'لیست کوین‌ها با موفقیت دریافت شد',
                'data' => $coins
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت لیست کوین‌ها',
                'error' => config('app.debug') ? $e->getMessage() : 'خطای داخلی سرور'
            ], 500);
        }
    }

    /**
     * دریافت لیست شبکه‌هایی که کاربر تراکنش واریزی دارد
     *
     * @return JsonResponse
     */
    public function availableNetworks(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $networks = DepositeTransaction::with('network')
                ->forUser($user->id)
                ->select('network_id')
                ->distinct()
                ->get()
                ->map(function ($transaction) {
                    return [
                        'id' => $transaction->network_id,
                        'name' => $transaction->network->name ?? 'نامشخص',
                        'slug' => $transaction->network->slug ?? null,
                        'chain_id' => $transaction->network->chain_id ?? null,
                    ];
                })
                ->filter(function ($network) {
                    return !is_null($network['id']);
                })
                ->values();

            return response()->json([
                'success' => true,
                'message' => 'لیست شبکه‌ها با موفقیت دریافت شد',
                'data' => $networks
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت لیست شبکه‌ها',
                'error' => config('app.debug') ? $e->getMessage() : 'خطای داخلی سرور'
            ], 500);
        }
    }
}
