<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\TransactionNotification;
use Illuminate\Http\Request;

class NotifyTransactionController extends Controller
{
    public function receive(Request $request)
    {
        try {
            $notification = TransactionNotification::create([
                'payload' => $request->all(),
                'transaction_id' => $request->input('transaction_id'), // اگر در پیلود وجود داره
                'status' => 'received'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Notification received successfully',
                'data' => $notification
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to store notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}