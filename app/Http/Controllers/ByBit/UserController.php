<?php

namespace App\Http\Controllers\ByBit;

use App\Http\Controllers\Controller;
use App\Http\Requests\ByBit\User\StoreSubUserRequest;
use App\Services\ByBit\SubUserService;
use Illuminate\Http\Request;

class UserController extends Controller
{
    public function __construct(
        protected SubUserService $service,
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return $this->service->indexAllSubUsers();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSubUserRequest $request)
    {
        // return '';
        return $this->service->createSubUser($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
