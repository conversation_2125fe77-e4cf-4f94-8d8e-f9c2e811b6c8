<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\FrequencyQuestion\StoreFrequencyQuestionRequest;
use App\Http\Requests\Admin\FrequencyQuestion\UpdateFrequencyQuestionRequest;
use App\Services\Admin\FrequencyQuestionService;
use Illuminate\Http\Request;

class FrequencyQuestionController extends Controller
{
    public function __construct(
        protected FrequencyQuestionService $service
    ){}
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return $this->service->allQuestions($request);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreFrequencyQuestionRequest $request)
    {
        return $this->service->createQuestion($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->service->showQuestion($id);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateFrequencyQuestionRequest $request, string $id)
    {
        return $this->service->updateQuestion($id, $request->validated());
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->service->deleteQuestion($id);
        return response()->noContent();
    }
}
