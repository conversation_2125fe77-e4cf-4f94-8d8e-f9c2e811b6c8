<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CoinSaveRequest;
use App\Models\Coin;
use App\Http\Requests\Admin\Coin\StoreCoinRequest;
use App\Http\Requests\Admin\Coin\UpdateCoinRequest;
use App\Models\CurrencyList;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class CoinController extends Controller
{
    public function index()
    {
        $coins = Coin::paginate(15);
        return view('admin.coins.index', compact('coins'));
    }

    public function create()
    {
        return view('admin.coins.create');
    }

    public function store(CoinSaveRequest $request)
    {
        try {
            $data = [
                'currency_type' => $request->currency_type,
                'name' => $request->name,
                'coin_type' => strtoupper($request->coin_type),
                'network' => 0,
                'decimal' => $request->decimal ? intval($request->decimal) : 18
            ];

            if ($request->currency_type == CURRENCY_TYPE_FIAT) {
                if ($currency = CurrencyList::whereCode($request->coin_type)->first()) {
                    $data['currency_id'] = $currency->id;
                    $data['coin_price'] = bcdiv(1, $currency->rate, 8);
                }
            } else {
                if ($request->get_price_api == 1) {
                    $pair = strtoupper($request->coin_type).'_'.'USDT';
                    $apiData = getPriceFromApi($pair);
                    if ($apiData['success'] == true) {
                        $data['coin_price'] = $apiData['data']['price'];
                    } else {
                        return redirect()
                            ->back()
                            ->with('error', __('Get api data failed, please add manual price'));
                    }
                } else {
                    $data['coin_price'] = $request->coin_price;
                }
            }

            if ($request->hasFile('coin_icon')) {
                $data['coin_icon'] = $request->file('coin_icon')->store('coins', 'public');
            }

            $save = Coin::create($data);
            
            if ($save) {
                return redirect()
                    ->route('admin.coins.index')
                    ->with('success', __('New coin added successfully'));
            }

            return redirect()
                ->back()
                ->with('error', __('Something went wrong'));

        } catch (\Exception $e) {
            storeException('store coin: ', $e->getMessage());
            return redirect()
                ->back()
                ->with('error', __('Something went wrong'));
        }
    }

    public function show(Coin $coin)
    {
        return view('admin.coins.show', compact('coin'));
    }

    public function edit(Coin $coin)
    {
        return view('admin.coins.form', compact('coin'));
    }

    public function update(UpdateCoinRequest $request, Coin $coin)
    {
        DB::beginTransaction();
        try {
            $data = $request->validated();
            
            // اطمینان از اینکه currency_type به عنوان عدد ذخیره می‌شود
            if (isset($data['currency_type'])) {
                $data['currency_type'] = (int) $data['currency_type'];
            }

            // اگر آیکون جدید آپلود شده
            if ($request->hasFile('coin_icon')) {
                // حذف آیکون قدیمی
                if ($coin->coin_icon) {
                    Storage::disk('public')->delete($coin->coin_icon);
                }
                $data['coin_icon'] = $request->file('coin_icon')->store('coins', 'public');
            }

            // تبدیل مقادیر boolean
            $booleanFields = [
                'status', 'is_withdrawal', 'is_deposit', 'is_buy', 'is_sell',
                'is_base', 'is_currency', 'is_primary', 'is_wallet',
                'is_demo_trade', 'is_transferable', 'is_virtual_amount',
                'trade_status', 'is_listed', 'admin_approval',
                'sync_rate_status', 'convert_status'
            ];

            foreach ($booleanFields as $field) {
                if (isset($data[$field])) {
                    $data[$field] = filter_var($data[$field], FILTER_VALIDATE_BOOLEAN);
                }
            }

            // تبدیل مقادیر decimal
            $decimalFields = [
                'minimum_buy_amount', 'minimum_sell_amount', 'minimum_withdrawal',
                'maximum_withdrawal', 'maximum_buy_amount', 'maximum_sell_amount',
                'max_send_limit', 'withdrawal_fees', 'coin_price',
                'min_convert_amount', 'max_convert_amount', 'convert_fee',
                'market_cap'
            ];

            foreach ($decimalFields as $field) {
                if (isset($data[$field])) {
                    $data[$field] = number_format((float)$data[$field], 8, '.', '');
                }
            }

            $coin->update($data);
            
            DB::commit();

            return redirect()
                ->route('admin.coins.index')
                ->with('success', 'ارز با موفقیت بروزرسانی شد');
                
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Coin Update Error: ' . $e->getMessage());
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'خطا در بروزرسانی ارز: ' . $e->getMessage());
        }
    }

    public function destroy(Coin $coin)
    {
        try {
            if ($coin->coin_icon) {
                Storage::disk('public')->delete($coin->coin_icon);
            }
            
            $coin->delete();

            return response()->json([
                'success' => true,
                'message' => 'ارز با موفقیت حذف شد'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در حذف ارز: ' . $e->getMessage()
            ], 500);
        }
    }

    public function search(Request $request)
    {
        $query = Coin::query();

        // اگر نوع ارز مشخص شده باشد
        if ($request->has('coin_type')) {
            $query->where('coin_type', strtoupper($request->coin_type));
        }

        // اگر نام ارز مشخص شده باشد
        if ($request->has('name')) {
            $query->where('name', 'LIKE', '%' . $request->name . '%');
        }

        // جستجوی ارز ترون با مشخصات پیش‌فرض
        if ($request->has('is_tron') && $request->is_tron) {
            $query->where([
                'coin_type' => 'TRX',
                'currency_type' => 1,
                'status' => 1,
                'admin_approval' => 1,
                'is_withdrawal' => 1,
                'is_deposit' => 1,
                'is_buy' => 1,
                'is_sell' => 1,
                'is_base' => 1,
                'trade_status' => 1
            ]);
        }

        // فیلتر بر اساس وضعیت
        if ($request->has('status')) {
            $query->where('status', $request->boolean('status'));
        }

        // فیلتر برای قابلیت برداشت
        if ($request->has('is_withdrawal')) {
            $query->where('is_withdrawal', $request->boolean('is_withdrawal'));
        }

        // فیلتر برای قابلیت واریز
        if ($request->has('is_deposit')) {
            $query->where('is_deposit', $request->boolean('is_deposit'));
        }

        // فیلتر برای شبکه
        if ($request->has('network')) {
            $query->where('network', $request->network);
        }

        return $query->get();
    }
}
