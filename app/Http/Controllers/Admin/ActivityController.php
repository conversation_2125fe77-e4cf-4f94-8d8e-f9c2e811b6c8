<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use Illuminate\Http\Request;

class ActivityController extends Controller
{
    public function index(Request $request)
    {
        $query = Activity::with('user')->orderByDesc('id');
        
        // Apply filters
        if ($request->search) {
            $query->where(function($q) use ($request) {
                $q->where('address', 'like', "%{$request->search}%")
                  ->orWhere('ip', 'like', "%{$request->search}%");
            });
        }
        
        if ($request->method) {
            $query->where('method', $request->method);
        }
        
        if ($request->date_from) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        
        if ($request->date_to) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }
        
        $activities = $query->paginate(20);
        
        return view('admin.activities.card', compact('activities'));
    }

    public function show($id)
    {
        $activity = Activity::with('user')->findOrFail($id);
        return view('admin.activities.show', compact('activity'));
    }
}
