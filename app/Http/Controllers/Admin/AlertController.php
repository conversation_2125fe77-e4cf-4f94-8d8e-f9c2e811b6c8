<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Alert;
use App\Models\User;
use Illuminate\Http\Request;

class AlertController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Alert::with('user')->orderBy('created_at', 'desc');
        
        // Filter by type if provided
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }
        
        // Filter by read status if provided
        if ($request->has('read') && $request->read !== null) {
            $query->where('read', $request->read == 'true');
        }
        
        // Filter by user if provided
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }
        
        $alerts = $query->paginate(20);
        $users = User::select('id', 'firstname', 'lastname')->get();
        
        return view('admin.alerts.index', compact('alerts', 'users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $users = User::select('id', 'firstname', 'lastname')->get();
        return view('admin.alerts.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'message' => 'required|string',
            'type' => 'required|in:warning,error,success,info,primary',
            'user_id' => 'nullable|exists:users,id',
        ]);
        
        Alert::create([
            'message' => $request->message,
            'type' => $request->type,
            'user_id' => $request->user_id,
            'read' => false,
        ]);
        
        return redirect()->route('admin.alerts.index')
            ->with('success', 'هشدار با موفقیت ایجاد شد.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $alert = Alert::with('user')->findOrFail($id);
        return view('admin.alerts.show', compact('alert'));
    }

    /**
     * Mark an alert as read.
     */
    public function markAsRead(string $id)
    {
        $alert = Alert::findOrFail($id);
        $alert->update(['read' => true]);
        
        return redirect()->back()
            ->with('success', 'هشدار به عنوان خوانده شده علامت‌گذاری شد.');
    }

    /**
     * Mark an alert as unread.
     */
    public function markAsUnread(string $id)
    {
        $alert = Alert::findOrFail($id);
        $alert->update(['read' => false]);
        
        return redirect()->back()
            ->with('success', 'هشدار به عنوان خوانده نشده علامت‌گذاری شد.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $alert = Alert::findOrFail($id);
        $alert->delete();
        
        return redirect()->route('admin.alerts.index')
            ->with('success', 'هشدار با موفقیت حذف شد.');
    }
    
    /**
     * Mark all alerts as read.
     */
    public function markAllAsRead(Request $request)
    {
        $query = Alert::where('read', false);
        
        // If user_id is provided, only mark alerts for that user as read
        if ($request->has('user_id') && $request->user_id) {
            $query->where('user_id', $request->user_id);
        }
        
        $query->update(['read' => true]);
        
        return redirect()->back()
            ->with('success', 'تمام هشدارها به عنوان خوانده شده علامت‌گذاری شدند.');
    }
}
