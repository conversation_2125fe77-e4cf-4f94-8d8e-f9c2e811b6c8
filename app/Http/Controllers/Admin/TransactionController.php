<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\TransactionFilterRequest;
use App\Http\Requests\Admin\UpdateTransactionRequest;
use App\Models\Coin;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Services\Admin\TransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TransactionController extends Controller
{
    public function __construct(protected TransactionService $service) {}

    public function index(TransactionFilterRequest $request, ?User $user = null, ?Wallet $wallet = null)
    {
        // Get statistics
        $stats = [
            'total_count' => Transaction::count(),
            'success_count' => Transaction::where('status', 'done')->count(),
            'today_count' => Transaction::whereDate('created_at', now()->toDateString())->count(),
            'active_users' => Transaction::distinct('user_id')->count('user_id'),
        ];

        // Get all currencies/coins for filter dropdown
        $currencies = Coin::where('status', true)->get();

        // Get transactions
        $transactions = $this->service->index($request->validated(), $wallet ?? $user);

        return view('admin.transaction.index', [
            'transactions' => $transactions,
            'stats' => $stats,
            'currencies' => $currencies,
        ]);
    }

    public function show($id)
    {
        $transaction = $this->service->show($id);
        return view('admin.transaction.show', ['transaction' => $transaction]);
    }

    /**
     * Get transactions by type
     *
     * @param string $type
     * @param TransactionFilterRequest $request
     * @return \Illuminate\Contracts\View\View
     */
    public function indexByType($type, TransactionFilterRequest $request)
    {
        $request->merge(['type' => $type]);

        // Get statistics
        $stats = [
            'total_count' => Transaction::where('type', $type)->count(),
            'success_count' => Transaction::where('type', $type)->where('status', 'done')->count(),
            'today_count' => Transaction::where('type', $type)->whereDate('created_at', now()->toDateString())->count(),
            'active_users' => Transaction::where('type', $type)->distinct('user_id')->count('user_id'),
        ];

        // Get all currencies/coins for filter dropdown
        $currencies = Coin::where('status', true)->get();

        // Get transactions
        $transactions = $this->service->index($request->validated());

        return view('admin.transaction.index', [
            'transactions' => $transactions,
            'stats' => $stats,
            'currencies' => $currencies,
        ]);
    }

    /**
     * Get transactions by status
     *
     * @param string $status
     * @param TransactionFilterRequest $request
     * @return \Illuminate\Contracts\View\View
     */
    public function indexByStatus($status, TransactionFilterRequest $request)
    {
        $request->merge(['status' => $status]);

        // Get statistics
        $stats = [
            'total_count' => Transaction::where('status', $status)->count(),
            'success_count' => Transaction::where('status', $status)->count(),
            'today_count' => Transaction::where('status', $status)->whereDate('created_at', now()->toDateString())->count(),
            'active_users' => Transaction::where('status', $status)->distinct('user_id')->count('user_id'),
        ];

        // Get all currencies/coins for filter dropdown
        $currencies = Coin::where('status', true)->get();

        // Get transactions
        $transactions = $this->service->index($request->validated());

        return view('admin.transaction.index', [
            'transactions' => $transactions,
            'stats' => $stats,
            'currencies' => $currencies,
        ]);
    }

    /**
     * Get transactions by user
     *
     * @param int $userId
     * @param TransactionFilterRequest $request
     * @return \Illuminate\Contracts\View\View
     */
    public function indexByUser($userId, TransactionFilterRequest $request)
    {
        $request->merge(['user_id' => $userId]);

        // Get user
        $user = User::findOrFail($userId);

        // Get statistics
        $stats = [
            'total_count' => Transaction::where('user_id', $userId)->count(),
            'success_count' => Transaction::where('user_id', $userId)->where('status', 'done')->count(),
            'today_count' => Transaction::where('user_id', $userId)->whereDate('created_at', now()->toDateString())->count(),
            'active_users' => 1,
        ];

        // Get all currencies/coins for filter dropdown
        $currencies = Coin::where('status', true)->get();

        // Get transactions
        $transactions = $this->service->index($request->validated());

        return view('admin.transaction.index', [
            'transactions' => $transactions,
            'stats' => $stats,
            'currencies' => $currencies,
            'user' => $user,
        ]);
    }

    /**
     * Get transactions by currency
     *
     * @param int $currencyId
     * @param TransactionFilterRequest $request
     * @return \Illuminate\Contracts\View\View
     */
    public function indexByCurrency($currencyId, TransactionFilterRequest $request)
    {
        $request->merge(['currency_id' => $currencyId]);

        // Get currency
        $currency = Coin::findOrFail($currencyId);

        // Get statistics
        $stats = [
            'total_count' => Transaction::where('currency_id', $currencyId)->count(),
            'success_count' => Transaction::where('currency_id', $currencyId)->where('status', 'done')->count(),
            'today_count' => Transaction::where('currency_id', $currencyId)->whereDate('created_at', now()->toDateString())->count(),
            'active_users' => Transaction::where('currency_id', $currencyId)->distinct('user_id')->count('user_id'),
        ];

        // Get all currencies/coins for filter dropdown
        $currencies = Coin::where('status', true)->get();

        // Get transactions
        $transactions = $this->service->index($request->validated());

        return view('admin.transaction.index', [
            'transactions' => $transactions,
            'stats' => $stats,
            'currencies' => $currencies,
            'selected_currency' => $currency,
        ]);
    }

    /**
     * Export transactions to CSV
     *
     * @param TransactionFilterRequest $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(TransactionFilterRequest $request)
    {
        // Get transactions
        $transactions = $this->service->getAllForExport($request->validated());

        // Create CSV file
        $filename = 'transactions_' . now()->format('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($transactions) {
            $file = fopen('php://output', 'w');

            // Add headers
            fputcsv($file, [
                'ID', 'User', 'Type', 'Amount', 'Currency', 'Status', 'Balance Before', 'Balance After', 'Date'
            ]);

            // Add data
            foreach ($transactions as $transaction) {
                fputcsv($file, [
                    $transaction->id,
                    $transaction->user ? $transaction->user->email : 'N/A',
                    $transaction->type,
                    $transaction->amount,
                    $transaction->currency ? $transaction->currency->coin_type : 'N/A',
                    $transaction->status,
                    $transaction->balance_before ?? 0,
                    $transaction->balance_after ?? 0,
                    $transaction->created_at ? $transaction->created_at->format('Y-m-d H:i:s') : 'N/A',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function update($id, UpdateTransactionRequest $request)
    {
        $this->service->update($id, $request->validated());
        // return
    }
}
