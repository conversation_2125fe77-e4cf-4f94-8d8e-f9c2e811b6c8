<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Accounting\StoreSettlementRequest;
use App\Models\AdminSetting;
use App\Services\Admin\Accounting\SettlementInfoService;
use App\Services\Admin\Accounting\SettlementService;
use Illuminate\Http\Request;

class SettlementInfoController extends Controller
{
    public function __construct(
        protected SettlementInfoService $service
    ) {}
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return $this->service->settlementList($request);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSettlementRequest $request)
    {
        return $this->service->storeSettlement($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
