<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Controller;
use App\Models\ModelHasRole;
use App\Models\Transaction;
use App\Models\User;

class UserInfoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $queryUsers = User::query();

        return [
            'countAll' => $queryUsers->count(),
            'countPending' => $queryUsers->where('status', 'pending')->count(),
            'countAdmin' => ModelHasRole::where('role_id', 1)->where('model_type', 'App\Models\User')->distinct('model_id')->count('model_id'),
            'countUserIrr' => Transaction::where('currency_id', 3)->distinct('user_id')->count(),
            'countUserCrypto' => Transaction::whereNot('currency_id', 3)->distinct('user_id')->count(),
            'countUserLastMonth' => User::whereMonth('created_at', date('m', strtotime(date('Y-m-d').'-1 month')))->count(),
            'countUserBirth' => User::whereMonth('birth_date', date('m'))->whereDay('birth_date', date('d'))->count(),
        ];
    }
}
