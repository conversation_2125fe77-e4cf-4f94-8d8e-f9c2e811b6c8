<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Controller;
use App\Models\User;

class UserBirthController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(string $filter = '')
    {
        // ایجاد کوئری برای مدل User با فیلتر تاریخ تولد
        $queryUsers = User::whereMonth('birth_date', date('m'))
            ->whereDay('birth_date', date('d'))
            ->with('roles', 'transactions'); // بارگذاری پیش‌فرض نقش‌ها و تراکنش‌ها

        // اعمال فیلتر بر روی کوئری
        if ($filter) {
            $queryUsers->where(function ($query) use ($filter) {
                $query->where('phone', 'LIKE', "%$filter%")
                    ->orWhere('email', 'LIKE', "%$filter%")
                    ->orWhere('firstname', 'LIKE', "%$filter%")
                    ->orWhere('lastname', 'LIKE', "%$filter%")
                    ->orWhere('id', 'LIKE', "%$filter%")
                    ->orWhere('national_id', 'LIKE', "%$filter%");
            });
        }

        // صفحه‌بندی
        $perPage = request()->input('per_page', 30); // تعداد آیتم‌ها در هر صفحه، به طور پیش‌فرض ۳۰
        $page = request()->input('page', 1); // شماره صفحه، به طور پیش‌فرض ۱

        $users = $queryUsers->paginate($perPage, ['*'], 'page', $page)
            ->through(function ($user) {
                $user->balance = $user->transactions->sum('amount'); // محاسبه موجودی

                return $user;
            });

        return $users;
    }
}
