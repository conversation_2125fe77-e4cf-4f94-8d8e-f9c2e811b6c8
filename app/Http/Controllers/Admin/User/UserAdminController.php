<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Controller;
use App\Models\ModelHasRole;
use App\Models\User;

class UserAdminController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(string $filter = '')
    {
        $userIds = ModelHasRole::where('role_id', 1)
            ->where('model_type', 'App\Models\User')
            ->distinct('model_id')
            ->pluck('model_id');

        $queryUsers = User::whereIn('id', $userIds)
            ->with('roles', 'transactions');

        if ($filter) {
            $queryUsers->where(function ($query) use ($filter) {
                $query->where('phone', 'LIKE', "%$filter%")
                    ->orWhere('email', 'LIKE', "%$filter%")
                    ->orWhere('firstname', 'LIKE', "%$filter%")
                    ->orWhere('lastname', 'LIKE', "%$filter%")
                    ->orWhere('id', 'LIKE', "%$filter%")
                    ->orWhere('national_id', 'LIKE', "%$filter%");
            });
        }

        $perPage = request()->input('per_page', 30);
        $page = request()->input('page', 1);

        $users = $queryUsers->paginate($perPage, ['*'], 'page', $page)
            ->through(function ($user) {
                $user->balance = $user->transactions->sum('amount');

                return $user;
            });

        return $users;
    }
}
