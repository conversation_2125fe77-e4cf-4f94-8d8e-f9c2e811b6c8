<?php

namespace App\Http\Controllers\Admin;

use App\Models\Coin;
use App\Models\Network;
use App\Models\CoinNetwork;
use Illuminate\Http\Request;
use App\Models\AffiliationHistory;
use App\Models\DepositeTransaction;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Services\WalletService;
use App\Jobs\TokenReceiveToAdminJob;
use Illuminate\Support\Facades\Auth;
use App\Http\Services\DepositService;
use App\Jobs\PendingDepositAcceptJob;
use App\Jobs\PendingDepositRejectJob;
use Illuminate\Support\Facades\Schema;
use App\Http\Services\BitgoWalletService;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\Admin\DepositRequest;
use App\Services\Evm\EvmWalletService;
use App\Http\Repositories\CustomTokenRepository;
use App\Models\EstimateGasFeesTransactionHistory;
use App\Models\AdminReceiveTokenTransactionHistory;
use Modules\IcoLaunchpad\Entities\TokenBuyHistory;

class DepositController extends Controller
{
    public function adminGasSendHistory(Request $request)
    {
        $data['title'] = __('تاریخچه ارسال گس تخمینی ادمین');
        if ($request->ajax()) {
            $items = EstimateGasFeesTransactionHistory::join('deposite_transactions', 'deposite_transactions.id', '=','estimate_gas_fees_transaction_histories.deposit_id')
                ->select('estimate_gas_fees_transaction_histories.*','deposite_transactions.coin_type as token');

            return datatables()->of($items)
                ->addColumn('created_at', function ($item) {
                    return $item->created_at;
                })
                ->addColumn('status', function ($item) {
                    return deposit_status($item->status);
                })
                ->rawColumns(['status'])
                ->make(true);
        }

        return view('admin.transaction.deposit.gas_sent_history', $data);
    }

    public function adminTokenReceiveHistory(Request $request)
    {
        $data['title'] = __('تاریخچه دریافت توکن ادمین');
        if ($request->ajax()) {
            $items = AdminReceiveTokenTransactionHistory::join('deposite_transactions', 'deposite_transactions.id', '=','admin_receive_token_transaction_histories.deposit_id')
                ->select('admin_receive_token_transaction_histories.*','deposite_transactions.coin_type');

            return datatables()->of($items)
                ->addColumn('created_at', function ($item) {
                    return $item->created_at;
                })
                ->addColumn('status', function ($item) {
                    return deposit_status($item->status);
                })
                ->rawColumns(['status'])
                ->make(true);
        }

        return view('admin.transaction.deposit.token_receive_history', $data);
    }

    public function adminPendingDepositHistory(Request $request)
    {
        $data['buy_token'] = false;
        if (Schema::hasTable('token_buy_histories')) {
            $data['buy_token'] = true;
        }
        $data['title'] = __('تاریخچه واریزهای در انتظار');

        if ($request->ajax()) {
            $items = DepositeTransaction::join('coins','coins.coin_type', '=', 'deposite_transactions.coin_type')
                ->join('networks','networks.id', '=', 'deposite_transactions.network_id')
                ->whereNotIn('networks.base_type',[COIN_PAYMENT,BITCOIN_API,BITGO_API])
                ->where(['deposite_transactions.address_type' => ADDRESS_TYPE_EXTERNAL])
                ->where('deposite_transactions.is_admin_receive', STATUS_PENDING)
                ->select('deposite_transactions.*')
                ->orderBy('id','desc');

            return datatables()->of($items)
                ->addColumn('created_at', function ($item) {
                    return $item->created_at;
                })
                ->addColumn('from_address', function ($item) {
                    return '<div class="address-text" title="'.$item->from_address.'">'.substr($item->from_address, 0, 15).'...</div>';
                })
                ->addColumn('address', function ($item) {
                    return '<div class="address-text" title="'.$item->address.'">'.substr($item->address, 0, 15).'...</div>';
                })
                ->addColumn('transaction_id', function ($item) {
                    return '<div class="tx-hash" title="'.$item->transaction_id.'">'.substr($item->transaction_id, 0, 15).'...</div>';
                })
                ->addColumn('status', function ($item) {
                    return deposit_status($item->status);
                })
                ->addColumn('actions', function ($wdrl) {
                    $action = '<div class="action-buttons d-flex justify-content-center">';
                    $action .= accept_html('admin.deposit.adminPendingDepositAccept',encrypt($wdrl->id));
                    $action .= '</div>';

                    return $action;
                })
                ->rawColumns(['actions','status','from_address','address','transaction_id'])
                ->make(true);
        }

        return view('admin.transaction.deposit.token_pending_deposit_history', $data);
    }

    public function adminPendingDepositReject($id)
    {
        if (isset($id)) {
            try {
                $wdrl_id = decrypt($id);
            } catch (\Exception $e) {
                return redirect()->back();
            }
            $transaction = DepositeTransaction::where(['id' => $wdrl_id, 'status' => STATUS_PENDING, 'address_type' => ADDRESS_TYPE_EXTERNAL])->first();

            if (!empty($transaction)) {
                dispatch(new PendingDepositRejectJob($transaction,Auth::id()))->onQueue('deposit');
                return redirect()->back()->with('success', __('درخواست رد واریز در صف قرار گرفت. لطفا منتظر بمانید'));
            } else {
                return redirect()->back()->with('dismiss', __('واریز در انتظار یافت نشد'));
            }
        }
    }

    public function adminPendingDepositAccept($id)
    {
        try {
            $wdrl_id = decrypt($id);

            $transactions = DepositeTransaction::join('coins','coins.coin_type', '=', 'deposite_transactions.coin_type')
                ->where(['deposite_transactions.address_type' => ADDRESS_TYPE_EXTERNAL])
                ->where('deposite_transactions.is_admin_receive', STATUS_PENDING)
                ->where('deposite_transactions.id', $wdrl_id)
                ->select('deposite_transactions.*')
                ->first();

            if (!empty($transactions)) {
                $network = Network::find($transactions->network_id);
                if ($network) {
                    if($network->base_type == TRC20_TOKEN || $network->base_type == EVM_BASE_COIN || $network->base_type == SOLONA_BASE_COIN) {
                        $response = (new EvmWalletService)->acceptDepositFromUser($transactions->id);
                        storeException('adminPendingDepositAccept response', $response);
                        if ($response['success'] == true) {
                            return redirect()->back()->with('success', $response['message']);
                        } else {
                            return redirect()->back()->with('dismiss', $response['message']);
                        }
                    } else {
                        return redirect()->back()->with('dismiss', __('فقط واریز ارزهای مبتنی بر EVM در اینجا امکان‌پذیر است'));
                    }
                } else {
                    return redirect()->back()->with('dismiss', __('شبکه‌ای یافت نشد'));
                }
            } else {
                return redirect()->back()->with('dismiss', __('واریز در انتظار یافت نشد'));
            }

        } catch (\Exception $e) {
            storeException('adminPendingDepositAccept ex', $e->getMessage());
            return redirect()->back()->with('dismiss', __('در حال پردازش. کمی طول می‌کشد، لطفا صبر کنید'));
        }
    }

    public function adminCheckDeposit()
    {
        $data['title'] = __('بررسی واریز');
        $data['networks'] = Network::whereStatus(STATUS_ACTIVE)->get();
        $data['coin_list'] = Coin::where(['status' => STATUS_ACTIVE])->get();
        return view('admin.transaction.deposit.check-deposit', $data);
    }

    public function submitCheckDeposit(DepositRequest $request)
    {
        try {
            $service = new DepositService();
            $response = $service->checkDepositByHash($request->network,$request->coin_network,$request->transaction_id,$request->type);
            if ($response['success'] == true) {
                if ($request->type == CHECK_DEPOSIT) {
                    $data = $response['data'];
                    $data['networks'] = Network::whereStatus(STATUS_ACTIVE)->get();
                    $data['network'] = $request->network;
                    $data['coin_network'] = $request->coin_network;
                    $data['transaction_id'] = $request->transaction_id;
                    $data['type'] = $request->type;
                    $data['title'] = __('جزئیات تراکنش');
                    $data['coin_list'] = Coin::where(['status' => STATUS_ACTIVE])->get();

                    return view('admin.transaction.deposit.check-deposit', $data);
                }
            } else {
                return redirect()->route('admin.deposit.check')->with('dismiss', $response['message']);
            }
        } catch (\Exception $e) {
            storeException("submitCheckDeposit",$e->getMessage());
            return redirect()->route('admin.deposit.check')->with('dismiss', $e->getMessage());
        }
    }

    public function icoTokenBuyListAccept(Request $request)
    {
        if ($request->ajax()) {
            $tokenBuyHistories = DB::table('token_buy_histories')
                ->where('token_buy_histories.status', STATUS_ACCEPTED)
                ->join('coins', 'token_buy_histories.coin_id', '=', 'coins.id')
                ->join('ico_tokens', 'token_buy_histories.token_id', '=', 'ico_tokens.id')
                ->join('wallet_address_histories', 'token_buy_histories.wallet_id', '=', 'wallet_address_histories.wallet_id')
                ->where('coins.is_listed', STATUS_ACTIVE)
                ->where('token_buy_histories.is_admin_receive', STATUS_PENDING)
                ->select(
                    'token_buy_histories.*',
                    'coins.coin_type',
                    'ico_tokens.wallet_address as from_address',
                    'wallet_address_histories.address'
                );

            return datatables()->of($tokenBuyHistories)
                ->addColumn('from_address', function ($item) {
                    return '<div class="address-text" title="'.$item->from_address.'">'.substr($item->from_address, 0, 15).'...</div>';
                })
                ->addColumn('address', function ($item) {
                    return '<div class="address-text" title="'.$item->address.'">'.substr($item->address, 0, 15).'...</div>';
                })
                ->addColumn('transaction_id', function ($item) {
                    if (isset($item->transaction_id)) {
                        return '<div class="tx-hash" title="'.$item->transaction_id.'">'.substr($item->transaction_id, 0, 15).'...</div>';
                    }
                    return 'N/A';
                })
                ->addColumn('actions', function ($item) {
                    return '<div class="action-buttons d-flex justify-content-center">' .
                        accept_html('admin.deposit.adminReceiveBuyTokenAmount', encrypt($item->id)) .
                        '</div>';
                })
                ->addColumn('status', function ($item) {
                    return deposit_status($item->status);
                })
                ->rawColumns(['actions', 'status', 'from_address', 'address', 'transaction_id'])
                ->make(true);
        }

        return view('admin.transaction.deposit.token_pending_deposit_history');
    }

    public function adminReceiveBuyTokenAmount($id)
    {
        if (isset($id)) {
            try {
                $wdrl_id = decrypt($id);
            } catch (\Exception $e) {
                return redirect()->back();
            }
            $transactions = DB::table('token_buy_histories')->where('token_buy_histories.status',STATUS_ACCEPTED)
                ->where('token_buy_histories.is_admin_receive',STATUS_PENDING)
                ->join('coins','token_buy_histories.coin_id', '=', 'coins.id')
                ->join('ico_tokens','token_buy_histories.token_id', '=', 'ico_tokens.id')
                ->join('wallet_address_histories','token_buy_histories.wallet_id', '=', 'wallet_address_histories.wallet_id')
                ->where('coins.is_listed',STATUS_ACTIVE)
                ->select('token_buy_histories.*','coins.coin_type as coin_type',
                    'ico_tokens.wallet_address as from_address',
                    'wallet_address_histories.address as address',
                    'token_buy_histories.blockchain_tx as transaction_id')
                ->first();

            if (!empty($transactions)) {
                dispatch(new TokenReceiveToAdminJob($transactions,Auth::id()))->onQueue('deposit');
                return redirect()->back()->with('success', __('پذیرش توکن به آدرس ادمین در صف قرار گرفت. لطفا منتظر بمانید و چند بار کلیک نکنید'));
            } else {
                return redirect()->back()->with('dismiss', __('اطلاعات یافت نشد'));
            }
        }
    }

    public function getCurrencyByNetworkID($network_id)
    {
        try {
            if (!($network = Network::find($network_id))) {
                return response()->json(responseData(false, __('شبکه یافت نشد')));
            }

            $currency = CoinNetwork::with(['coin' => function($query) {
                $query->where('status', STATUS_ACTIVE);
            }])
            ->where("network_id", $network_id)
            ->whereHas('coin')  // اضافه کردن این خط برای اطمینان از وجود coin
            ->get();

            if ($currency->isEmpty()) {
                return response()->json(responseData(false, __('ارز یافت نشد')));
            }

            return response()->json(responseData(true, __('ارز با موفقیت یافت شد'), $currency));
        } catch (\Exception $e) {
            storeException('getCurrencyByNetworkID', $e->getMessage());
            return response()->json(responseData(false, __('خطا در دریافت ارزها')));
        }
    }
}
