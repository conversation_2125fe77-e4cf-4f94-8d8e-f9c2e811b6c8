<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Bank\StoreBankRequest;
use App\Http\Requests\Admin\Bank\UpdateBankRequest;
use App\Models\Bank;
use App\Services\Admin\BankService;
use Illuminate\Http\Request;

class BankController extends Controller
{
    public function __construct(
        protected BankService $service
    ) {}

    /**
     * Display a listing of the banks.
     */
    public function index()
    {
        $banks = $this->service->listBanks();
        return view('admin.bank.index', compact('banks'));
    }

    /**
     * Show the form for creating a new bank.
     */
    public function create()
    {
        return view('admin.bank.create');
    }

    /**
     * Store a newly created bank in storage.
     */
    public function store(StoreBankRequest $request)
    {
        $result = $this->service->createBank($request);
        
        if ($result['success']) {
            return redirect()
                ->route('admin.banks.index')
                ->with('success', $result['message']);
        }

        return back()
            ->withInput()
            ->with('error', $result['message']);
    }

    /**
     * Show the form for editing the specified bank.
     */
    public function edit($id)
    {
        $bank = Bank::findOrFail($id);
        return view('admin.bank.create', compact('bank'));
    }

    /**
     * Update the specified bank in storage.
     */
    public function update(UpdateBankRequest $request, $id)
    {
        $result = $this->service->updateBank($id, $request);
        
        if ($result['success']) {
            return redirect()
                ->route('admin.banks.index')
                ->with('success', $result['message']);
        }

        return back()
            ->withInput()
            ->with('error', $result['message']);
    }

    /**
     * Remove the specified bank from storage.
     */
    public function destroy($id)
    {
        $result = $this->service->deleteBank($id);
        
        if ($result['success']) {
            return redirect()
                ->route('admin.banks.index')
                ->with('success', $result['message']);
        }

        return back()->with('error', $result['message']);
    }

    /**
     * Toggle the status of the specified bank.
     */
    public function toggleStatus(Request $request)
    {
        $bank = Bank::findOrFail($request->id);
        $bank->status = !$bank->status;
        $bank->save();

        return response()->json([
            'success' => true,
            'message' => 'وضعیت بانک با موفقیت تغییر کرد'
        ]);
    }
}
