<?php

namespace App\Http\Controllers\Admin;

use App\Models\Network;
use App\Models\SupportedNetwork;
use App\Http\Controllers\Controller;
use App\Http\Services\NetworkService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;

class NetworkController extends Controller
{
    protected $service;

    public function __construct(NetworkService $service)
    {
        $this->service = $service;
    }

    public function index()
    {
        $networks = Network::paginate(15);
        return view('admin.network.index', compact('networks'));
    }

    public function create()
    {
        $supportedNetworks = SupportedNetwork::whereNotIn('type', ['COIN_PAYMENT', 'BITCOIN_API', 'BITGO_API'])->get();
        return view('admin.network.create', compact('supportedNetworks'));
    }

    public function store(Request $request)
    {
        $response = $this->service->createNetworkProccess($request);
        
        if ($response['success']) {
            return redirect()
                ->route('admin.networks.index')
                ->with('success', $response['message']);
        }

        return back()
            ->withInput()
            ->with('error', $response['message']);
    }

    public function edit($id)
    {
        $network = Network::findOrFail($id);
        $supportedNetworks = SupportedNetwork::whereSlug($network->slug)->get();
        
        return view('admin.network.create', compact('network', 'supportedNetworks'));
    }

    public function update(Request $request, $id)
    {
        $request->merge(['id' => $id]); // Add the ID to the request
        $response = $this->service->createNetworkProccess($request);
        
        if ($response['success']) {
            return redirect()
                ->route('admin.networks.index')
                ->with('success', $response['message']);
        }

        return back()
            ->withInput()
            ->with('error', $response['message']);
    }

    public function toggleStatus(Request $request)
    {
        $network = Network::findOrFail($request->id);
        $network->status = !$network->status;
        $network->save();

        return response()->json([
            'success' => true,
            'message' => 'وضعیت شبکه با موفقیت تغییر کرد'
        ]);
    }

    public function delete($id)
    {
        $response = $this->service->deleteNetwork($id);
        
        if ($response['success']) {
            return redirect()
                ->route('admin.networks.index')
                ->with('success', $response['message']);
        }

        return back()->with('error', $response['message']);
    }

    public function checkCurrentBlock(Request $request)
    {
        $response = $this->service->checkLatestBlock($request);
        return response()->json($response);
    }
}
