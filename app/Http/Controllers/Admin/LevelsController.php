<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Level;
use App\Models\LevelTypeValue;
use Illuminate\Http\Request;
use App\Services\Admin\LevelService;
use App\Http\Requests\Admin\StoreLevelRequest;

class LevelsController extends Controller
{

    public function __construct(
        protected LevelService $service
    ){

    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return $this->service->levelsList($request);

    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreLevelRequest $request)
    {
        return $this->service->storeLevels($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
