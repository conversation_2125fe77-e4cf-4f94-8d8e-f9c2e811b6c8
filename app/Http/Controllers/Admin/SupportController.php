<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Support\StoreSupportRequest;
use App\Http\Requests\Admin\Support\UpdateSupportRequest;
use App\Services\Admin\SupportService;
use Illuminate\Http\Request;

class SupportController extends Controller
{
    public function __construct(
        protected SupportService $service,
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return $this->service->allTickets($request);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function create($userId = null)
    {
        return $this->service->newTicket($userId);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSupportRequest $request)
    {
        return $this->service->createTicket($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->service->getSupportRequest($id);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSupportRequest $request, string $id)
    {
        return $this->service->updateTicket($id, $request->validated());
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        return $this->service->deleteTicket($id);
    }
}
