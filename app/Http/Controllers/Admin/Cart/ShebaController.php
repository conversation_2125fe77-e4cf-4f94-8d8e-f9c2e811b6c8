<?php

namespace App\Http\Controllers\Admin\Cart;

use App\Http\Controllers\Controller;
use App\Models\Card;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\ValidationException;

class She<PERSON>Controller extends Controller
{
    public function update($id, Request $request)
    {
        $card = Card::findOrFail($id);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.env('ZIBAL_API'),
            ])->post('https://api.zibal.ir/v1/facility/cardToIban/', [
                'cardNumber' => $card->number,
            ]);

            $response = json_decode($response->body(), true);

            if ($response['result'] == 1) {
                $iban = $response['data']['IBAN'];
                $card->update([
                    'iban' => $iban,
                    'sheba' => $iban,
                ]);

                if ($request->wantsJson()) {
                    return $response;
                }

                return redirect()->route('admin.card.show', $id)
                    ->with('success', 'شماره شبا با موفقیت دریافت شد');
            } else {
                if ($request->wantsJson()) {
                    throw ValidationException::withMessages([$response['message']]);
                }

                return redirect()->route('admin.card.show', $id)
                    ->with('error', 'خطا در دریافت شماره شبا: ' . ($response['message'] ?? 'خطای نامشخص'));
            }
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                throw ValidationException::withMessages([$e->getMessage()]);
            }

            return redirect()->route('admin.card.show', $id)
                ->with('error', 'خطا در ارتباط با سرویس استعلام شبا');
        }
    }
}
