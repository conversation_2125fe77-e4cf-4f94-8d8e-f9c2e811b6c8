<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TomanWithdrawal;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TomanWithdrawalController extends Controller
{
    /**
     * Display a listing of pending withdrawals.
     */
    public function pending()
    {
        $withdrawals = TomanWithdrawal::with(['user', 'card.bank'])
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.toman-withdrawals.pending', compact('withdrawals'));
    }

    /**
     * Display a listing of approved withdrawals.
     */
    public function approved()
    {
        $withdrawals = TomanWithdrawal::with(['user', 'card.bank', 'admin'])
            ->where('status', 'approved')
            ->orderBy('processed_at', 'desc')
            ->paginate(15);

        return view('admin.toman-withdrawals.approved', compact('withdrawals'));
    }

    /**
     * Display a listing of rejected withdrawals.
     */
    public function rejected()
    {
        $withdrawals = Toman<PERSON>ithdrawal::with(['user', 'card.bank', 'admin'])
            ->where('status', 'rejected')
            ->orderBy('processed_at', 'desc')
            ->paginate(15);

        return view('admin.toman-withdrawals.rejected', compact('withdrawals'));
    }

    /**
     * Show operation page for a withdrawal request.
     */
    public function operation($id)
    {
        $withdrawal = TomanWithdrawal::with(['user', 'card.bank'])->findOrFail($id);

        if ($withdrawal->status !== 'pending') {
            return redirect()->route('admin.toman-withdrawal.pending')
                ->with('error', 'این درخواست قبلاً پردازش شده است');
        }

        return view('admin.toman-withdrawals.operation', compact('withdrawal'));
    }

    /**
     * Approve a withdrawal request.
     */
    public function approve(Request $request, $id)
    {
        $request->validate([
            'tracking_number' => 'required|string|max:255',
        ]);

        DB::beginTransaction();
        try {
            $withdrawal = TomanWithdrawal::findOrFail($id);

            if ($withdrawal->status !== 'pending') {
                return redirect()->back()->with('error', 'این درخواست قبلاً پردازش شده است');
            }

            // Update withdrawal status
            $withdrawal->status = 'approved';
            $withdrawal->admin_id = Auth::id();
            $withdrawal->processed_at = now();
            $withdrawal->tracking_number = $request->tracking_number;
            $withdrawal->save();

            // Update related transaction
            $transaction = $withdrawal->user->transactions()
                ->where('type', 'withdraw')
                ->where('amount', $withdrawal->amount)
                ->where('card_id', $withdrawal->card_id)
                ->where('status', 'pending')
                ->first();

            if ($transaction) {
                $transaction->status = 'done';
                $transaction->save();
            }

            DB::commit();
            return redirect()->route('admin.toman-withdrawal.pending')
                ->with('success', 'درخواست برداشت با موفقیت تایید شد');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'خطا در پردازش درخواست: ' . $e->getMessage());
        }
    }

    /**
     * Show reject form for a withdrawal request.
     */
    public function showRejectForm($id)
    {
        $withdrawal = TomanWithdrawal::with(['user', 'card.bank'])->findOrFail($id);

        if ($withdrawal->status !== 'pending') {
            return redirect()->route('admin.toman-withdrawal.pending')
                ->with('error', 'این درخواست قبلاً پردازش شده است');
        }

        return view('admin.toman-withdrawals.reject', compact('withdrawal'));
    }

    /**
     * Reject a withdrawal request.
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reject_reason' => 'required|string|max:255',
        ]);

        DB::beginTransaction();
        try {
            $withdrawal = TomanWithdrawal::findOrFail($id);

            if ($withdrawal->status !== 'pending') {
                return redirect()->back()->with('error', 'این درخواست قبلاً پردازش شده است');
            }

            // Update withdrawal status
            $withdrawal->status = 'rejected';
            $withdrawal->reject_reason = $request->reject_reason;
            $withdrawal->admin_id = Auth::id();
            $withdrawal->processed_at = now();
            $withdrawal->save();

            // Return funds to user's toman balance
            $user = User::findOrFail($withdrawal->user_id);
            $user->toman_balance += ($withdrawal->amount + $withdrawal->fee);
            $user->save();

            // Update related transaction
            $transaction = $withdrawal->user->transactions()
                ->where('type', 'withdraw')
                ->where('amount', $withdrawal->amount)
                ->where('card_id', $withdrawal->card_id)
                ->where('status', 'pending')
                ->first();

            if ($transaction) {
                $transaction->status = 'declined';
                $transaction->save();
            }

            DB::commit();
            return redirect()->route('admin.toman-withdrawal.pending')->with('success', 'درخواست برداشت با موفقیت رد شد');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'خطا در پردازش درخواست: ' . $e->getMessage());
        }
    }

    /**
     * Show details of a withdrawal request.
     */
    public function show($id)
    {
        $withdrawal = TomanWithdrawal::with(['user', 'card.bank', 'admin'])->findOrFail($id);

        return view('admin.toman-withdrawals.show', compact('withdrawal'));
    }
}
