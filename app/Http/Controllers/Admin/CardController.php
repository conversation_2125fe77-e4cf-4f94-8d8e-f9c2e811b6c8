<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Card\UpdateCardRequest;
use App\Models\Bank;
use App\Models\Card;
use App\Services\Admin\CardService;
use Illuminate\Http\Request;

class CardController extends Controller
{
    public function __construct(
        protected CardService $service
    ) {}

    public function index(Request $request)
    {
        $banks = Bank::all();
        $cards = $this->service->listCards($request);

        return view('admin.card.index', compact('cards', 'banks'));
    }

    public function show($id)
    {
        $card = $this->service->showCard($id);
        $userCards = Card::where('user_id', $card->user_id)->with('bank', 'user')->get();

        return view('admin.card.show', compact('card', 'userCards'));
    }

    public function update($id, UpdateCardRequest $request)
    {
        $result = $this->service->updateCard($id, $request->validated());

        if ($request->wantsJson()) {
            return $result;
        }

        return redirect()->route('admin.card.show', $id)
            ->with('success', 'کارت بانکی با موفقیت بروزرسانی شد');
    }

    public function destroy($id)
    {
        $this->service->deleteCard($id);

        if (request()->wantsJson()) {
            return [];
        }

        return redirect()->route('admin.card.index')
            ->with('success', 'کارت بانکی با موفقیت حذف شد');
    }
}
