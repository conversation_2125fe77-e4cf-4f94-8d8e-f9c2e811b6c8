<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\Transaction;
use App\Rules\User\TransactionRule;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

use function App\authWallet;
use function App\transaction;

class TradeController extends Controller
{
    public function buy(Request $request)
    {
        $request->validate([
            'currency_id' => ['required', 'exists:currencies,id'],
            'type' => 'required|in:deposit,withdraw,gift,transfer,buy,sell',
            'amount' => ['required', 'numeric', new TransactionRule($request->amount, $request->type)],
        ]);
        $currency = Currency::find($request->currency_id);
        $price = $request->amount * $currency->buy;
        $wallet = authWallet();
        if (isset($wallet) && $wallet->credit($price)) {
            try {
                $data = $currency->provider()->buy($request->all());

                return transaction('buy', $price, $wallet, details: $data);
            } catch (Exception $e) {
                $wallet->debit($price);
                throw ValidationException::withMessages(['خطایی رخ داد: '.$e->getMessage()]);
            }
        } else {
            throw ValidationException::withMessages(['اعتبار کیف پول شما کافی نیست.']);
        }
    }

    public function sell(Request $request): Transaction
    {
        $request->validate([
            'currency_id' => ['required', 'exists:currencies,id'],
            'type' => 'required|in:deposit,withdraw,gift,transfer,buy,sell',
            'amount' => ['required', new TransactionRule($request->amount, $request->type)],
        ]);
        $currency = Currency::find($request->currency_id);
        $wallet = authWallet();
        try {
            $data = $currency->provider()->sell($request->all());
            $price = $data['amount'] * $currency->sell;
            $wallet->debit($price);

            return transaction('sell', $price, $wallet, details: $data);
        } catch (Exception $e) {
            throw ValidationException::withMessages([$e->getMessage()]);
        }
    }
}
