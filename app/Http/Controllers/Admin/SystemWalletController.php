<?php

namespace App\Http\Controllers\Admin;

use App\Models\Network;
use App\Models\AdminWalletKey;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\SystemWalletService;
use App\Http\Requests\Admin\CreateSystemWalletRequest;
use Illuminate\Support\Facades\Log;

class SystemWalletController extends Controller
{
    public function __construct(
        protected SystemWalletService $service
    ) {}

    public function index(Request $request)
    {
        $wallets = AdminWalletKey::with('network')->paginate(15);
        return view('admin.system_wallet.index', compact('wallets'));
    }

    public function create()
    {
        $networks = Network::where('status', 1)
            ->whereDoesntHave('admin_wallet')
            ->get();
        return view('admin.system_wallet.create', compact('networks'));
    }

    public function store(CreateSystemWalletRequest $request)
    {
        try {
            $result = $this->service->createSystemWalletProccess($request);
            
            if ($result['success']) {
                return redirect()
                    ->route('admin.system-wallets.index')
                    ->with('success', $result['message']);
            }

            return redirect()
                ->back()
                ->withInput()
                ->with('error', $result['message']);

        } catch (\Exception $e) {
            Log::error('Wallet creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'خطا در ایجاد کیف پول: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        $wallet = AdminWalletKey::findOrFail($id);
        $networks = Network::where('status', 1)->get();
        return view('admin.system_wallet.edit', compact('wallet', 'networks'));
    }

    public function update(Request $request, $id)
    {
        $result = $this->service->updateWallet($id, $request->validated());
        
        if ($result['success']) {
            return redirect()
                ->route('admin.system-wallets.index')
                ->with('success', 'کیف پول سیستمی با موفقیت بروزرسانی شد');
        }

        return back()->with('error', $result['message']);
    }

    public function toggleStatus(Request $request)
    {
        $wallet = AdminWalletKey::findOrFail($request->id);
        $wallet->status = !$wallet->status;
        $wallet->save();

        return response()->json([
            'success' => true,
            'message' => 'وضعیت کیف پول با موفقیت تغییر کرد'
        ]);
    }
}
