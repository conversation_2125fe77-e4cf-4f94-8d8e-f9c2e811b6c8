<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TomanDepositController extends Controller
{
    /**
     * Display a listing of pending deposits.
     */
    public function pending()
    {
        $deposits = Transaction::with(['user', 'currency'])
            ->where('type', 'deposit')
            ->where('currency_id', 6) // Toman/IRR currency ID
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.toman-deposits.pending', compact('deposits'));
    }

    /**
     * Display a listing of approved deposits.
     */
    public function approved()
    {
        $deposits = Transaction::with(['user', 'currency'])
            ->where('type', 'deposit')
            ->where('currency_id', 6) // Toman/IRR currency ID
            ->where('status', 'done')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.toman-deposits.approved', compact('deposits'));
    }

    /**
     * Display a listing of rejected deposits.
     */
    public function rejected()
    {
        $deposits = Transaction::with(['user', 'currency'])
            ->where('type', 'deposit')
            ->where('currency_id', 6) // Toman/IRR currency ID
            ->where('status', 'rejected')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.toman-deposits.rejected', compact('deposits'));
    }

    /**
     * Show operation page for a deposit request.
     */
    public function operation($id)
    {
        $deposit = Transaction::with(['user', 'currency'])->findOrFail($id);

        if ($deposit->status !== 'pending') {
            return redirect()->route('admin.toman-deposit.pending')
                ->with('error', 'این درخواست قبلاً پردازش شده است');
        }

        return view('admin.toman-deposits.operation', compact('deposit'));
    }

    /**
     * Approve a deposit request.
     */
    public function approve(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $deposit = Transaction::findOrFail($id);

            if ($deposit->status !== 'pending') {
                return redirect()->back()->with('error', 'این درخواست قبلاً پردازش شده است');
            }

            // Update deposit status
            $deposit->status = 'done';
            $deposit->admin_id = Auth::id();
            $deposit->processed_at = now();
            $deposit->save();

            DB::commit();
            return redirect()->route('admin.toman-deposit.pending')
                ->with('success', 'درخواست واریز با موفقیت تایید شد');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'خطا در پردازش درخواست: ' . $e->getMessage());
        }
    }

    /**
     * Show reject form for a deposit request.
     */
    public function showRejectForm($id)
    {
        $deposit = Transaction::with(['user', 'currency'])->findOrFail($id);

        if ($deposit->status !== 'pending') {
            return redirect()->route('admin.toman-deposit.pending')
                ->with('error', 'این درخواست قبلاً پردازش شده است');
        }

        return view('admin.toman-deposits.reject', compact('deposit'));
    }

    /**
     * Reject a deposit request.
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'reject_reason' => 'required|string|max:255',
        ]);

        DB::beginTransaction();
        try {
            $deposit = Transaction::findOrFail($id);

            if ($deposit->status !== 'pending') {
                return redirect()->back()->with('error', 'این درخواست قبلاً پردازش شده است');
            }

            $user = User::findOrFail($deposit->user_id);

            // Revert the balance change
            $user->toman_balance -= $deposit->amount;
            $user->save();

            // Update deposit status
            $deposit->status = 'rejected';
            $deposit->admin_id = Auth::id();
            $deposit->processed_at = now();
            $deposit->reject_reason = $request->reject_reason;
            $deposit->save();

            DB::commit();
            return redirect()->route('admin.toman-deposit.pending')->with('success', 'درخواست واریز با موفقیت رد شد');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'خطا در پردازش درخواست: ' . $e->getMessage());
        }
    }

    /**
     * Show details of a deposit request.
     */
    public function show($id)
    {
        $deposit = Transaction::with(['user', 'currency'])->findOrFail($id);

        return view('admin.toman-deposits.show', compact('deposit'));
    }
}
