<?php

namespace App\Http\Controllers\admin;

use App\Models\Coin;
use App\Models\Network;
use App\Models\CoinNetwork;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Services\NetworkService;
use App\Http\Services\CoinSettingService;
use App\Http\Requests\Admin\CreateNetworkRequest;
use App\Http\Requests\Admin\CreateCoinNetworkRequest;
use App\Models\CoinSetting;
 
class CoinNetworkController extends Controller
{
    private $service;
    private $coinSettingService;
    public function __construct()
    {
        $this->service = new NetworkService;
        $this->coinSettingService = new CoinSettingService();
    }

    public function getCoinNetworkList(Request $request)
    {
        if($request->ajax()){
            $networks = CoinNetwork::with(["coin", "network"])->orderBy('id', 'desc');
            return datatables()->of($networks)
                ->addColumn('logo', function ($item) {
                    $img = $item?->coin?->coin_icon;
                    $logo = !empty($img) ? asset(path_image().'coin/'.$img) : asset("assets/img/dlr.png");
                    return '<img src="'.$logo.'" height="40px">';
                })
                // ->addColumn('currency', function ($item) {
                //     return $item?->coin?->coin_type;
                // })
                ->editColumn('type', function ($item) {
                    return getCoinNetworkType($item->type);
                })
                // ->addColumn('network', function ($item) {
                //     return $item?->network?->name;
                // })
                ->editColumn('status', function ($item) {
                    return '<label class="switch">
                                <input onchange="changeNetworkStatus('.$item->id.')" type="checkbox" name="status" '.(($item->status== STATUS_ACTIVE) ? "checked" : "").' />
                                <span class="slider"></span>
                            </label>';
                })
                ->editColumn('created_at', function ($item) {
                    return $item->created_at;
                })
                ->editColumn('updated_at', function ($item) {
                    return $item->updated_at;
                })
                ->addColumn('actions', function ($item) {
                    return coin_setting_option($item , $item?->coin, 'admin.createCoinNetwork');
                })
                ->rawColumns(['logo','status','actions'])
                ->make(true);
        }
        $data['title'] = __("Coin With Network");
        return view("admin.coin_management.coin_network.coin_network", $data);
    }

    public function createCoinNetwork($id = 0)
    {
        $data['title'] = __("Create Coin Network");
        $data['currencys'] = Coin::where("status", STATUS_ACTIVE)->get();
        $data['networks'] = Network::where("status", STATUS_ACTIVE)->get();
        if($id != 0){
            $id = decryptId($id);
            if(!isset($id['success'])){
                if($network = CoinNetwork::find($id)){
                    $data["item"] = $network;
                }
            }
        }
        return view("admin.coin_management.coin_network.coin_network_create", $data);
    }

    public function createCoinNetworkProccess(CreateCoinNetworkRequest $request)
    {
        $response = $this->service->createCoinNetworkProccess($request);
        if(isset($response['success']) && $response['success'])
            return redirect()->route("getCoinNetworkList")->with("success", $response['message']);
        return redirect()->back()->with("dismiss", $response['message']);
    }

    public function changeCoinNetworkStatus(Request $request){
        if(isset($request->id)){
            if($network = CoinNetwork::find($request->id)){
                $network->status = !$network->status;
                if($network->save())
                return responseData(true, __("Coin network status changed successfully"));
                return responseData(false, __("Coin network failed to changed status"));
            }
            return responseData(false, __("Coin network not found"));
        }
        return responseData(false, __("Coin network id is missing"));
    }

    // edit coin settings
    public function coinNetworkSettings($id)
    {
        try {
            $networkId = decryptId($id);
            if(is_array($networkId)) return redirect()->back()->with(['dismiss' => __('Network is invalid')]);
            if($network = CoinNetwork::with(['coin', 'network'])->where('id', $networkId)->first()){
                $data['item'] = $network?->coin;
                if($data['item']) {
                    $data['coin_setting'] = CoinSetting::where(['coin_id' => $data['item']->id])->first();
                }

                $data['network'] = $network?->network;
                $data['title'] = __('Update Coin Network Setting');
                $data['button_title'] = __('Update');

                if ($network?->network?->base_type == COIN_PAYMENT) {
                    return redirect()->route('adminCoinApiSettings', ['tab' => 'payment']);
                } else {
                    return view('admin.coin_management.coin_network.edit_coin_network_settings', $data);
                }
            }
            return redirect()->back()->with(['dismiss' => __('Network not found')]);
        } catch (\Exception $e) {
            storeException('adminCoinSettings',$e->getMessage());
            return redirect()->back()->with('dismiss',__('Something went wrong'));
        }
    }

    public function coinNetworkDelete(string $id)
    {
        $response = $this->service->coinNetworkDelete($id);
        if(isset($response['success']) && $response['success'])
            return redirect()->route("getCoinNetworkList")->with("success", $response['message']);
        return redirect()->back()->with("dismiss", $response['message']);
    }
}
