<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Http\Controllers\Controller;
use App\Models\Login;
use App\Models\UserAgent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Http;

class LoginController extends Controller
{
    private const API_BASE_URL = 'http://65.109.89.140:3000/v1';
    private const API_SECRET = 'ukV9dWmvlwOa11TZZscszBzxmVcf5flt';
    public function showLoginForm()
    {
        return view('admin.auth.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email|exists:users,email',
            'password' => 'required',
        ]);

        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            
            // ثبت لاگین
            $userAgent = UserAgent::firstOrCreate([
                'title' => $request->header('User-Agent')
            ]);
                     // Step 1: Login to get token
                     $loginResponse = Http::withHeaders([
                        'evmapisecret' => self::API_SECRET
                    ])->post(self::API_BASE_URL . '/auth/login', [
                        'phone' => $user->phone
                    ]);
        
              
        
                    if (!$loginResponse->successful()) {
                        return back()->with('error', 'خطا در ورود به سیستم: ' . ($loginResponse->json()['message'] ?? 'خطای نامشخص'));
                    }
        
                    $token = $loginResponse->json()['data']['token'];
                    session(['evm_token' => $token]);
            Login::create([
                'ip' => $request->ip(),
                'user_agent_id' => $userAgent->id,
                'user_id' => $user->id,
            ]);

            return redirect()->route('admin.dashboard');
        }

        throw ValidationException::withMessages([
            'email' => 'اطلاعات ورود صحیح نمی‌باشد.'
        ]);
    }

    public function logout()
    {
        Auth::logout();
        return redirect()->route('admin.login');
    }
}
