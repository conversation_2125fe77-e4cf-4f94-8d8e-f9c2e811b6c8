<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Support;
use App\Services\Admin\SupportService;
use Illuminate\Http\Request;

class TicketController extends Controller
{
    public function __construct(
        protected SupportService $service
    ) {}

    public function show($id)
    {
        $ticket = Support::with([
            'tickets',
            'user.roles',
            'user.transactions',
            'user.tickets',
            'unit',
            'level'
        ])->findOrFail($id);
        
        return view('admin.tickets.show', compact('ticket'));
    }

    public function reply(Request $request, $id)
    {
        $request->validate([
            'message' => 'required|string'
        ]);

        $ticket = Support::findOrFail($id);
        
        $ticket->update([
            'status' => 'admin_response'
        ]);

        $ticket->tickets()->create([
            'message' => $request->message,
            'user_id' => auth()->id(),
        ]);

        return back()->with('success', 'پاسخ شما با موفقیت ثبت شد.');
    }
}
