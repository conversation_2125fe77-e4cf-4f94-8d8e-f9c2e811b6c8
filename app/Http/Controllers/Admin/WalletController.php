<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class WalletController extends Controller
{
    private const API_BASE_URL = 'http://65.109.89.140:3000/v1';
    private const API_SECRET = 'ukV9dWmvlwOa11TZZscszBzxmVcf5flt';

    public function createWallet(Request $request, User $user)
    {
        $request->validate([
            'coin_type' => 'required|string',
            'network_id' => 'required|numeric'
        ]);

        try {
            Log::info('Creating wallet for user', [
                'user_id' => $user->id,
                'coin_type' => $request->coin_type,
                'network_id' => $request->network_id
            ]);

            // Step 1: Login to get token
            $loginResponse = Http::withHeaders([
                'evmapisecret' => self::API_SECRET
            ])->post(self::API_BASE_URL . '/auth/login', [
                'phone' => $user->phone
            ]);

            Log::info('Login response', ['response' => $loginResponse->json()]);

            if (!$loginResponse->successful()) {
                Log::error('Login failed', ['response' => $loginResponse->json()]);
                return back()->with('error', 'خطا در ورود به سیستم: ' . ($loginResponse->json()['message'] ?? 'خطای نامشخص'));
            }

            $token = $loginResponse->json()['data']['token'];

            // Step 2: Get wallet address
            $walletResponse = Http::withHeaders([
                'evmapisecret' => self::API_SECRET,
                'token' => $token
            ])->post(self::API_BASE_URL . '/evm/create-wallet', [
                'coin_type' => $request->coin_type,
                'network' => $request->network_id
            ]);

            Log::info('Wallet creation response', ['response' => $walletResponse->json()]);

            if (!$walletResponse->successful()) {
                Log::error('Wallet creation failed', ['response' => $walletResponse->json()]);
                return back()->with('error', 'خطا در ایجاد کیف پول: ' . ($walletResponse->json()['message'] ?? 'خطای نامشخص'));
            }

            // اگر آدرس کیف پول در پاسخ وجود دارد، آن را نمایش دهیم
            $walletAddress = $walletResponse->json()['data']['address'] ?? null;
            $successMessage = 'کیف پول با موفقیت ایجاد شد';
            if ($walletAddress) {
                $successMessage .= " - آدرس: $walletAddress";
            }

            return back()->with('success', $successMessage);

        } catch (\Exception $e) {
            Log::error('Wallet creation exception', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return back()->with('error', 'خطا در ایجاد کیف پول: ' . $e->getMessage());
        }
    }
    
    public function adjustBalance(Request $request, Wallet $wallet)
    {
        $request->validate([
            'operation' => 'required|in:add,subtract,set',
            'amount' => 'required|numeric|min:0',
            'description' => 'nullable|string|max:255'
        ]);

        try {
            DB::beginTransaction();

            $oldBalance = $wallet->balance;
            $amount = $request->amount;
            
            switch ($request->operation) {
                case 'add':
                    $wallet->balance += $amount;
                    $type = 'deposit';
                    break;
                
                case 'subtract':
                    if ($wallet->balance < $amount) {
                        throw new \Exception('موجودی کافی نیست');
                    }
                    $wallet->balance -= $amount;
                    $type = 'withdraw';
                    break;
                
                case 'set':
                    $wallet->balance = $amount;
                    $type = 'adjustment';
                    break;
            }

            $wallet->save();

            // ثبت تراکنش
            Transaction::create([
                'type' => $type,
                'amount' => $amount,
                'price' => null,
                'wallet_id' => $wallet->id,
                'wallet_address' => null,
                'currency_id' => $wallet->coin_id, // Get coin_id from wallet
                'user_id' => $wallet->user_id,
                'registrar' => auth()->id(),
                'network' => null,
                'status' => 'done',
                'description' => $request->description ?? "تنظیم موجودی توسط ادمین",
                'details' => json_encode([
                    'adjustment_type' => $request->operation,
                    'adjusted_by_admin' => true
                ]),
                'balance_before' => $oldBalance,
                'balance_after' => $wallet->balance
            ]);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'موجودی با موفقیت بروزرسانی شد'
                ]);
            }

            return back()->with('success', 'موجودی با موفقیت بروزرسانی شد');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'خطا در بروزرسانی موجودی: ' . $e->getMessage()
                ]);
            }

            return back()->with('error', 'خطا در بروزرسانی موجودی: ' . $e->getMessage());
        }
    }
}
