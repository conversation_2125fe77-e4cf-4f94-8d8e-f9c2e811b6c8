<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UsdPrice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UsdPriceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $usdPrices = UsdPrice::latest()->paginate(10);
        return view('admin.usd-prices.index', compact('usdPrices'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.usd-prices.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'buy_price' => 'required|numeric|min:1',
            'sell_price' => 'required|numeric|min:1',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // If setting a new price as active, deactivate all other prices
        if ($request->has('is_active') && $request->is_active) {
            UsdPrice::where('is_active', true)->update(['is_active' => false]);
        }

        UsdPrice::create([
            'buy_price' => $request->buy_price,
            'sell_price' => $request->sell_price,
            'is_active' => $request->has('is_active') ? true : false,
        ]);

        return redirect()->route('admin.usd-prices.index')
            ->with('success', 'قیمت دلار با موفقیت ثبت شد');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(UsdPrice $usdPrice)
    {
        return view('admin.usd-prices.edit', compact('usdPrice'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, UsdPrice $usdPrice)
    {
        $validator = Validator::make($request->all(), [
            'buy_price' => 'required|numeric|min:1',
            'sell_price' => 'required|numeric|min:1',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // If setting this price as active, deactivate all other prices
        if ($request->has('is_active') && $request->is_active && !$usdPrice->is_active) {
            UsdPrice::where('is_active', true)->update(['is_active' => false]);
        }

        $usdPrice->update([
            'buy_price' => $request->buy_price,
            'sell_price' => $request->sell_price,
            'is_active' => $request->has('is_active') ? true : false,
        ]);

        return redirect()->route('admin.usd-prices.index')
            ->with('success', 'قیمت دلار با موفقیت بروزرسانی شد');
    }

    /**
     * Set a price as active and deactivate others.
     */
    public function setActive(UsdPrice $usdPrice)
    {
        // Deactivate all prices
        UsdPrice::where('is_active', true)->update(['is_active' => false]);
        
        // Activate the selected price
        $usdPrice->update(['is_active' => true]);
        
        return redirect()->route('admin.usd-prices.index')
            ->with('success', 'قیمت دلار فعال با موفقیت تغییر کرد');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(UsdPrice $usdPrice)
    {
        // Don't allow deleting the active price
        if ($usdPrice->is_active) {
            return redirect()->route('admin.usd-prices.index')
                ->with('error', 'قیمت دلار فعال را نمی‌توان حذف کرد');
        }
        
        $usdPrice->delete();
        
        return redirect()->route('admin.usd-prices.index')
            ->with('success', 'قیمت دلار با موفقیت حذف شد');
    }
}
