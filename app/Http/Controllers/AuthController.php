<?php

namespace App\Http\Controllers;

use App\Http\Requests\Auth\VerifyRequest;
use App\Jobs\ByBit\CreateSubUIDJob;
// use App\Models\ByBitSubUser;
use App\Jobs\VerifyUserProfile;
use App\Models\Login;
use App\Models\ModelHasRole;
use App\Models\Otp;
use App\Models\Role;
use App\Models\User;
use App\Models\UserAgent;
use Cryptommer\Smsir\Classes\Smsir;
use Cryptommer\Smsir\Objects\Parameters;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use Throwable;
use Illuminate\Support\Facades\Validator;
// use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    public function __construct(
    ) {}

    /**
     * @throws Throwable
     */

    public function sendOtp(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => ['required', 'regex:/^9\d{9}$/'], // شماره بدون صفر اول
        ]);

        if ($validator->fails()) {
            throw ValidationException::withMessages($validator->errors()->toArray());
        }

        $phone = $request->phone;

        // بررسی ارسال مجدد OTP در کمتر از 2 دقیقه
        $recentOtp = Otp::where('phone', $phone)
            ->where('expires_at', '>', now())
            ->first();

        if ($recentOtp) {
            throw ValidationException::withMessages([
                'message' => 'تا دو دقیقه نمی‌توانید کد جدید دریافت کنید.'
            ]);
        }

        // تولید کد تصادفی
        $code = (string) random_int(100000, 999999);

        // ذخیره در دیتابیس
        Otp::create([
            'phone' => $phone,
            'code' => $code,
            'expires_at' => now()->addMinutes(2),
        ]);
        $userExists = User::where('phone', $phone)->exists();
       // dd($userExists);
        // ارسال پیامک (شماره را با ۰۹ بفرستید چون اپراتور نیاز دارد)
        $sms = new Smsir;
        $sms->Send()->Verify($phone, env('SMSIR_TEMPLATE'), [new Parameters('CODE', $code)]);

        return response()->json([
            'message' => 'کد تأیید ارسال شد.',
            'is_new_user' => $userExists,
        ]);
    }
    public function verifyOtp(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'phone' => ['required', 'regex:/^9\d{9}$/'], // شماره بدون صفر اول
            'otp' => ['required', 'digits:6'],
        ]);

        if ($validator->fails()) {
            throw ValidationException::withMessages($validator->errors()->toArray());
        }

        $phone = $request->phone;
        $otp = $request->otp;

        // دریافت OTP از دیتابیس
        $otpRecord = Otp::where('phone', $phone)
            ->where('code', $otp)
            ->first();

        if (!$otpRecord) {
            throw ValidationException::withMessages([
                'message' => 'کد وارد شده نامعتبر است.'
            ]);
        }

        // یافتن یا ایجاد کاربر
        $user = User::where('phone', $phone)->first();

        if (!$user) {
            $validator = Validator::make($request->all(), [
                'firstname' => ['required', 'string', 'max:255'],
                'lastname' => ['required', 'string', 'max:255'],
                'national_id' => ['required', 'string', 'size:10'],
                'gender' => ['required', 'in:male,female'],
            ]);

            if ($validator->fails()) {
                throw ValidationException::withMessages($validator->errors()->toArray());
            }

            $user = User::create([
                'phone' => $phone,
                'firstname' => $request->firstname,
                'lastname' => $request->lastname,
                'national_id' => $request->national_id,
                'gender' => $request->gender,
                'phone_verified_at' => now(),
            ]);
            VerifyUserProfile::dispatch($user->id);
            // ایجاد کیف پول برای کاربر جدید
            create_coin_wallet($user->id);
        } else {
            $user->touch('phone_verified_at');
        }
        $ip = $request->ip();
        $userAgentTitle = $request->header('User-Agent');
        $userAgent = UserAgent::firstOrCreate(['title' => $userAgentTitle]);
        Login::create([
            'ip' => $ip,
            'user_agent_id' => $userAgent->id,
            'user_id' => $user->id,
        ]);
            $otpRecord->delete();
        return response()->json([
            'message' => 'کد تأیید شد.',
            'token' => $user->createToken("phone.$phone")->plainTextToken,
            'user' => $user,
        ]);
    }
    public function login(Request $request): array
    {
        $data = $request->validate([
            'email' => 'required|exists:users,email',
            'password' => 'required',
        ]);
        throw_unless(Auth::attempt($data), ValidationException::withMessages(['نام کاربری یا کلمه عبور وارد شده صحیح نمیباشد.']));
        $user = Auth::user();

        $ip = $request->ip();
        $userAgentTitle = $request->header('User-Agent');
        $userAgent = UserAgent::firstOrCreate(['title' => $userAgentTitle]);
        Login::create([
            'ip' => $ip,
            'user_agent_id' => $userAgent->id,
            'user_id' => $user->id,
        ]);

        return [
            'message' => 'logged in successfully',
            'token' => $user->createToken("phone.$request->phone")->plainTextToken,
            'user' => $user->id,
        ];
    }
}
