<?php

namespace App\Http\Controllers\Kucoin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Kucoin\StoreAccountApiRequest;
use App\Services\Kucoin\AccountApiService;
use Illuminate\Http\Request;

class AccountApiController extends Controller
{
    public function __construct(
        protected AccountApiService $service,
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // return $this->service->
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAccountApiRequest $request)
    {
        return $this->service->createSubAccountApi($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
