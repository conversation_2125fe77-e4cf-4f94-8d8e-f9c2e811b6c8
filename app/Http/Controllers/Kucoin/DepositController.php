<?php

namespace App\Http\Controllers\Kucoin;

use App\CurrencyProviders\KuCoinProvider;
use App\Http\Controllers\Controller;
use App\Http\Requests\Kucoin\StoreDepositRequest;
use App\Services\Kucoin\DepositService;
use Illuminate\Http\Request;

class DepositController extends Controller
{
    public function __construct(
        protected KuCoinProvider $provider,
        protected DepositService $service,
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return $this->provider->addresses(['USDT']);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDepositRequest $request)
    {
        // dd($request->validated());
        return $this->service->createDeposit($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->provider->address(['USDT', 'TRC20']);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
