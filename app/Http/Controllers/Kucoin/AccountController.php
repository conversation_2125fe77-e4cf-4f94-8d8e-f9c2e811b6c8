<?php

namespace App\Http\Controllers\Kucoin;

use App\CurrencyProviders\KuCoinProvider;
use App\Http\Controllers\Controller;
use App\Http\Requests\Kucoin\StoreSubAccountRequest;
use App\Services\Kucoin\AccountService;
use Illuminate\Http\Request;

class AccountController extends Controller
{
    public function __construct(
        protected KuCoinProvider $provider,
        protected AccountService $accountService,
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return $this->provider->getSubAccounts();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSubAccountRequest $request)
    {
        return $this->accountService->createAccount($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
