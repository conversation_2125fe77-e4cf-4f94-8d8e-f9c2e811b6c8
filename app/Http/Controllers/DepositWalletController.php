<?php

namespace App\Http\Controllers;

use App\Models\Coin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;

class DepositWalletController extends Controller
{
    private const API_BASE_URL = 'http://*************:3000/v1';
    private const API_SECRET = 'ukV9dWmvlwOa11TZZscszBzxmVcf5flt';

    public function getWalletAddress(Request $request)
    {
        $request->validate([
            'coin_id' => 'required|string',
            'network_id'=> 'required',
        ]);
        $coin = Coin::find($request->coin_id);
        try {
            // Step 1: Login to get token
            $loginResponse = Http::withHeaders([
                'evmapisecret' => self::API_SECRET
            ])->post(self::API_BASE_URL . '/auth/login', [
                'phone' => Auth::user()->phone
            ]);

            if (!$loginResponse->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Login failed',
                    'error' => $loginResponse->json()
                ], 400);
            }

            $token = $loginResponse->json()['data']['token'];

            // Step 2: Get wallet address
            $walletResponse = Http::withHeaders([
                'evmapisecret' => self::API_SECRET,
                'token' => $token
            ])->post(self::API_BASE_URL . '/evm/create-wallet', [
                'coin_type' => $coin->coin_type,
                'network' => $request->network_id
            ]);

            if (!$walletResponse->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to get wallet address',
                    'error' => $walletResponse->json()
                ], 400);
            }

            return response()->json($walletResponse->json());

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}