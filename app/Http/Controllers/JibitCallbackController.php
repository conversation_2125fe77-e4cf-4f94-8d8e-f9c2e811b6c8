<?php

namespace App\Http\Controllers;

use App\Services\JibitPaymentService;
use App\Models\JibitPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class JibitCallbackController extends Controller
{
    protected $jibitService;

    public function __construct(JibitPaymentService $jibitService)
    {
        $this->jibitService = $jibitService;
    }

    /**
     * Handle Jibit payment callback
     */
    public function handleCallback(Request $request)
    {
        try {
            Log::info('Jibit Callback Received', [
                'request_data' => $request->all(),
                'headers' => $request->headers->all(),
                'ip' => $request->ip()
            ]);

            // Get payment identifier from request
            $paymentIdentifier = $request->input('paymentIdentifier');
            
            if (!$paymentIdentifier) {
                Log::error('Jibit Callback: Missing payment identifier');
                return response()->json([
                    'success' => false,
                    'message' => 'شناسه پرداخت یافت نشد'
                ], 400);
            }

            // Find payment record
            $payment = JibitPayment::where('payment_identifier', $paymentIdentifier)->first();

            if (!$payment) {
                Log::error('Jibit Callback: Payment not found', [
                    'payment_identifier' => $paymentIdentifier
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'پرداخت یافت نشد'
                ], 404);
            }

            // Verify payment with Jibit API
            $verificationResult = $this->jibitService->verifyPayment($paymentIdentifier);

            if (!$verificationResult['success']) {
                Log::error('Jibit Callback: Verification failed', [
                    'payment_identifier' => $paymentIdentifier,
                    'verification_result' => $verificationResult
                ]);
                
                // Mark payment as failed
                $payment->markAsFailed($request->all());
                
                return response()->json([
                    'success' => false,
                    'message' => 'تایید پرداخت ناموفق بود'
                ], 400);
            }

            // Check if payment was successful
            $isSuccessful = $verificationResult['data']['is_successful'] ?? false;

            if ($isSuccessful) {
                Log::info('Jibit Callback: Payment successful', [
                    'payment_id' => $payment->id,
                    'user_id' => $payment->user_id,
                    'amount' => $payment->amount
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'پرداخت با موفقیت انجام شد'
                ]);
            } else {
                Log::warning('Jibit Callback: Payment not successful', [
                    'payment_identifier' => $paymentIdentifier,
                    'verification_result' => $verificationResult
                ]);

                // Mark payment as failed
                $payment->markAsFailed($request->all());

                return response()->json([
                    'success' => false,
                    'message' => 'پرداخت ناموفق بود'
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Jibit Callback Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'خطا در پردازش callback'
            ], 500);
        }
    }

    /**
     * Handle Jibit webhook notifications
     */
    public function handleWebhook(Request $request)
    {
        try {
            Log::info('Jibit Webhook Received', [
                'request_data' => $request->all(),
                'headers' => $request->headers->all(),
                'ip' => $request->ip()
            ]);

            // Validate webhook signature if needed
            // $this->validateWebhookSignature($request);

            $paymentIdentifier = $request->input('paymentIdentifier');
            $status = $request->input('status');

            if (!$paymentIdentifier) {
                return response()->json(['message' => 'Invalid webhook data'], 400);
            }

            $payment = JibitPayment::where('payment_identifier', $paymentIdentifier)->first();

            if (!$payment) {
                Log::warning('Jibit Webhook: Payment not found', [
                    'payment_identifier' => $paymentIdentifier
                ]);
                return response()->json(['message' => 'Payment not found'], 404);
            }

            // Update payment status based on webhook data
            switch (strtoupper($status)) {
                case 'SUCCESSFUL':
                    if ($payment->isPending()) {
                        // Verify payment before processing
                        $verificationResult = $this->jibitService->verifyPayment($paymentIdentifier);
                        if ($verificationResult['success'] && $verificationResult['data']['is_successful']) {
                            Log::info('Jibit Webhook: Payment processed successfully', [
                                'payment_id' => $payment->id
                            ]);
                        }
                    }
                    break;

                case 'FAILED':
                    $payment->markAsFailed($request->all());
                    Log::info('Jibit Webhook: Payment marked as failed', [
                        'payment_id' => $payment->id
                    ]);
                    break;

                case 'EXPIRED':
                    $payment->markAsExpired();
                    Log::info('Jibit Webhook: Payment marked as expired', [
                        'payment_id' => $payment->id
                    ]);
                    break;
            }

            return response()->json(['message' => 'Webhook processed successfully']);

        } catch (\Exception $e) {
            Log::error('Jibit Webhook Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json(['message' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Validate webhook signature (implement based on Jibit documentation)
     */
    protected function validateWebhookSignature(Request $request): bool
    {
        // Implement signature validation based on Jibit's webhook security requirements
        // This is a placeholder - implement according to Jibit's documentation
        
        $signature = $request->header('X-Jibit-Signature');
        $payload = $request->getContent();
        $secretKey = config('payment.drivers.jibit_pip.secret_key');

        // Example validation (adjust based on actual Jibit requirements)
        $expectedSignature = hash_hmac('sha256', $payload, $secretKey);
        
        return hash_equals($signature, $expectedSignature);
    }
}
