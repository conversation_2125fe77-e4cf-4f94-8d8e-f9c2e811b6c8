<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\NetworkAddressRequest;
use App\Http\Requests\Api\User\WalletRateRequest;
use App\Http\Requests\User\WithdrawalRequest;
use App\Http\Requests\CoinSwapRequest;
use App\Http\Services\Logger;
use App\Http\Services\TransService;
use App\Http\Services\WalletService;
use App\Http\Services\ProgressStatusService;
use App\Models\Coin;
use App\Models\Transaction;
use App\Models\UsdPrice;
use App\Models\Wallet;
use App\Models\WalletSwapHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;


class WalletController extends Controller
{
    public WalletService $service;
    public TransService $transService;
    public ProgressStatusService $progressService;

    public function __construct()
    {
        $this->service = new WalletService();
        $this->transService = new TransService();
        $this->progressService = new ProgressStatusService();
    }

    /**
     * wallet list
     * @return \Illuminate\Http\JsonResponse
     */
    public function walletList(Request $request)
    {
        $id = Auth::id();
        $coins = Coin::where('status', 1)
        ->where(function($query) {
            $query->where('is_withdrawal', 1)
                  ->orWhere('is_deposit', 1);
        })
        ->get();

    // Get user wallets with coin information
    $wallets = Wallet::where('user_id', $id)
        ->join('coins', 'wallets.coin_id', '=', 'coins.id')
        ->select(
            'wallets.*',
            'coins.coin_type',
            'coins.coin_icon',
            'coins.is_withdrawal',
            'coins.is_deposit'
        )
        ->get();
        $response = ['success' => true, 'message' => __('Data get'), 'data' => $coins];
        return response()->json($response);
    }

    /**
     * wallet deposit
     * @param $walletId
     * @return \Illuminate\Http\JsonResponse
     */
    public function walletDeposit($walletId)
    {
        try {
            $response = $this->service->userWalletDeposit(Auth::id(),$walletId);
        } catch (\Exception $e) {
            storeException('walletDeposit', $e->getMessage());
            $response = ['success' => false, 'message' => __('Something went wrong'), 'data' => []];
        }
        return response()->json($response);
    }

    /**
     * wallet withdrawal
     * @param $walletId
     * @return \Illuminate\Http\JsonResponse
     */
    public function walletWithdrawal($walletId)
    {
        try {
            $response = $this->service->userWalletWithdrawal(Auth::id(),$walletId);
        } catch (\Exception $e) {
            storeException('walletWithdrawal', $e->getMessage());
            $response = ['success' => false, 'message' => __('Something went wrong'), 'data' => []];
        }
        return response()->json($response);
    }

    /**
     * wallet withdrawal
     * @param WithdrawalRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function walletWithdrawalProcess(WithdrawalRequest $request)
    {
        try {
            $response = $this->transService->withdrawalProcess($request);
        } catch (\Exception $e) {
            storeException('walletWithdrawalProcess', $e->getMessage());
            $response = ['success' => false, 'message' => __('Something went wrong'), 'data' => []];
        }
        return response()->json($response);
    }

    /**
     * wallet history
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function walletHistoryApp(Request $request){
        $limit = $request->per_page ?? 5;
        $order_data['column_name'] = $request->column_name ?? '';
        $order_data['order_by'] = $request->order_by ?? '';
        $limit = $request->per_page ?? 5;
        $response = [];
        $data = [];
        if(isset($request->type) && ($request->type == 'deposit' || $request->type == 'withdraw')) {
            $data['type'] = $request->type;
            $data['sub_menu'] = $request->type;
            if ($request->type == 'deposit') {
                $data['title'] = __('Deposit History');
            } else {
                $data['title'] = __("Withdrawal History");
            }
            if ($request->type == 'deposit') {
                $data['histories'] = $this->transService->depositTransactionHistories(Auth::id(),null,null,null,null,$order_data)->paginate($limit);
            } else {
                $data['histories'] = $this->transService->withdrawTransactionHistories(Auth::id(),null,null,null,null,$order_data)->paginate($limit);
            }

            $data['progress_status_for_deposit'] = allsetting('progress_status_for_deposit');
            $data['progress_status_for_withdrawal'] = allsetting('progress_status_for_withdrawal');

            if($request->type == 'deposit' && allsetting('progress_status_for_deposit') == true)
            {
                $data['progress_status_list'] = $this->progressService->getProgressStatusActiveListBytype(PROGRESS_STATUS_TYPE_DEPOSIT)['data'];

            }else if($request->type == 'withdraw' && allsetting('progress_status_for_withdrawal') == STATUS_ACTIVE)
            {
                $data['progress_status_list'] = $this->progressService->getProgressStatusActiveListBytype(PROGRESS_STATUS_TYPE_WITHDRAWN)['data'];
            }

            $data['status'] = deposit_status();
            $response['success'] = true;
            $response['data'] = $data;
            $response['message'] = $data['title'];

        }else{
            $response['success'] = false;
            $response['data'] = [];
            $response['message'] = 'Something Went Wrong!';
        }
        return response()->json($response);
    }

    /**
     * coin swap history
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function coinSwapHistoryApp(Request $request){
        $limit = $request->per_page ?? 5;
        $order_data['column_name'] = $request->column_name ?? 'id';
        $order_data['order_by'] = $request->order_by ?? 'desc';
        $data['title'] = __('Coin swap history');
        $data['sub_menu'] = 'swap_history';
        $data['list'] = WalletSwapHistory::where(['user_id' => Auth::id()])
                                            ->when(isset($request->search), function($query) use($request){
                                                $query->where('requested_amount', 'LIKE', '%'.$request->search.'%')
                                                        ->orWhere('converted_amount', 'LIKE', '%'.$request->search.'%')
                                                        ->orWhere('from_coin_type', 'LIKE', '%'.$request->search.'%')
                                                        ->orWhere('to_coin_type', 'LIKE', '%'.$request->search.'%');
                                            })
                                            ->orderBy($order_data['column_name'], $order_data['order_by'])->paginate($limit);
        foreach($data['list'] as &$item){
            $item->fromWallet=$item->fromWallet->name;
            $item->toWallet=$item->toWallet->name;
        }
        $response['success'] = true;
        $response['data'] = $data;
        $response['message'] = $data['title'];
        return response()->json($response);
    }

    /**
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function coinSwapApp(){
        $data['title'] = __('Coin Swap');
        $data['wallets'] = Wallet::join('coins','coins.id', '=', 'wallets.coin_id')
            ->where(['wallets.user_id'=> Auth::id(), 'wallets.type'=> PERSONAL_WALLET, 'coins.status' => STATUS_ACTIVE])
            ->orderBy('wallets.id', 'ASC')
            ->select('wallets.*')
            ->get();
        return response()->json(['success'=>true,'data'=>$data,'message'=>__('Coin Swap Data')]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * get rate of coin
     */
    public function getRateApp(WalletRateRequest $request)
    {
        $data = $this->service->get_wallet_rate($request);
        return response()->json($data);
    }

    /**
     * @param CoinSwapRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function swapCoinApp(CoinSwapRequest $request){
        try{
            $data['success']=false;
            $data['message']=__('Something went wrong');
            $fromWallet = Wallet::where(['id'=>$request->from_coin_id])->first();
            // if(isset($request->code)){
            //     $response = checkTwoFactor("two_factor_swap",$request);
            //     if(!$response["success"]){
            //         return response()->json($response);
            //     }
            // }
            if (!empty($fromWallet) && $fromWallet->type == CO_WALLET) {
                return response()->json($data);
            }
            $response = $this->service->get_wallet_rate($request);
            if ($response['success'] == false) {
                return response()->json($data);
            }
            $swap_coin = $this->service->coinSwap($response['from_wallet'], $response['to_wallet'], $response['convert_rate'], $response['amount'], $response['rate']);
            if ($swap_coin['success'] == true) {
                $data['success']=true;
                $data['message']=$swap_coin['message'];
            } else {
                $data['success']=false;
                $data['message']=$swap_coin['message'];
            }
            return response()->json($data);
        }catch(\Exception $e){
            storeException('swapCoinApp ', $e->getMessage());
            return response()->json(responseData(false,__("Something went wrong")));
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCoinSwapDetailsApp(Request $request)
    {
        $wallet = Wallet::find($request->id);
        $data['wallets'] = Coin::select('coins.*', 'wallets.name as wallet_name', 'wallets.id as wallet_id', 'wallets.balance')
            ->join('wallets', 'wallets.coin_type', '=', 'coins.coin_type')
            ->where('coins.status', STATUS_ACTIVE)
            ->where('wallets.user_id', Auth::id())
            ->where('coins.coin_type', '!=', $wallet->coin_type)
            ->get();

        return response()->json($data);
    }

    /**
     * wallet network address
     * @param $walletId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWalletNetworkAddress(NetworkAddressRequest $request)
    {
        try {
            $response = $this->service->getWalletNetworkAddress($request,Auth::id());
        } catch (\Exception $e) {
            storeException('getWalletNetworkAddress', $e->getMessage());
            $response = responseData(false);
        }
        return response()->json($response);
    }

    public function preWithdrawalProcess(Request $request)
    {
        try {
            $response = $this->transService->preWithdrawalProcess($request);
        } catch (\Exception $e) {
            storeException('walletWithdrawalProcess', $e->getMessage());
            $response = ['success' => false, 'message' => __('Something went wrong'), 'data' => []];
        }
        return response()->json($response);
    }

    public function getWalletBalanceDetails(Request $request)
    {
        return response()->json(
            $this->service->getWalletBalanceDetails($request)
        );
    }

    public function walletDetailsApp(Request $request)
    {
        return response()->json(
            $this->service->getWalletDetailsApp($request)
        );
    }

    public function walletWithdrawDetailsApp($wallet_id)
    {
        $response = null;
        if(!isset($wallet_id)) $response = responseData(false, __("Wallet is required"));
        if(!is_numeric($wallet_id)) $response = responseData(false, __("Wallet must be a number"));

        return response()->json(
            $response ?? $this->service->walletWithdrawDetailsApp($wallet_id)
        );
    }

    /**
     * wallet total value
     * @return \Illuminate\Http\JsonResponse
     */
    public function walletTotalValue()
    {
        try {
            $response = $this->service->userWalletTotalValue();
        } catch (\Exception $e) {
            storeException('walletTotalValue', $e->getMessage());
            $response = ['success' => false, 'message' => __('Something went wrong'), 'data' => []];
        }
        return response()->json($response);
    }

    /**
     * Get key statistics including profit/loss, today's transactions, and total asset value
     * @return \Illuminate\Http\JsonResponse
     */
    public function keyStatistics()
    {
        try {
            $userId = Auth::id();
            $data = [];

            // 1. Calculate total profit/loss from yesterday's cryptocurrency holdings
            $wallets = Wallet::where('user_id', $userId)
                ->join('coins', 'wallets.coin_id', '=', 'coins.id')
                ->select(
                    'wallets.*',
                    'coins.coin_type',
                    'coins.coin_price',
                    'coins.coin_icon'
                )
                ->get();

            $totalProfitLoss = 0;
            $yesterdayDate = now()->subDay()->format('Y-m-d');

            foreach ($wallets as $wallet) {
                // Get yesterday's transactions to determine holdings
                $yesterdayTransactions = Transaction::where('wallet_id', $wallet->id)
                    ->whereDate('created_at', $yesterdayDate)
                    ->get();

                $yesterdayBalance = $wallet->balance;

                // Calculate yesterday's balance by subtracting today's transactions
                foreach ($yesterdayTransactions as $transaction) {
                    if ($transaction->type == 'increase' || $transaction->type == 'deposit') {
                        $yesterdayBalance -= $transaction->amount;
                    } elseif ($transaction->type == 'decrease' || $transaction->type == 'withdraw') {
                        $yesterdayBalance += $transaction->amount;
                    }
                }

                // Calculate profit/loss based on price change
                $currentValueInUsd = get_coin_usd_value($wallet->balance, $wallet->coin_type);
                $yesterdayValueInUsd = get_coin_usd_value($yesterdayBalance, $wallet->coin_type);

                $profitLoss = $currentValueInUsd - $yesterdayValueInUsd;
                $totalProfitLoss += $profitLoss;
            }

            // 2. Get today's transactions
            $todayTransactions = Transaction::where('user_id', $userId)
                ->whereDate('created_at', now()->format('Y-m-d'))
                ->with(['currency', 'card'])
                ->orderBy('created_at', 'desc')
                ->get();

            // 3. Calculate total value of assets - using the same approach as in CurrencyController index method
            // Get all currencies with their balances
            $currencies = Coin::leftJoin('wallets', function($join) use ($userId) {
                $join->on('coins.id', '=', 'wallets.coin_id')
                     ->where('wallets.user_id', '=', $userId);
            })
            ->select(
                'coins.id',
                'coins.name',
                'coins.coin_type',
                'coins.coin_icon',
                'coins.coin_price',
                'wallets.balance'
            )
            ->where('coins.status', 1)
            ->where(function($query) {
                $query->where('is_withdrawal', 1)
                      ->orWhere('is_deposit', 1);
            })
            ->get();

            // Get active USD price for Toman conversion
            $usdPrice = UsdPrice::getActive();

            // Calculate total values
            $totalAssetValue = 0;
            $totalAssetValueInToman = 0;

            foreach ($currencies as $currency) {
                // Format balance and calculate USD value exactly as in CurrencyController
                $balance = $currency->balance ?? 0;
                $balanceUsd = get_coin_usd_value($balance, $currency->coin_type);
                $totalAssetValue += $balanceUsd;

                // Calculate Toman value exactly as in CurrencyController
                $balanceToman = (int)($balanceUsd * ($usdPrice ? $usdPrice->sell_price : 0));
                $totalAssetValueInToman += $balanceToman;
            }

            // Calculate profit/loss in Toman
            $totalProfitLossInToman = (int)($totalProfitLoss * ($usdPrice ? $usdPrice->sell_price : 0));

            $data = [
                'profit_loss' => number_format($totalProfitLoss, 2),
                'profit_loss_toman' => $totalProfitLossInToman,
                'today_transactions' => $todayTransactions,
                'today_transactions_count' => $todayTransactions->count(),
                'total_asset_value' => number_format($totalAssetValue, 2),
                'total_asset_value_toman' => $totalAssetValueInToman
            ];

            $response = ['success' => true, 'message' => __('Key statistics retrieved successfully'), 'data' => $data];
        } catch (\Exception $e) {
            storeException('keyStatistics', $e->getMessage());
            $response = ['success' => false, 'message' => __('Something went wrong'), 'data' => []];
        }
        return response()->json($response);
    }
}
