<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\BuyCryptoRequest;
use App\Http\Requests\User\SellCryptoRequest;
use App\Http\Requests\User\SwapCryptoRequest;
use App\Models\Coin;
use App\Models\Currency;
use App\Models\Transaction;
use App\Models\UsdPrice;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TradingController extends Controller
{
    /**
     * Buy cryptocurrency using Toman balance
     *
     * @param BuyCryptoRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function buy(BuyCryptoRequest $request)
    {
        DB::beginTransaction();
        try {
            $user = User::findOrFail(Auth::id());
            $coin = Coin::findOrFail($request->currency_id);
            $tomanAmount = $request->amount;

            // Get active USD price
            $usdPrice = UsdPrice::getActive();
            if (!$usdPrice) {
                return response()->json([
                    'success' => false,
                    'message' => 'قیمت دلار تنظیم نشده است'
                ]);
            }

            // Convert Toman to USD
            $usdAmount = UsdPrice::tomanToUsd($tomanAmount, 'sell');

            // Calculate cryptocurrency amount based on USD value
           // $cryptoAmount = get_coin_usd_value($usdAmount, $coin->coin_type);
            $cryptoAmount = $usdAmount / $coin->coin_price;

            // Check if user has enough Toman balance
            if ($user->toman_balance < $tomanAmount) {
                return response()->json([
                    'success' => false,
                    'message' => 'موجودی تومانی کافی نیست'
                ]);
            }

            // Find or create user's wallet for this currency
            $wallet = Wallet::where('user_id', $user->id)
                ->where('coin_id', $coin->id)
                ->first();

            if (!$wallet) {
                $wallet = Wallet::create([
                    'user_id' => $user->id,
                    'coin_id' => $coin->id,
                    'name' => $coin->name,
                    'balance' => 0,
                    'status' => 'active'
                ]);
            }

            // Deduct Toman from user's balance
            $user->toman_balance -= $tomanAmount;
            $user->save();

            // Add cryptocurrency amount to user's wallet
            $wallet->balance += $cryptoAmount;
            $wallet->save();

            // Create transaction record
            $transaction = Transaction::create([
                'type' => 'buy',
                'amount' => $cryptoAmount,
                'price' => $tomanAmount,
                'wallet_id' => $wallet->id,
                'currency_id' => $coin->id,
                'user_id' => $user->id,
                'registrar' => $user->id,
                'status' => 'done',
                'description' => 'خرید ارز',
                'details' => json_encode([
                    'toman_amount' => $tomanAmount,
                    'usd_amount' => $usdAmount,
                    'usd_rate' => $usdPrice->sell_price,
                    'crypto_amount' => $cryptoAmount
                ]),
                'balance_before' => $wallet->balance - $cryptoAmount,
                'balance_after' => $wallet->balance
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'خرید با موفقیت انجام شد',
                'data' => [
                    'transaction' => $transaction,
                    'wallet_balance' => $wallet->balance,
                    'toman_balance' => $user->toman_balance,
                    'crypto_amount' => $cryptoAmount,
                    'usd_amount' => $usdAmount
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'خطا در انجام خرید: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Sell cryptocurrency and receive Toman
     *
     * @param SellCryptoRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sell(SellCryptoRequest $request)
    {
        DB::beginTransaction();
        try {
            $user = User::findOrFail(Auth::id());
            $coin = Coin::findOrFail($request->currency_id);
            $cryptoAmount = $request->amount;

            // Find user's wallet for this currency
            $wallet = Wallet::where('user_id', $user->id)
                ->where('coin_id', $coin->id)
                ->first();

            if (!$wallet || $wallet->balance < $cryptoAmount) {
                return response()->json([
                    'success' => false,
                    'message' => 'موجودی ارز کافی نیست'
                ]);
            }

            // Get active USD price
            $usdPrice = UsdPrice::getActive();
            if (!$usdPrice) {
                return response()->json([
                    'success' => false,
                    'message' => 'قیمت دلار تنظیم نشده است'
                ]);
            }

            // Convert cryptocurrency to USD
          //  $usdAmount = $cryptoAmount / $coin->coin_price;
            $usdAmount = get_coin_usd_value($cryptoAmount, $coin->coin_type);

            // Convert USD to Toman
            $tomanAmount = UsdPrice::usdToToman($usdAmount, 'buy');

            // Deduct cryptocurrency from user's wallet
            $wallet->balance -= $cryptoAmount;
            $wallet->save();

            // Add Toman to user's balance
            $user->toman_balance += $tomanAmount;
            $user->save();

            // Create transaction record
            $transaction = Transaction::create([
                'type' => 'sell',
                'amount' => $cryptoAmount,
                'price' => $tomanAmount,
                'wallet_id' => $wallet->id,
                'currency_id' => $coin->id,
                'user_id' => $user->id,
                'registrar' => $user->id,
                'status' => 'done',
                'description' => 'فروش ارز',
                'details' => json_encode([
                    'crypto_amount' => $cryptoAmount,
                    'usd_amount' => $usdAmount,
                    'usd_rate' => $usdPrice->buy_price,
                    'toman_amount' => $tomanAmount
                ]),
                'balance_before' => $wallet->balance + $cryptoAmount,
                'balance_after' => $wallet->balance
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'فروش با موفقیت انجام شد',
                'data' => [
                    'transaction' => $transaction,
                    'wallet_balance' => $wallet->balance,
                    'toman_balance' => $user->toman_balance,
                    'crypto_amount' => $cryptoAmount,
                    'usd_amount' => $usdAmount
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'خطا در انجام فروش: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Swap one cryptocurrency for another
     *
     * @param SwapCryptoRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function swap(SwapCryptoRequest $request)
    {
        DB::beginTransaction();
        try {
            $user = User::findOrFail(Auth::id());
            $fromCoin = Coin::findOrFail($request->from_currency_id);
            $toCoin = Coin::findOrFail($request->to_currency_id);
            $amount = $request->amount;

            // Find user's wallets for both currencies
            $fromWallet = Wallet::where('user_id', $user->id)
                ->where('coin_id', $fromCoin->id)
                ->first();

            $toWallet = Wallet::where('user_id', $user->id)
                ->where('coin_id', $toCoin->id)
                ->first();

            // Create destination wallet if it doesn't exist
            if (!$toWallet) {
                $toWallet = Wallet::create([
                    'user_id' => $user->id,
                    'coin_id' => $toCoin->id,
                    'name' => $toCoin->name,
                    'balance' => 0,
                    'status' => 'active'
                ]);
            }

            // Check if user has enough balance
            if ($fromWallet->balance < $amount) {
                return response()->json([
                    'success' => false,
                    'message' => 'موجودی ارز کافی نیست'
                ]);
            }

            // Get active USD price
            $usdPrice = UsdPrice::getActive();
            if (!$usdPrice) {
                return response()->json([
                    'success' => false,
                    'message' => 'قیمت دلار تنظیم نشده است'
                ]);
            }

            // Convert from currency to USD
            $fromUsdValue = $amount * $fromCoin->coin_price;

            // Convert USD to target currency
            $toAmount = $fromUsdValue / $toCoin->coin_price;

            // Apply swap fee if needed (e.g., 0.5%)
            $swapFee = 0.005; // 0.5%
            $feeAmount = $toAmount * $swapFee;
            $finalAmount = $toAmount - $feeAmount;

            // Deduct from source wallet
            $fromWallet->balance -= $amount;
            $fromWallet->save();

            // Add to destination wallet
            $toWallet->balance += $finalAmount;
            $toWallet->save();

            // Create transaction record for source wallet (debit)
            $fromTransaction = Transaction::create([
                'type' => 'swap_out', // Using the new swap_out type for the source wallet
                'amount' => $amount,
                'price' => $fromUsdValue,
                'wallet_id' => $fromWallet->id,
                'currency_id' => $fromCoin->id,
                'user_id' => $user->id,
                'registrar' => $user->id,
                'status' => 'done',
                'description' => 'تبدیل ارز',
                'details' => json_encode([
                    'from_currency' => $fromCoin->coin_type,
                    'to_currency' => $toCoin->coin_type,
                    'from_amount' => $amount,
                    'to_amount' => $finalAmount,
                    'usd_value' => $fromUsdValue,
                    'fee_percentage' => $swapFee * 100 . '%',
                    'fee_amount' => $feeAmount
                ]),
                'balance_before' => $fromWallet->balance + $amount,
                'balance_after' => $fromWallet->balance
            ]);

            // Create transaction record for destination wallet (credit)
            $toTransaction = Transaction::create([
                'type' => 'swap_in', // Using the new swap_in type for the destination wallet
                'amount' => $finalAmount,
                'price' => $fromUsdValue,
                'wallet_id' => $toWallet->id,
                'currency_id' => $toCoin->id,
                'user_id' => $user->id,
                'registrar' => $user->id,
                'status' => 'done',
                'description' => 'دریافت ارز از تبدیل',
                'details' => json_encode([
                    'from_currency' => $fromCoin->coin_type,
                    'to_currency' => $toCoin->coin_type,
                    'from_amount' => $amount,
                    'to_amount' => $finalAmount,
                    'usd_value' => $fromUsdValue,
                    'fee_percentage' => $swapFee * 100 . '%',
                    'fee_amount' => $feeAmount
                ]),
                'balance_before' => $toWallet->balance - $finalAmount,
                'balance_after' => $toWallet->balance
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تبدیل ارز با موفقیت انجام شد',
                'data' => [
                    'from_transaction' => $fromTransaction,
                    'to_transaction' => $toTransaction,
                    'from_wallet_balance' => $fromWallet->balance,
                    'to_wallet_balance' => $toWallet->balance,
                    'from_amount' => $amount,
                    'to_amount' => $finalAmount,
                    'fee_amount' => $feeAmount,
                    'usd_value' => $fromUsdValue
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'خطا در انجام تبدیل ارز: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get swap history for the authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function swapHistory(Request $request)
    {
        try {
            $user = User::findOrFail(Auth::id());
            $perPage = $request->per_page ?? 15;
            $page = $request->page ?? 1;

            // Get all swap transactions for the user
            $swapOutTransactions = Transaction::where('user_id', $user->id)
                ->where('type', 'swap_out')
                ->with(['currency', 'wallet'])
                ->orderBy('created_at', 'desc');

            // Apply filters if provided
            if ($request->has('from_date') && $request->from_date) {
                $swapOutTransactions->whereDate('created_at', '>=', $request->from_date);
            }

            if ($request->has('to_date') && $request->to_date) {
                $swapOutTransactions->whereDate('created_at', '<=', $request->to_date);
            }

            if ($request->has('from_currency_id') && $request->from_currency_id) {
                $swapOutTransactions->where('currency_id', $request->from_currency_id);
            }

            // Get paginated results
            $swapOutTransactions = $swapOutTransactions->paginate($perPage, ['*'], 'page', $page);

            // For each swap_out transaction, find the corresponding swap_in transaction
            $swapOutTransactions->getCollection()->transform(function ($transaction) {
                $details = json_decode($transaction->details, true);

                // Find the corresponding swap_in transaction
                $swapInTransaction = Transaction::where('user_id', $transaction->user_id)
                    ->where('type', 'swap_in')
                    ->where('created_at', $transaction->created_at)
                    ->first();

                if ($swapInTransaction) {
                    $toCoin = Coin::find($swapInTransaction->currency_id);
                    $transaction->to_currency = $toCoin ? $toCoin->only(['id', 'name', 'coin_type']) : null;
                    $transaction->to_amount = $swapInTransaction->amount;
                }

                // Add from_currency details
                $fromCoin = $transaction->currency;
                $transaction->from_currency = $fromCoin ? $fromCoin->only(['id', 'name', 'coin_type']) : null;
                $transaction->from_amount = $transaction->amount;

                // Add fee details from the transaction details
                $transaction->fee_percentage = $details['fee_percentage'] ?? '0%';
                $transaction->fee_amount = $details['fee_amount'] ?? 0;
                $transaction->usd_value = $details['usd_value'] ?? 0;

                // Remove unnecessary fields
                unset($transaction->currency);
                unset($transaction->details);

                return $transaction;
            });

            return response()->json([
                'success' => true,
                'message' => 'تاریخچه تبدیل ارز با موفقیت دریافت شد',
                'data' => $swapOutTransactions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت تاریخچه تبدیل ارز: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get all transactions history for the authenticated user with detailed information
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function transactionHistory(Request $request)
    {
        try {
            $user = User::findOrFail(Auth::id());
            $perPage = $request->per_page ?? 15;
            $page = $request->page ?? 1;

            // Get all transactions for the user
            $transactions = Transaction::where('user_id', $user->id)
                ->with(['currency', 'wallet'])
                ->orderBy('created_at', 'desc');

            // Apply filters if provided
            if ($request->has('type') && $request->type) {
                $transactions->where('type', $request->type);
            }

            if ($request->has('status') && $request->status) {
                $transactions->where('status', $request->status);
            }

            if ($request->has('from_date') && $request->from_date) {
                $transactions->whereDate('created_at', '>=', $request->from_date);
            }

            if ($request->has('to_date') && $request->to_date) {
                $transactions->whereDate('created_at', '<=', $request->to_date);
            }

            if ($request->has('currency_id') && $request->currency_id) {
                $transactions->where('currency_id', $request->currency_id);
            }

            if ($request->has('wallet_id') && $request->wallet_id) {
                $transactions->where('wallet_id', $request->wallet_id);
            }

            // Get paginated results
            $transactions = $transactions->paginate($perPage, ['*'], 'page', $page);

            // Transform each transaction to include detailed information
            $transactions->getCollection()->transform(function ($transaction) {
                // Add currency details
                if ($transaction->currency) {
                    $transaction->currency_details = [
                        'id' => $transaction->currency->id,
                        'name' => $transaction->currency->name,
                        'coin_type' => $transaction->currency->coin_type,
                        'coin_price' => $transaction->currency->coin_price
                    ];
                }

                // Add wallet details
                if ($transaction->wallet) {
                    $transaction->wallet_details = [
                        'id' => $transaction->wallet->id,
                        'name' => $transaction->wallet->name,
                        'balance' => $transaction->wallet->balance
                    ];
                }

                // Process details based on transaction type
                if ($transaction->type == 'swap_out' || $transaction->type == 'swap_in') {
                    $details = json_decode($transaction->details, true);

                    // For swap transactions, add source and destination currency info
                    if ($transaction->type == 'swap_out') {
                        $transaction->swap_details = [
                            'from_currency' => $details['from_currency'] ?? null,
                            'to_currency' => $details['to_currency'] ?? null,
                            'from_amount' => $details['from_amount'] ?? null,
                            'to_amount' => $details['to_amount'] ?? null,
                            'usd_value' => $details['usd_value'] ?? null,
                            'fee_percentage' => $details['fee_percentage'] ?? null,
                            'fee_amount' => $details['fee_amount'] ?? null
                        ];
                    } else { // swap_in
                        $transaction->swap_details = [
                            'from_currency' => $details['from_currency'] ?? null,
                            'to_currency' => $details['to_currency'] ?? null,
                            'from_amount' => $details['from_amount'] ?? null,
                            'to_amount' => $details['to_amount'] ?? null,
                            'usd_value' => $details['usd_value'] ?? null,
                            'fee_percentage' => $details['fee_percentage'] ?? null,
                            'fee_amount' => $details['fee_amount'] ?? null
                        ];
                    }
                } else if ($transaction->type == 'buy') {
                    $details = json_decode($transaction->details, true);

                    $transaction->buy_details = [
                        'toman_amount' => $details['toman_amount'] ?? null,
                        'usd_amount' => $details['usd_amount'] ?? null,
                        'usd_rate' => $details['usd_rate'] ?? null,
                        'crypto_amount' => $details['crypto_amount'] ?? null
                    ];
                } else if ($transaction->type == 'sell') {
                    $details = json_decode($transaction->details, true);

                    $transaction->sell_details = [
                        'crypto_amount' => $details['crypto_amount'] ?? null,
                        'usd_amount' => $details['usd_amount'] ?? null,
                        'usd_rate' => $details['usd_rate'] ?? null,
                        'toman_amount' => $details['toman_amount'] ?? null
                    ];
                } else {
                    // For other transaction types, just decode the details
                    $transaction->transaction_details = json_decode($transaction->details, true);
                }

                // Add human-readable transaction type description
                $transaction->type_description = $this->getTransactionTypeDescription($transaction->type);

                // Remove raw details to avoid duplication
                unset($transaction->details);

                return $transaction;
            });

            return response()->json([
                'success' => true,
                'message' => 'تاریخچه تراکنش ها با موفقیت دریافت شد',
                'data' => $transactions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت تاریخچه تراکنش ها: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get human-readable description for transaction types
     *
     * @param string $type
     * @return string
     */
    private function getTransactionTypeDescription($type)
    {
        $descriptions = [
            'deposit' => 'واریز',
            'withdraw' => 'برداشت',
            'gift' => 'هدیه',
            'transfer' => 'انتقال',
            'buy' => 'خرید',
            'sell' => 'فروش',
            'increase' => 'افزایش موجودی',
            'decrease' => 'کاهش موجودی',
            'swap_out' => 'تبدیل ارز (خروجی)',
            'swap_in' => 'تبدیل ارز (ورودی)'
        ];

        return $descriptions[$type] ?? $type;
    }

    /**
     * Get a single transaction by ID with detailed information
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTransaction($id)
    {
        try {
            $user = User::findOrFail(Auth::id());

            // Get the transaction for the user
            $transaction = Transaction::where('user_id', $user->id)
                ->where('id', $id)
                ->with(['currency', 'wallet'])
                ->first();

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'تراکنش مورد نظر یافت نشد'
                ], 404);
            }

            // Transform the transaction to include detailed information
            // Add currency details
            if ($transaction->currency) {
                $transaction->currency_details = [
                    'id' => $transaction->currency->id,
                    'name' => $transaction->currency->name,
                    'coin_type' => $transaction->currency->coin_type,
                    'coin_price' => $transaction->currency->coin_price
                ];
            }

            // Add wallet details
            if ($transaction->wallet) {
                $transaction->wallet_details = [
                    'id' => $transaction->wallet->id,
                    'name' => $transaction->wallet->name,
                    'balance' => $transaction->wallet->balance
                ];
            }

            // Process details based on transaction type
            if ($transaction->type == 'swap_out' || $transaction->type == 'swap_in') {
                $details = json_decode($transaction->details, true);

                // For swap transactions, add source and destination currency info
                if ($transaction->type == 'swap_out') {
                    $transaction->swap_details = [
                        'from_currency' => $details['from_currency'] ?? null,
                        'to_currency' => $details['to_currency'] ?? null,
                        'from_amount' => $details['from_amount'] ?? null,
                        'to_amount' => $details['to_amount'] ?? null,
                        'usd_value' => $details['usd_value'] ?? null,
                        'fee_percentage' => $details['fee_percentage'] ?? null,
                        'fee_amount' => $details['fee_amount'] ?? null
                    ];
                } else { // swap_in
                    $transaction->swap_details = [
                        'from_currency' => $details['from_currency'] ?? null,
                        'to_currency' => $details['to_currency'] ?? null,
                        'from_amount' => $details['from_amount'] ?? null,
                        'to_amount' => $details['to_amount'] ?? null,
                        'usd_value' => $details['usd_value'] ?? null,
                        'fee_percentage' => $details['fee_percentage'] ?? null,
                        'fee_amount' => $details['fee_amount'] ?? null
                    ];
                }
            } else if ($transaction->type == 'buy') {
                $details = json_decode($transaction->details, true);

                $transaction->buy_details = [
                    'toman_amount' => $details['toman_amount'] ?? null,
                    'usd_amount' => $details['usd_amount'] ?? null,
                    'usd_rate' => $details['usd_rate'] ?? null,
                    'crypto_amount' => $details['crypto_amount'] ?? null
                ];
            } else if ($transaction->type == 'sell') {
                $details = json_decode($transaction->details, true);

                $transaction->sell_details = [
                    'crypto_amount' => $details['crypto_amount'] ?? null,
                    'usd_amount' => $details['usd_amount'] ?? null,
                    'usd_rate' => $details['usd_rate'] ?? null,
                    'toman_amount' => $details['toman_amount'] ?? null
                ];
            } else {
                // For other transaction types, just decode the details
                $transaction->transaction_details = json_decode($transaction->details, true);
            }

            // Add human-readable transaction type description
            $transaction->type_description = $this->getTransactionTypeDescription($transaction->type);

            // Remove raw details to avoid duplication
            unset($transaction->details);

            return response()->json([
                'success' => true,
                'message' => 'اطلاعات تراکنش با موفقیت دریافت شد',
                'data' => $transaction
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت اطلاعات تراکنش: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get daily limits information for buy and sell operations
     * Shows how much of the daily limit has been used and how much remains
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function dailyLimits(Request $request)
    {
        try {
            $user = User::findOrFail(Auth::id());
            $currencyId = $request->currency_id ?? null;

            // Default response structure
            $response = [
                'buy' => [
                    'limit' => 0,
                    'used' => 0,
                    'remaining' => 0,
                    'can_buy' => false,
                    'next_buy_time' => null
                ],
                'sell' => [
                    'limit' => 0,
                    'used' => 0,
                    'remaining' => 0,
                    'can_sell' => false
                ]
            ];

            // If user is level 1, they can't buy or sell
            if ($user->level == 1) {
                return response()->json([
                    'success' => true,
                    'message' => 'اطلاعات محدودیت‌های روزانه',
                    'data' => $response
                ]);
            }

            // Get buy transactions for today
            $buyTransactions = $user->transactions()
                ->where('type', 'buy')
                ->where('status', 'done')
                ->whereDate('created_at', date('Y-m-d'));

            if ($currencyId) {
                $buyTransactions->where('currency_id', $currencyId);
            }

            $todayBuyAmount = intval($buyTransactions->sum('price'));

            // Get sell transactions for today
            $sellTransactions = $user->transactions()
                ->where('type', 'sell')
                ->where('status', 'done')
                ->whereDate('created_at', date('Y-m-d'));

            if ($currencyId) {
                $sellTransactions->where('currency_id', $currencyId);
            }

            $todaySellAmount = intval($sellTransactions->sum('price'));

            // Get limits based on user level
            $canBuy = true;
            $nextBuyTime = null;

            switch ($user->level) {
                case 2:
                    $buyLimit = intval(env('L2_MAX_DAILY_BUY', 0));
                    $sellLimit = intval(env('L2_MAX_DAILY_SELL', 0));

                    // We're removing the 48-hour rule to allow multiple purchases within the same day
                    // as long as the user hasn't reached their daily limit
                    $canBuy = true;
                    $nextBuyTime = null;
                    break;

                case 3:
                    $buyLimit = intval(env('L3_MAX_DAILY_BUY', 0));
                    $sellLimit = intval(env('L3_MAX_DAILY_SELL', 0));
                    break;

                default:
                    // For other levels
                    $buyLimit = 0;
                    $sellLimit = 0;
                    $canBuy = false;
                    break;
            }

            // Calculate remaining amounts
            $buyRemaining = max(0, $buyLimit - $todayBuyAmount);
            $sellRemaining = max(0, $sellLimit - $todaySellAmount);

            // Update response
            $response['buy'] = [
                'limit' => $buyLimit,
                'used' => $todayBuyAmount,
                'remaining' => $buyRemaining,
                'can_buy' => $canBuy && ($buyRemaining > 0),
                'next_buy_time' => $nextBuyTime
            ];

            $response['sell'] = [
                'limit' => $sellLimit,
                'used' => $todaySellAmount,
                'remaining' => $sellRemaining,
                'can_sell' => ($sellRemaining > 0)
            ];

            return response()->json([
                'success' => true,
                'message' => 'اطلاعات محدودیت‌های روزانه با موفقیت دریافت شد',
                'data' => $response
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت اطلاعات محدودیت‌های روزانه: ' . $e->getMessage()
            ]);
        }
    }
}
