<?php

namespace App\Http\Controllers\User;

use App\CurrencyProviders\TronProvider;
use App\Http\Controllers\Controller;
use App\Http\Requests\User\Trade\StoreTradeRequest;
use App\Http\Requests\User\Trade\UpdateTradeRequest;
use App\Models\Currency;
use App\Models\Transaction;
use App\Models\UserWallet;
use App\Services\User\TradeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Mollsoft\LaravelTronModule\Facades\Tron;
use Mollsoft\LaravelTronModule\Models\TronAddress;
use Mollsoft\LaravelTronModule\Models\TronWallet;

class TradeController extends Controller
{

    public function __construct(
        protected TradeService $service,
        protected TronProvider $tronProvider,
    ){}
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return [];
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTradeRequest $request)
    {
        return $this->service->storeTrade($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->service->showTradeData($id);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTradeRequest $request, string $id)
    {

    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
