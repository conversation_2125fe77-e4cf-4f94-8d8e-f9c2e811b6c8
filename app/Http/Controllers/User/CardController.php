<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\Card\StoreCardRequest;
use App\Services\User\CardService;
use Illuminate\Http\Request;

class CardController extends Controller
{
    public function __construct(
        protected CardService $service
    ) {}

    public function index(Request $request)
    {
        return $this->service->listCards($request);
    }

    public function store(StoreCardRequest $request)
    {
        $create = $this->service->createCard($request->validated());
        return $this->service->showCard($create->id);
    }
}
