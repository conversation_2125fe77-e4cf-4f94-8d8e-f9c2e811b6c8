<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\TomanDepositRequest;
use App\Models\Transaction;
use App\Services\User\TomanDepositService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class TomanDepositController extends Controller
{
    public function __construct(
        protected TomanDepositService $service,
    ) {}

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $deposits = $this->service->depositList($request);

        return response()->json([
            'success' => true,
            'data' => [
                'deposits' => $deposits
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param TomanDepositRequest $request
     * @return JsonResponse
     */
    public function store(TomanDepositRequest $request): JsonResponse
    {
        $result = $this->service->createDeposit($request->validated());

        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['success'] ? $result['data'] ?? null : null
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $deposit = $this->service->showDeposit($id);

        return response()->json([
            'success' => true,
            'data' => $deposit
        ]);
    }
}
