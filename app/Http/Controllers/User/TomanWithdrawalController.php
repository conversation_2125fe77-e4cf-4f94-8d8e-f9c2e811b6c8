<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\TomanWithdrawalRequest;
use App\Models\Card;
use App\Services\User\TomanWithdrawalService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class TomanWithdrawalController extends Controller
{
    public function __construct(
        protected TomanWithdrawalService $service,
    ) {}

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        // Get user's approved cards
        $cards = Card::where('user_id', Auth::id())
            ->where('status', 'approved')
            ->with('bank')
            ->get();

        // Get user's withdrawal history
        $withdrawals = $this->service->withdrawalList($request);

        return response()->json([
            'success' => true,
            'data' => [
                'cards' => $cards,
                'withdrawals' => $withdrawals
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param TomanWithdrawalRequest $request
     * @return JsonResponse
     */
    public function store(TomanWithdrawalRequest $request): JsonResponse
    {
        // The 2FA verification is already handled in the TomanWithdrawalRequest
        $result = $this->service->createWithdrawal($request->validated());

        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'],
            'data' => $result['success'] ? $result['data'] ?? null : null
        ], $result['success'] ? 200 : 400);
    }

    /**
     * Check if the user needs 2FA for withdrawal
     *
     * @return JsonResponse
     */
    public function check2fa(): JsonResponse
    {
        $user = Auth::user();
        $needs2fa = $user && $user->g2f_enabled && !empty($user->google2fa_secret);

        return response()->json([
            'success' => true,
            'data' => [
                'needs2fa' => $needs2fa
            ]
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        try {
            $withdrawal = $this->service->showWithdrawal($id);

            return response()->json([
                'success' => true,
                'data' => $withdrawal
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'برداشت مورد نظر یافت نشد'
            ], 404);
        }
    }
}
