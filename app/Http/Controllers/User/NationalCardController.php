<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\StoreNationalCardRequest;
use App\Jobs\Zibal\OcrNationalCard;
use App\Models\Document;
use App\Services\FileDataService;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class NationalCardController extends Controller
{
    public function __construct(
        protected FileDataService $fileDataService,
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return Document::with('file')->where('user_id', $request->user()->id)->whereIn('name', ['front-ocr', 'back-ocr'])->get();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreNationalCardRequest $request)
    {
        $document = Document::where('user_id', $request->user()->id)->whereIn('name', ['front-ocr', 'back-ocr'])->exists();
        if ($document) {
            throw ValidationException::withMessages(['کارت ملی قبلا ارسال شده. تا مشخص شدن نتیجه نهایی منتظر بمانید.']);
        }
        $user = $request->user();
        $front = $request->file('front');
        $back = $request->file('back');

        $front_document = $user->documents()->updateOrCreate([
            'name' => 'front-ocr',
            'type' => 'image',
        ]);
        $this->fileDataService->createFile($front, $front_document);

        $back_document = $user->documents()->updateOrCreate([
            'name' => 'back-ocr',
            'type' => 'image',
        ]);
        $this->fileDataService->createFile($back, $back_document);

        OcrNationalCard::dispatch($front_document, $back_document);

        return [$front_document, $back_document];

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
