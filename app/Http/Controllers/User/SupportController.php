<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\Support\StoreSupportRequest;
use App\Http\Requests\User\Support\UpdateSupportRequest;
use App\Http\Requests\User\Support\ReplySupportRequest;
use App\Services\User\SupportService;
use Illuminate\Http\Request;

class SupportController extends Controller
{
    public function __construct(
        protected SupportService $service,
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return $this->service->allTickets($request);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSupportRequest $request)
    {
        return $this->service->createTicket($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->service->getSupportRequest($id);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSupportRequest $request, string $id)
    {
        return $this->service->updateTicket($id, $request->validated());
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        return $this->service->deleteTicket($id);
    }

    /**
     * Reply to an existing ticket
     */
    public function reply(ReplySupportRequest $request, string $id)
    {
        return $this->service->replyToTicket($id, $request->validated());
    }
}
