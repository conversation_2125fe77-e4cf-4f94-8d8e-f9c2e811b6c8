<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\UsdPrice;
use App\Models\Wallet;
use App\Http\Services\TransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class TransactionController extends Controller
{
    public function __construct(protected TransactionService $service) {}

    public function index(Request $request, ?Wallet $wallet = null)
    {
        $query = Transaction::where('user_id', Auth::id());

        // Apply filters if provided
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // If wallet is provided, filter by wallet
        if ($wallet) {
            $query->where('wallet_id', $wallet->id);
        }

        return $query->with(['currency', 'card'])->orderBy('created_at', 'desc')->paginate(15);
    }

    public function store(Request $request, Wallet $wallet)
    {
        $data = $request->validate([
            'currency' => ['required', 'string', 'max:5'],
            'crypto' => ['required', 'boolean'],
            'balance' => ['required', 'numeric'],
        ]);

        return $request->user()->transactions()->create([...$data, 'wallet_id' => $wallet->id]);
    }

    public function show(Request $request, Transaction $transaction)
    {
        throw_if($transaction->user()->isNot($request->user()), ValidationException::withMessages(['این تراکنش متعلق به شما نیست.']));

        return $transaction;
    }

    /**
     * Get total withdrawals, deposits, and purchases for the authenticated user in Toman
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function totals()
    {
        try {
            $userId = Auth::id();
            $tomanCurrencyId = 6; // Toman/IRR currency ID

            // Calculate total withdrawals in Toman
            $totalWithdrawals = Transaction::where('user_id', $userId)
                ->where('type', 'withdraw')
                ->where('status', 'done')
                ->where('currency_id', $tomanCurrencyId)
                ->sum('amount');

            // Calculate total deposits in Toman
            $totalDeposits = Transaction::where('user_id', $userId)
                ->where('type', 'deposit')
                ->where('status', 'done')
                ->where('currency_id', $tomanCurrencyId)
                ->sum('amount');

            // Calculate total purchases in Toman
            $totalPurchases = Transaction::where('user_id', $userId)
                ->where('type', 'buy')
                ->where('status', 'done')
                ->where('currency_id', $tomanCurrencyId)
                ->sum('amount');

            // For non-Toman transactions, convert to Toman
            $nonTomanWithdrawals = $this->convertNonTomanTransactionsToToman($userId, 'withdraw');
            $nonTomanDeposits = $this->convertNonTomanTransactionsToToman($userId, 'deposit');
            $nonTomanPurchases = $this->convertNonTomanTransactionsToToman($userId, 'buy');

            // Add converted amounts to totals
            $totalWithdrawals += $nonTomanWithdrawals;
            $totalDeposits += $nonTomanDeposits;
            $totalPurchases += $nonTomanPurchases;

            return response()->json([
                'success' => true,
                'message' => 'اطلاعات مجموع تراکنش‌ها با موفقیت دریافت شد',
                'data' => [
                    'total_withdrawals' => $totalWithdrawals,
                    'total_deposits' => $totalDeposits,
                    'total_purchases' => $totalPurchases
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در دریافت اطلاعات مجموع تراکنش‌ها: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert non-Toman transactions to Toman
     *
     * @param int $userId
     * @param string $type
     * @return float
     */
    private function convertNonTomanTransactionsToToman($userId, $type)
    {
        $tomanCurrencyId = 6; // Toman/IRR currency ID
        $totalInToman = 0;

        // Get all non-Toman transactions of the specified type
        $transactions = Transaction::where('user_id', $userId)
            ->where('type', $type)
            ->where('status', 'done')
            ->where('currency_id', '!=', $tomanCurrencyId)
            ->with('currency')
            ->get();

        // Get active USD price
        $usdPrice = UsdPrice::getActive();
        if (!$usdPrice) {
            return 0; // If no USD price is set, return 0
        }

        foreach ($transactions as $transaction) {
            // Get the coin's USD value
            $coinUsdValue = 0;
            if ($transaction->currency) {
                $coinUsdValue = $transaction->currency->coin_price ?? 0;
            }

            // Convert to Toman: amount * coin_price_in_usd * usd_price_in_toman
            $amountInToman = $transaction->amount * $coinUsdValue * $usdPrice->sell_price;
            $totalInToman += $amountInToman;
        }

        return $totalInToman;
    }
}
