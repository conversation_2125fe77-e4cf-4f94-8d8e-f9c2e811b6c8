<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\Deposit\StoreDepositRequest;
use App\Services\User\DepositService;

class DepositController extends Controller
{
    public function __construct(
        protected DepositService $service,
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return $this->service->getAllDeposits();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDepositRequest $request)
    {
        return $this->service->createDeposit($request->validated());
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->service->getDeposit($id);
    }
}
