<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use PragmaRX\Google2FA\Google2FA;

class TwoFactorAuthController extends Controller
{
    /**
     * Get the current 2FA status for the authenticated user
     *
     * @return JsonResponse
     */
    public function status(): JsonResponse
    {
        $user = Auth::user();

        return response()->json([
            'success' => true,
            'data' => [
                'enabled' => (bool) $user->g2f_enabled,
                'has_secret' => !empty($user->google2fa_secret)
            ]
        ]);
    }

    /**
     * Generate a new 2FA secret for the authenticated user
     *
     * @return JsonResponse
     */
    public function generate(): JsonResponse
    {
        $user = Auth::user();
        $google2fa = new Google2FA();

        // Generate a new secret key
        $secretKey = $google2fa->generateSecretKey();

        // Generate the QR code URL
        $companyName = config('app.name', 'Exchangim');
        $qrCodeUrl = $google2fa->getQRCodeUrl(
            $companyName,
            $user->email,
            $secretKey
        );

        // Store the secret temporarily in the session
        session(['2fa_temp_secret' => $secretKey]);

        return response()->json([
            'success' => true,
            'data' => [
                'secret' => $secretKey,
                'qr_code_url' => $qrCodeUrl
            ],
            'message' => 'کد احراز هویت دو مرحله‌ای با موفقیت ایجاد شد. لطفاً آن را در برنامه Google Authenticator اسکن کنید.'
        ]);
    }

    /**
     * Enable 2FA for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function enable(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string',
            'secret' => 'required|string'
        ]);

        $user = Auth::user();
        $google2fa = new Google2FA();

        // Verify the provided code with the secret
        $valid = $google2fa->verifyKey($request->secret, $request->code);

        if (!$valid) {
            throw ValidationException::withMessages([
                'code' => ['کد وارد شده نامعتبر است. لطفاً دوباره تلاش کنید.']
            ]);
        }

        // Save the secret and enable 2FA
        $user->google2fa_secret = $request->secret;
        $user->g2f_enabled = true;
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'احراز هویت دو مرحله‌ای با موفقیت فعال شد.'
        ]);
    }

    /**
     * Disable 2FA for the authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function disable(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string'
        ]);

        $user = Auth::user();

        // Check if 2FA is enabled
        if (!$user->g2f_enabled || empty($user->google2fa_secret)) {
            return response()->json([
                'success' => false,
                'message' => 'احراز هویت دو مرحله‌ای در حال حاضر غیرفعال است.'
            ], 400);
        }

        $google2fa = new Google2FA();

        // Verify the provided code with the user's secret
        $valid = $google2fa->verifyKey($user->google2fa_secret, $request->code);

        if (!$valid) {
            throw ValidationException::withMessages([
                'code' => ['کد وارد شده نامعتبر است. لطفاً دوباره تلاش کنید.']
            ]);
        }

        // Disable 2FA
        $user->g2f_enabled = false;
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'احراز هویت دو مرحله‌ای با موفقیت غیرفعال شد.'
        ]);
    }

    /**
     * Reset 2FA for the authenticated user (completely remove 2FA)
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function reset(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string'
        ]);

        $user = Auth::user();

        // Check if 2FA is set up
        if (empty($user->google2fa_secret)) {
            return response()->json([
                'success' => false,
                'message' => 'احراز هویت دو مرحله‌ای تنظیم نشده است.'
            ], 400);
        }

        $google2fa = new Google2FA();

        // Verify the provided code with the user's secret
        $valid = $google2fa->verifyKey($user->google2fa_secret, $request->code);

        if (!$valid) {
            throw ValidationException::withMessages([
                'code' => ['کد وارد شده نامعتبر است. لطفاً دوباره تلاش کنید.']
            ]);
        }

        // Reset 2FA completely
        $user->google2fa_secret = null;
        $user->g2f_enabled = false;
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'احراز هویت دو مرحله‌ای با موفقیت حذف شد.'
        ]);
    }
}
