<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ListCurrency extends ResourceCollection
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // return parent::toArray($request);
        return [
            'status' => true,
            'data' => $this->collection->transform(function ($package) {
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'code' => strtoupper($package->code),
                    'icon' => $package->icon,
                ];
            }),
        ];

    }
}
