<?php

namespace App\Http\Resources\Admin\Card;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class IndexCards extends ResourceCollection
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // return parent::toArray($request);
        return [
            'status' => true,
            'data' => $this->collection->transform(function ($data) {
                return [
                    'id' => $data->id,
                    'status' => $data->status,
                    'user' => [
                        'id' => $data->user->id,
                        'firstname' => $data->user->firstname,
                        'lastname' => $data->user->lastname,
                        'phone' => $data->user->phone,
                    ],
                    'bank' => $data->bank,
                    'number' => $data->number,
                    'sheba' => $data->sheba,
                ];
            }),
        ];

    }
}
