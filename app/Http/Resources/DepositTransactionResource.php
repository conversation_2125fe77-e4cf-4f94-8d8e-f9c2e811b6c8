<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DepositTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'transaction_id' => $this->transaction_id,
            'address' => $this->address,
            'from_address' => $this->from_address,
            'amount' => [
                'value' => $this->amount,
                'formatted' => number_format($this->amount, 8),
                'display' => $this->amount . ' ' . ($this->coin->coin_type ?? $this->coin_type),
            ],
            'received_amount' => [
                'value' => $this->received_amount,
                'formatted' => number_format($this->received_amount ?? 0, 8),
                'display' => ($this->received_amount ?? 0) . ' ' . ($this->coin->coin_type ?? $this->coin_type),
            ],
            'fees' => [
                'value' => $this->fees,
                'formatted' => number_format($this->fees ?? 0, 8),
                'display' => ($this->fees ?? 0) . ' ' . ($this->coin->coin_type ?? $this->coin_type),
            ],
            'btc_value' => [
                'value' => $this->btc,
                'formatted' => number_format($this->btc ?? 0, 8),
                'display' => ($this->btc ?? 0) . ' BTC',
            ],
            'usd_value' => [
                'value' => $this->doller,
                'formatted' => number_format($this->doller ?? 0, 2),
                'display' => '$' . number_format($this->doller ?? 0, 2),
            ],
            'status' => [
                'value' => $this->status,
                'label' => $this->getStatusLabel(),
                'color' => $this->getStatusColor(),
            ],
            'confirmations' => [
                'current' => $this->confirmations ?? 0,
                'required' => $this->network?->block_confirmation ?? 6,
                'percentage' => $this->getConfirmationPercentage(),
                'is_confirmed' => $this->isConfirmed(),
            ],
            'coin' => $this->when($this->relationLoaded('coin') && $this->coin, function () {
                return [
                    'id' => $this->coin->id,
                    'name' => $this->coin->name,
                    'coin_type' => $this->coin->coin_type,
                    'icon' => $this->coin->coin_icon,
                    'decimal' => $this->coin->decimal,
                ];
            }),
            'network' => $this->when($this->relationLoaded('network') && $this->network, function () {
                return [
                    'id' => $this->network->id,
                    'name' => $this->network->name,
                    'slug' => $this->network->slug,
                    'chain_id' => $this->network->chain_id,
                    'block_confirmation' => $this->network->block_confirmation,
                ];
            }),
            'sender_wallet' => $this->when($this->relationLoaded('senderWallet') && $this->senderWallet, function () {
                return [
                    'id' => $this->senderWallet->id,
                    'name' => $this->senderWallet->name,
                    'balance' => $this->senderWallet->balance,
                ];
            }),
            'receiver_wallet' => $this->when($this->relationLoaded('receiverWallet') && $this->receiverWallet, function () {
                return [
                    'id' => $this->receiverWallet->id,
                    'name' => $this->receiverWallet->name,
                    'balance' => $this->receiverWallet->balance,
                ];
            }),
            'blockchain_info' => [
                'block_number' => $this->block_number,
                'network_type' => $this->network_type,
                'address_type' => $this->address_type,
            ],
            'admin_info' => [
                'is_admin_receive' => $this->is_admin_receive,
                'updated_by' => $this->updated_by,
                'notification_status' => $this->notification_status,
            ],
            'timestamps' => [
                'created_at' => $this->created_at?->toISOString(),
                'updated_at' => $this->updated_at?->toISOString(),
                'created_at_persian' => $this->created_at?->format('Y/m/d H:i:s'),
                'created_at_human' => $this->created_at?->diffForHumans(),
            ],
        ];
    }

    /**
     * Get status label in Persian
     */
    private function getStatusLabel(): string
    {
        return match ($this->status) {
            'pending' => 'در انتظار',
            'confirmed' => 'تأیید شده',
            'failed' => 'ناموفق',
            'cancelled' => 'لغو شده',
            'processing' => 'در حال پردازش',
            default => 'نامشخص',
        };
    }

    /**
     * Get status color for UI
     */
    private function getStatusColor(): string
    {
        return match ($this->status) {
            'pending' => 'warning',
            'confirmed' => 'success',
            'failed' => 'danger',
            'cancelled' => 'secondary',
            'processing' => 'info',
            default => 'light',
        };
    }

    /**
     * Calculate confirmation percentage
     */
    private function getConfirmationPercentage(): int
    {
        $required = $this->network?->block_confirmation ?? 6;
        $current = $this->confirmations ?? 0;

        if ($required <= 0) return 100;

        return min(100, round(($current / $required) * 100));
    }

    /**
     * Check if transaction is confirmed
     */
    private function isConfirmed(): bool
    {
        $required = $this->network?->block_confirmation ?? 6;
        $current = $this->confirmations ?? 0;

        return $current >= $required;
    }
}
