<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\RSAService;

class DecryptRSAInput
{
    protected $rsa;

    public function __construct(RSAService $rsa)
    {
        $this->rsa = $rsa;
    }

    public function handle(Request $request, Closure $next)
    {
        // بررسی اینکه آیا فیلد data وجود دارد یا نه
        if (!$request->has('data')) {
            return response()->json([
                'status' => false,
                'message' => 'داده‌های رمزنگاری شده الزامی است.',
                'error' => 'ENCRYPTED_DATA_REQUIRED'
            ], 400);
        }

        try {
            $decrypted = $this->rsa->decrypt($request->input('data'));

            // بررسی اینکه آیا داده‌های رمزگشایی شده معتبر هستند
            if (empty($decrypted) || !is_array($decrypted)) {
                return response()->json([
                    'status' => false,
                    'message' => 'داده‌های رمزگشایی شده نامعتبر است.',
                    'error' => 'INVALID_DECRYPTED_DATA'
                ], 400);
            }

            // تزریق داده‌های رمزگشایی شده به request
            $request->merge($decrypted);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'خطا در رمزگشایی داده‌های ورودی.',
                'error' => 'DECRYPTION_FAILED',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 400);
        }

        return $next($request);
    }
}