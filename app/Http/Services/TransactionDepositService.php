<?php
namespace App\Http\Services;

use Exception;
use App\Models\Coin;
use App\Models\Wallet;
use App\Models\Network;
use App\Models\CoinNetwork;
use App\Jobs\TransactionDeposit;
use App\Models\DepositeTransaction;
use App\Models\WalletAddressHistory;
use App\Services\Evm\EvmWalletService;

class TransactionDepositService
{
    public function __construct(){}

    public function getNetworks(): array
    {
        $networks = Network::whereNotIn("base_type", [1,2,3])->whereStatus(STATUS_ACTIVE)->get(["id", "name"]);
        return responseData(true, __("Networks get successfully"), $networks);
    }

    public function getCoinNetwork($request): array
    {
        if(!      isset($request->network_id)) return responseData(false, __("Network is required"));
        if(! is_numeric($request->network_id)) return responseData(false, __("Network is invalid"));

        $responseData = [];
        $coin_network = CoinNetwork::with("coin:id,name,coin_type")->where("network_id", $request->network_id)->get();

        $coin_network->map(function($coinNetwork) use(&$responseData) {
            $new = new \stdClass;
            $new->id = $coinNetwork->coin?->id;
            $new->name = $coinNetwork->coin?->name;
            $new->coin_type = $coinNetwork->coin?->coin_type;
            $new->network_id = $coinNetwork?->network_id;
            $responseData[] = $new;
        });

        return responseData(true, __("Network coins get successfully"), $responseData);
    }

    public function checkCoinTransactionAndDeposit($request): array
    {
        $coin = CoinNetwork::where(["currency_id" => $request->coin_id, "network_id" => $request->network_id])->first();

        // return if coin not exist
        if(! $coin) return responseData(false, __("Coin not found"));

        $evmService = new EvmWalletService();
        $getTransaction = $evmService->checkDepositByTx([
            'network' => $coin->network_id,
            'coin_network' => $coin->id,
            'transaction_id' => $request->trx_id
        ]);
        $data = [];
        if(isset($getTransaction["success"]) && $getTransaction["success"]){
            $transactionData = $getTransaction['data'];
            $data = [
                'coin_type' => $transactionData["coin_type"],
                'txId' => $transactionData["hash"],
                'confirmations' => 1,
                'amount' => $transactionData["amount"],
                'address' => $transactionData["toAddress"],
                'from_address' => $transactionData["fromAddress"],
                'coin_id' => $coin->currency_id,
                'network_id' => $coin->network_id
            ];

            $responseData = $data;
            $responseData["network"] = (Network::find($request->network_id))->name ?? __("Not found");

            $checkAddress = WalletAddressHistory::where(['address' => $data['address'], 'coin_type' => $data['coin_type']])->first();
            if ($checkAddress) {
                $wallet = Wallet::find($checkAddress->wallet_id);
                if ($wallet) {
                    if(!$deposit = DepositeTransaction::where("transaction_id", $data['txId'])->first()){
                        TransactionDeposit::dispatch($data)->onQueue("deposit");
                        return responseData(true, __("Transaction details found, System will adjust deposit soon"), $responseData);
                    }
                    return responseData(true, __("This transaction already deposited in our system"), $responseData);
                }
            }
            return responseData(true, __("Transaction details found but To address not match in system"), $responseData);
        } else if(
            !$getTransaction["success"] && isset($getTransaction["data"]) 
            && isset($getTransaction["data"]->msg_for_user) 
            && $getTransaction["data"]->msg_for_user
        ) {
            return responseData(false, $getTransaction["message"]);
        }

        return responseData(false, $getTransaction["message"] ?? __("Failed to get transaction"));
    }

    // public function checkAddressAndDeposit($data)
    // {
    //     try {
    //         storeException('checkAddressAndDeposit', json_encode($data));
    //         $checkAddress = WalletAddressHistory::where(['address' => $data['address'], 'coin_type' => $data['coin_type']])->first();
    //         if ($checkAddress) {
    //             $wallet = Wallet::find($checkAddress->wallet_id);
    //             if ($wallet) {
    //                 if(!$deposit = DepositeTransaction::where("transaction_id", $data['txId'])->first()){

    //                     storeException('checkAddressAndDeposit wallet ', json_encode($wallet));
    //                     $deposit = DepositeTransaction::create($this->depositData($data,$wallet));
    //                     storeException('checkAddressAndDeposit created ', json_encode($deposit));
    //                     storeException('checkAddressAndDeposit wallet balance before ', $wallet->balance);
    //                     $wallet->increment('balance',$data['amount']);
    //                     storeException('checkAddressAndDeposit wallet balance increment ', $wallet->balance);
    //                     storeException('checkAddressAndDeposit', ' wallet deposit successful');
    //                     $response = responseData(false,__('Wallet deposited successfully'));
    //                 }else{
    //                     storeException('checkAddressAndDeposit', ' already deposited');
    //                     $response = responseData(false,__('already deposited'));
    //                 }
    //             } else {
    //                 storeException('checkAddressAndDeposit', ' wallet not found');
    //                 $response = responseData(false,__('wallet not found'));
    //             }
    //         } else {
    //             storeException('checkAddressAndDeposit', $data['address'].' this address not found in db ');
    //             $response = responseData(false,__('This address not found in db the address is ').$data['address']);
    //         }
    //     } catch (Exception $e) {
    //         storeException('checkAddressAndDeposit', $e->getMessage());
    //         $response = responseData(false,$e->getMessage());
    //     }
    //     return $response;
    // }
// // deposit data
//     public function depositData($data,$wallet)
//     {
//         return [
//             'address' => $data['address'],
//             'from_address' => isset($data['from_address']) ? $data['from_address'] : "",
//             'receiver_wallet_id' => $wallet->id,
//             'address_type' => ADDRESS_TYPE_EXTERNAL,
//             'coin_type' => $wallet->coin_type,
//             'amount' => $data['amount'],
//             'transaction_id' => $data['txId'],
//             'status' => STATUS_SUCCESS,
//             'confirmations' => $data['confirmations']
//         ];
//     }

    // check deposit address
    public function checkAddressAndDeposit($data)
    {
        try {
            $checkDeposit = DepositeTransaction::where(['transaction_id' => $data['txId']])->first();
            if ($checkDeposit) {
                return responseData(false,__('Transaction already exist'));
            }
            storeException('checkAddressAndDeposit', json_encode($data));
            if(isset($data['network_id'])) {
                $checkAddress = WalletAddressHistory::where([
                    'address' => $data['address'],
                    'coin_type' => $data['coin_type'],
                    'network_id' => $data['network_id'],
                    'coin_id' => $data['coin_id']
                ])->first();
            } else {
                $checkAddress = WalletAddressHistory::where(['address' => $data['address'], 'coin_type' => $data['coin_type']])->first();
            }

            if ($checkAddress) {
                $wallet = Wallet::find($checkAddress->wallet_id);
                if ($wallet) {
                    storeException('checkAddressAndDeposit wallet ', json_encode($wallet));
                    $deposit = DepositeTransaction::create($this->depositData($data,$wallet));
                    storeException('checkAddressAndDeposit created ', json_encode($deposit));
                    storeException('checkAddressAndDeposit wallet balance before ', $wallet->balance);
                    $wallet->increment('balance',$data['amount']);
                    storeException('checkAddressAndDeposit wallet balance increment ', $wallet->balance);
                    storeException('checkAddressAndDeposit', ' wallet deposit successful');
                    $response = responseData(false,__('Wallet deposited successfully'));
                } else {
                    storeException('checkAddressAndDeposit', ' wallet not found');
                    $response = responseData(false,__('wallet not found'));
                }
            } else {
                storeException('checkAddressAndDeposit', $data['address'].' this address not found in db ');
                $response = responseData(false,__('This address not found in db the address is ').$data['address']);
            }
        } catch (\Exception $e) {
            storeException('checkAddressAndDeposit', $e->getMessage());
            $response = responseData(false,$e->getMessage());
        }
        return $response;
    }

    // deposit data
    public function depositData($data,$wallet)
    {
        return [
            'address' => $data['address'],
            'from_address' => isset($data['from_address']) ? $data['from_address'] : "",
            'receiver_wallet_id' => $wallet->id,
            'address_type' => ADDRESS_TYPE_EXTERNAL,
            'coin_type' => $wallet->coin_type,
            'amount' => $data['amount'],
            'transaction_id' => $data['txId'],
            'status' => STATUS_SUCCESS,
            'confirmations' => $data['confirmations'],
            'network_type' => isset($data['network_id']) ? $data['network_id'] : 0,
            'network_id' => isset($data['network_id']) ? $data['network_id'] : 0,
            'coin_id' => $wallet->coin_id,
        ];
    }
}
