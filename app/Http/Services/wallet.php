<?php

namespace App\Http\Services;

use App\Models\Coin;
use App\Models\WalletAddressHistory;

class wallet
{
  function AddWalletAddressHistory($wallet_id,$address,$coin_type,$wallet_key,$network,$public_key='',$memo='')
  {
      if(!empty($wallet_key)) {
          $wallet_key = STRONG_KEY.$address.$wallet_key;
      }
      $coin = Coin::where(['coin_type' => $coin_type])->first();
       WalletAddressHistory::firstOrCreate(
            [
                'user_id' => getUserId(),
                'wallet_id' => $wallet_id,
                'coin_type' => $coin_type
            ],[
                'coin_id' => $coin->id,
                'network_id' => $network,
                'address' => $address,
                'wallet_key' => $wallet_key,
                'public_key' => $public_key ?? '',
                'memo' => $memo ?? '',
            ]
        );
       
       return ['success'=>true];
}
}
