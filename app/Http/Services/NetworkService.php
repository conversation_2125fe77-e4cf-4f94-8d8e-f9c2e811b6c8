<?php
namespace App\Http\Services;

use App\Services\Evm\EvmWalletService;
use App\Models\Coin;
use App\Models\Network;
use App\Models\CoinNetwork;
use App\Models\NotifiedBlock;
use App\Models\SupportedNetwork;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NetworkService
{
    public function __construct()
    {
    }

    public function createNetworkProccess($request)
    {
        try {
            $finder = [
                'id' => $request->id ?? 0
            ];
            $network = new Network;
            $insertData = [
                "name" => $request->name,
                "description" => $request->description ?? "",
                "status" => isset($request->status)
            ];
            DB::beginTransaction();
            if (!isset($request->id) && isset($request->slug)) {
                if ($network = SupportedNetwork::whereSlug($request->slug)->first()) {
                    $insertData["slug"] = $network->slug;
                    $insertData["base_type"] = $network->type;
                    $insertData["block_confirmation"] = $request->block_confirmation;
                    $insertData["rpc_url"] = $request->rpc_url;
                    $insertData["wss_url"] = $request->wss_url ?? '';
                    $insertData["explorer_url"] = $request->explorer_url;
                    $insertData["chain_id"] = $network->chain_id;
                } else
                    return responseData(false, __('Supported network not found'));
            } else {
                if ($network = Network::find($request->id)) {
                    if(isset($request->block_number)){
                        if($block = NotifiedBlock::where("network_id", $network->id)->first()){
                            $block->update(["block_number" => is_numeric($request->block_number) ? $request->block_number : 0]);
                        }
                    }
                    $insertData["block_confirmation"] = $request->block_confirmation;
                    $insertData["rpc_url"] = $request->rpc_url;
                    $insertData["wss_url"] = $request->wss_url ?? '';
                    $insertData["explorer_url"] = $request->explorer_url;
                    $insertData["from_block_number"] = $request->from_block_number ? $request->from_block_number : 0;
                    $insertData["to_block_number"] = $request->to_block_number ? $request->to_block_number : 0;
                } else
                    return responseData(false, __("Network not found"));
            }

            if ($request->hasFile('logo')) {
                if (isset($request->id)) {
                    deleteFile(IMG_NETWORK_LOGO_PATH, $network->logo);
                    if ($logo = uploadFile($request->file('logo'), IMG_NETWORK_LOGO_PATH))
                        $insertData['logo'] = $logo;
                } else if ($logo = uploadFile($request->file('logo'), IMG_NETWORK_LOGO_PATH)) {
                    $insertData['logo'] = $logo;
                }
            }
            $network = Network::updateOrCreate($finder, $insertData);
            if ($network) {
                if (isset($request->id)) {
                    DB::commit();
                    return responseData(true, __("Network updated successfully"));
                } else {
                    NotifiedBlock::create(['network_id' => $network->id]);
                    DB::commit();
                    return responseData(true, __("Network created successfully"));
                }
            }
            if (isset($request->id))
                return responseData(true, __("Network failed to update"));
            return responseData(false, __("Network failed to create"));
        } catch (\Exception $e) {
            DB::rollBack();
            storeException("createNetworkProccess", $e->getMessage());
            return responseData(false, __("Something went wrong"));
        }
    }

    public function createCoinNetworkProccess($request)
    {
        try {
            if (!isset($request->uid) && !($coin = Coin::find($request->currency_id)))
                return responseData(false, __("Coin not found"));
            if (!isset($request->uid) && !($network = Network::find($request->network_id)))
                return responseData(false, __("Network not found"));

            if (
                !isset($request->uid) &&
                CoinNetwork::where(["network_id" => $request->network_id, "currency_id" => $request->currency_id])->first()
            )
                return responseData(false, __("Already exists same coin network"));

            if ($first_coin = CoinNetwork::where(["currency_id" => $request->currency_id])->first()) {
                if ($first_coin_network = Network::find($first_coin->network_id)) {
                    if (in_array($first_coin_network->base_type, [1, 2, 3]))
                        return responseData(false, __("This coin already exists in none evm base network, not assignable to evm base network"));

                    if (in_array($network->base_type, [1, 2, 3]))
                        return responseData(false, __("This coin already exists in evm base network, not assignable to none evm base network"));
                }
            }

            $finder['uid'] = $request->uid ?? uniqid() . time();
            $insertData["status"] = isset($request->status);
            $insertData["contract_address"] = $request->contract_address ?? "";
            if(isset($request->withdrawal_fees))
            $insertData["withdrawal_fees"] = $request->withdrawal_fees ?? "";
            $insertData["withdrawal_fees_type"] = $request->withdrawal_fees_type ?? 2;

            if (!isset($request->uid)) {
                if (isset($request->type) && $request->type == NATIVE_COIN) {
                    if (!($supportedNetwork = SupportedNetwork::where('slug', $network->slug)->first()))
                        return responseData(false, __("Supported network not found"));

                    if (($supportedNetwork->native_currency != $coin->coin_type) && !in_array($supportedNetwork->type, [COIN_PAYMENT , BITGO_API, BITCOIN_API]))
                        return responseData(false, __(":network network native currency must be :coin", [
                            "network" => $network->name,
                            "coin" => $supportedNetwork->native_currency,
                        ]));
                }

                $insertData = [
                    "status" => $insertData["status"],
                    "network_id" => $request->network_id,
                    "currency_id" => $request->currency_id,
                    "type" => $request->type,
                    "contract_address" => $insertData["contract_address"],
                    "withdrawal_fees" => $request->withdrawal_fees ?? "",
                    "withdrawal_fees_type" => $request->withdrawal_fees_type ?? 2
                ];
            }



            if (CoinNetwork::updateOrCreate($finder, $insertData)) {
                if (isset($request->id))
                    return responseData(true, __("Coin network updated successfully"));
                return responseData(true, __("Coin network created successfully"));
            }
            if (isset($request->id))
                return responseData(false, __("Coin network failed to update"));
            return responseData(false, __("Coin network failed to create"));
        } catch (\Exception $e) {
            storeException("createCoinNetworkProccess", $e->getMessage());
            return responseData(false, __("Something went wrong"));
        }
    }

    public function deleteNetwork(string $id): array
    {
        try {
            if ($network = Network::find($id)) {
                if ($coin_network = CoinNetwork::where('network_id', $id)->first())
                    return responseData(false, __("This network has been merged with coins in coin network, So delete action aborted"));

                $network->delete();
                return responseData(true, __("Network deleted successfully"));
            }
            return responseData(false, __("Network not found"));
        } catch (\Exception $e) {
            storeException("deleteNetwork", $e->getMessage());
            return responseData(false, __("Somthing went worng"));
        }
    }

    public function deleteCoinNetwork(int $id): array
    {
        try {
            if ($coin_network = CoinNetwork::find($id)) {
                $coin_network->delete();
                return responseData(true, __("Coin network deleted successfully"));
            }
            return responseData(false, __("Coin network not found"));
        } catch (\Exception $e) {
            storeException('deleteCoinNetwork', $e->getMessage());
            return responseData(false, __("Somthing went worng"));
        }
    }

    public function coinNetworkDelete(string $id): array
    {
        try {
            $id = decryptId($id);
            if($coin_network = CoinNetwork::find($id)){
                if($coin = Coin::find($coin_network->currency_id)){
                   $response = checkCoinDeleteCondition($coin);
                   if(isset($response['success']) && $response['success']){
                        $coin_network->delete();
                        return responseData(true, __("Coin network deleted successfully"));
                   }
                   return $response;
                }
                return responseData(false, __("Coin not found"));
            }
            return responseData(false, __("Coin network not found"));
        } catch (\Exception $e) {
            storeException('coinNetworkDelete', $e->getMessage());
            return responseData(false, __("Somthing wentWORng"));
        }
    }

    public function checkLatestBlock($request): array
    {
        try {
            $service = new EvmWalletService();
            if ($network = Network::find($request->id)) {
                if ($network->rpc_url) {
                    $response = $service->checkCurrentBlockNumber(['id'=> $request->id]);
                    return $response;
                } else {
                    return responseData(false, __("Please add the rpc url first"));
                }
                return responseData(true, __("Network deleted successfully"));
            }
            return responseData(false, __("Network not found"));
        } catch (\Exception $e) {
            storeException("deleteNetwork", $e->getMessage());
            return responseData(false, __("Somthing went worng"));
        }
    }
}
