<?php


namespace App\Http\Services;


use App\Models\Coin;
use App\Models\Network;
use App\Models\CoinNetwork;
use App\Models\DepositeTransaction;
use App\Services\Evm\EvmWalletService;
use Modules\IcoLaunchpad\Http\Services\ERC20TokenApiService;

class DepositService
{

    public function __construct()
    {
    }

    // check deposit by transaction
    public function checkDepositByHash($network,$coinNetwork,$hash,$type)
    {
        try {
            if(! ($network = Network::find($network)))
                return responseData(false,__('Network not found'));

            if(! ($coin_network = CoinNetwork::find($coinNetwork)))
                return responseData(false,__('Coin network not found'));


            $coin = null;
            if($network->base_type == EVM_BASE_COIN || $network->base_type == TRC20_TOKEN){
                $coin = Coin::find($coin_network->currency_id);
            }else{
                $coin = Coin::join('coin_settings','coin_settings.coin_id', '=', 'coins.id')
                        ->where(['coins.id' => $coin_network->currency_id])->first();
            }

            if (!$coin)  return responseData(false,__('Coin not found'));

            if ($network->base_type == BITGO_API)
                return $this->checkBitgoTransaction($network,$coin, $hash, $type);

            if(($network->base_type == EVM_BASE_COIN) || ($network->base_type ==TRC20_TOKEN))
                return $this->getTransactionInformation($network,$coin_network, $coin, $hash, $type);

            return responseData(false,__('Network is invalid'));
        } catch (\Exception $e) {
            storeException('checkDepositByHash',$e->getMessage());
            return responseData(false,$e->getMessage());
        }
    }

    // check bitgo transaction hash
    public function checkBitgoTransaction($network, $coin, $hash, $type)
    {
        try {
            $service = new WalletService();
            if (empty($coin->bitgo_wallet_id)) {
                $response = responseData(false,__('Bitgo wallet id is empty, please add it first'));
            } else {
                $checkHash = DepositeTransaction::where(['transaction_id' => $hash])->first();
                if (isset($checkHash)) {
                    $response = responseData(false,__('bitgoWalletCoinDeposit hash already in db'));
                } else {
                    $getTransaction = $service->getTransaction($coin->coin_type, $coin->bitgo_wallet_id, $hash);
                    if ($getTransaction['success'] == true) {
                        $bitgoService = new BitgoWalletService();
                        $transactionData = $getTransaction['data'];
                        if ($transactionData['type'] == 'receive' && $transactionData['state'] == 'confirmed') {
                            $coinVal = $bitgoService->getDepositDivisibilityValues($transactionData['coin']);
                            $amount = bcdiv($transactionData['value'],$coinVal,8);

                            $data = [
                                'coin_type' => $transactionData['coin'],
                                'txId' => $transactionData['txid'],
                                'confirmations' => $transactionData['confirmations'],
                                'amount' => $amount,
                                'network_id' => $network->id,
                                'coin_id' => $coin->id
                            ];

                            if (isset($transactionData['entries'][0])) {
                                foreach ($transactionData['entries'] as $entry) {
                                    if (isset($entry['wallet']) && ($entry['wallet'] == $transactionData['wallet'])) {
                                        $data['address'] = $entry['address'];
                                        storeException('entry address', $data['address']);
                                    }
                                }
                            }
                            if(isset($data['address'])) {
                                if ($type == CHECK_DEPOSIT) {
                                    $response = ['success' => true,'message' => __('Transaction found'), 'data' => $data];
                                } else {
                                    $response = $service->checkAddressAndDeposit($data);
                                }
                            } else {
                                $response = ['success' => false,'message' => __('No address found')];
                            }
                        } else {
                            $response = ['success' => false,'message' => __('The transaction type is not receive')];
                        }
                    } else {
                        $response = responseData(false,$getTransaction['message']);
                    }
                }
            }
        } catch (\Exception $e) {
            storeException('checkBitgoTransaction',$e->getMessage());
            $response = responseData(false,$e->getMessage());
        }
        return $response;
    }


    // check erc20 transaction hash
    public function checkERC20Transaction($coin, $hash, $type)
    {
        try {
            $service = new WalletService();
            if (empty($coin->chain_link)) {
                $response = responseData(false,__('Chain link is empty, please add it first'));
            } else {
                $checkHash = DepositeTransaction::where(['transaction_id' => $hash])->first();
                if (isset($checkHash)) {
                    $response = responseData(false,__('Transaction hash already in db'));
                } else {
                    $erc20Api = new ERC20TokenApi($coin);
                    $reqData = ['transaction_hash' => $hash,'contract_address' => $coin->contract_address];
                    $getTransaction = $erc20Api->getTransactionData($reqData);
                    // dd($getTransaction);
                    if ($getTransaction['success'] == true) {
                        $transactionData = $getTransaction['data'];
                        $data = [
                            'coin_type' => $coin->coin_type,
                            'txId' => $transactionData->txID,
                            'confirmations' => 1,
                            'amount' => $transactionData->amount,
                            'address' => $transactionData->toAddress,
                            'from_address' => $transactionData->fromAddress
                        ];

                        if ($type == CHECK_DEPOSIT) {
                            $response = ['success' => true,'message' => __('Transaction found'), 'data' => $data];
                        } else {
                            $response = $service->checkAddressAndDeposit($data);
                        }

                    } else {
                        $response = responseData(false,$getTransaction['message']);
                    }
                }
            }
        } catch (\Exception $e) {
            storeException('checkBitgoTransaction',$e->getMessage());
            $response = responseData(false,$e->getMessage());
        }
        return $response;
    }
    public function checkTRC20Transaction($coin, $hash, $type)
    {
        try {
            $service = new WalletService();
            if (empty($coin->chain_link)) {
                $response = responseData(false,__('Chain link is empty, please add it first'));
            } else {
                $checkHash = DepositeTransaction::where(['transaction_id' => $hash])->first();
                if (isset($checkHash)) {
                    $response = responseData(false,__('Transaction hash already in db'));
                } else {
                    $erc20Api = new ERC20TokenApi($coin);
                    $reqData = ['transaction_hash' => $hash,'contract_address' => $coin->contract_address];
                    $getTransaction = $erc20Api->getTrxTransaction($reqData);
                    if ($getTransaction['success'] == true) {
                        $transactionData = $getTransaction['data'];
                        $data = [
                            'coin_type' => $coin->coin_type,
                            'txId' => $transactionData->transaction,
                            'confirmations' => 1,
                            'amount' => ($transactionData->result->value / 1000000),
                            'address' => $transactionData->result->to,
                            'from_address' => $transactionData->result->from
                        ];

                        if ($type == CHECK_DEPOSIT) {
                            $response = ['success' => true,'message' => __('Transaction found'), 'data' => $data];
                        } else {
                            $response = $service->checkAddressAndDeposit($data);
                        }

                    } else {
                        $response = responseData(false,$getTransaction['message']);
                    }
                }
            }
        } catch (\Exception $e) {
            storeException('checkTRC20Transaction',$e->getMessage());
            $response = responseData(false,$e->getMessage());
        }
        return $response;
    }
    public function getTransactionInformation($network,$coin_network, $coin, $hash, $type)
    {
        try {
            $service = new WalletService();
            if (empty($network->rpc_url)) {
                $response = responseData(false,__('Chain link is empty, please add it first'));
            } else {
                $checkHash = DepositeTransaction::where(['transaction_id' => $hash])->first();
                if (isset($checkHash)) {
                    $response = responseData(false,__('Transaction hash already in db'));
                } else {
                    $getTransaction = (new EvmWalletService)->checkDepositByTx([
                        'network' => $network->id,
                        'coin_network' => $coin_network->id,
                        'transaction_id' => $hash
                    ]);
                    if (isset($getTransaction['success']) && $getTransaction['success']) {
                        $transactionData =(Object) $getTransaction['data'];
                        $data = [
                            'coin_type' => $coin->coin_type,
                            'txId' => $transactionData->hash,
                            'confirmations' => 1,
                            'amount' => $transactionData->amount,
                            'address' => $transactionData->address,
                            'from_address' => $transactionData->from,
                            'network_id' => $network->id,
                            'coin_id' => $coin->id
                        ];

                        if ($type == CHECK_DEPOSIT) {
                            $response = ['success' => true,'message' => __('Transaction found'), 'data' => $data];
                        } else {
                            $response = $service->checkAddressAndDeposit($data);
                        }

                    } else {
                        $response = responseData(false,$getTransaction['message']);
                    }
                }
            }
        } catch (\Exception $e) {
            storeException('checkTRC20Transaction',$e->getMessage());
            $response = responseData(false,$e->getMessage());
        }
        return $response;
    }

}
