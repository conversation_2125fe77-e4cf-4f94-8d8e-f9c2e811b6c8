<?php

namespace App\Http\Requests\User;

use App\Models\Card;
use App\Models\User;
use App\Rules\TomanTransactionRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use PragmaRX\Google2FA\Google2FA;

class TomanWithdrawalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'amount' => [
                'required',
                'numeric',
                'min:50000', // Minimum withdrawal amount (50,000 Toman)
                new TomanTransactionRule($this->amount, 'withdraw'),
            ],
            'card_id' => [
                'required',
                'integer',
                function ($attribute, $value, $fail) {
                    $card = Card::where('id', $value)
                        ->where('user_id', Auth::id())
                        ->where('status', 'approved')
                        ->first();

                    if (!$card) {
                        $fail('کارت بانکی معتبر نیست');
                    }
                },
            ],
        ];

        // Check if user has 2FA enabled, then require the code
        $user = Auth::user();
        if ($user && $user->g2f_enabled && !empty($user->google2fa_secret)) {
            $rules['code'] = [
                'required',
                'string',
                function ($attribute, $value, $fail) use ($user) {
                    $google2fa = new Google2FA();
                    $valid = $google2fa->verifyKey($user->google2fa_secret, $value);
                    if (!$valid) {
                        $fail('کد احراز هویت دو مرحله‌ای نامعتبر است');
                    }
                },
            ];
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'مبلغ برداشت الزامی است',
            'amount.numeric' => 'مبلغ برداشت باید عدد باشد',
            'amount.min' => 'حداقل مبلغ برداشت ۵۰,۰۰۰ تومان است',
            'card_id.required' => 'انتخاب کارت بانکی الزامی است',
            'card_id.integer' => 'کارت بانکی نامعتبر است',
            'code.required' => 'کد احراز هویت دو مرحله‌ای الزامی است',
            'code.string' => 'کد احراز هویت دو مرحله‌ای باید رشته باشد',
        ];
    }
}
