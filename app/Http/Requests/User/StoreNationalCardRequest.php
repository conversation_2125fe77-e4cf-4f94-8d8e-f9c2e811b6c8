<?php

namespace App\Http\Requests\User;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;

class StoreNationalCardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'front' => 'required|file|mimes:jpeg,png,jpg|max:1096',
            'back' => 'required|file|mimes:jpeg,png,jpg|max:1096',
        ];
    }

    public function messages(): array
    {
        return [
            'front.required' => 'تصویر روی کارت ملی ارزمی است',
            'front.file' => 'تصویر باید فایل باشد',
            'front.mimes' => 'فرمت نصویر باید jpeg٫ png یا jpg باشد.',
            'front.max' => 'حداکثر حجم فایل باید ۱ مگابایت باشد',

            'back.required' => 'تصویر پشت کارت ملی ارزمی است',
            'back.file' => 'تصویر باید فایل باشد',
            'back.mimes' => 'فرمت نصویر باید jpeg٫ png یا jpg باشد.',
            'back.max' => 'حداکثر حجم فایل باید ۱ مگابایت باشد',
        ];
    }

    public function prepareForValidation(): void {}

    public function failedValidation(Validator $validator)
    {
        // throw new HttpResponseException(response()->json([
        //     'success' => false,
        //     'message' => $validator->errors(),
        // ], 422));
        // dd($validator->errors());
        throw ValidationException::withMessages($validator->errors()->toArray());
    }
}
