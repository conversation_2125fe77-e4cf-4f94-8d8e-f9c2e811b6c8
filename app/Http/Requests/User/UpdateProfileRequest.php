<?php

namespace App\Http\Requests\User;

use App\Rules\ValidatePhoneWithNationalId;
use Illuminate\Foundation\Http\FormRequest;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // Check if we're only updating the national_id (partial update)
        $isPartialUpdate = $this->has('national_id') && count($this->all()) === 1;

        if ($isPartialUpdate) {
            return [
                'national_id' => ['required', 'digits:10', 'numeric'],
            ];
        }

        return [
            'firstname' => 'sometimes|required',
            'lastname' => 'sometimes|required',
            // 'national_id' => ['sometimes','required','digits:10','numeric',new ValidatePhoneWithNationalId($this->input('national_id'))],
            'national_id' => ['sometimes', 'required', 'digits:10', 'numeric'],
            'gender' => 'sometimes|required|in:male,female,undefined',
            'birth_date' => 'sometimes|required|date',
            'email' => 'sometimes|required|email',
        ];
    }
}
