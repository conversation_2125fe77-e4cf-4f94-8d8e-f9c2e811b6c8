<?php

namespace App\Http\Requests\User;

use App\Models\Wallet;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SwapCryptoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'from_currency_id' => 'required|exists:coins,id',
            'to_currency_id' => 'required|exists:coins,id|different:from_currency_id',
            'amount' => [
                'required',
                'numeric',
                'min:0.000001',
                function ($attribute, $value, $fail) {
                    $wallet = Wallet::where('user_id', Auth::id())
                        ->where('coin_id', $this->from_currency_id)
                        ->first();

                    if (!$wallet || $wallet->balance < $value) {
                        $currentBalance = $wallet ? $wallet->balance : 0;
                        $fail('موجودی ارز شما کافی نیست. موجودی فعلی: ' . number_format($currentBalance, 8));
                    }
                }
            ]
        ];
    }
}
