<?php

namespace App\Http\Requests\User\Accounting;

use App\Rules\WithdrawRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreWithdrawRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => ['numeric', 'required', 'min:50000', new WithdrawRule(amount: $this->input(key: 'amount'), card_id: $this->input(key: 'card_id'))],
            'card_id' => ['integer', 'required', 'exists:cards,id'],
        ];
    }
}
