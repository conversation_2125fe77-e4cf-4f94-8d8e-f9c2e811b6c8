<?php

namespace App\Http\Requests\User\Document;

use Illuminate\Foundation\Http\FormRequest;

class StoreDocumentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'file' => 'required|file|mimes:jpeg,png,jpg,webp,mp4,mp3,pdf|max:4096',
            // 'file' => 'required|file|mimes:'.config('file_types').'|max:'.config('max_file_size'),
            'name' => 'required|string|in:id,id_back,consent,selfie',
        ];
    }
}
