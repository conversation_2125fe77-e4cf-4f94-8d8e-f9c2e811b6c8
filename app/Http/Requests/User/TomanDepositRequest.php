<?php

namespace App\Http\Requests\User;

use App\Rules\TomanTransactionRule;
use Illuminate\Foundation\Http\FormRequest;

class TomanDepositRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => [
                'required',
                'numeric',
                'min:10000', // Minimum deposit amount (10,000 Toman)
                new TomanTransactionRule($this->amount, 'deposit'),
            ],
            'transaction_id' => 'required|string',
            'description' => 'nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'amount.required' => 'مبلغ واریز الزامی است',
            'amount.numeric' => 'مبلغ واریز باید عدد باشد',
            'amount.min' => 'حداقل مبلغ واریز ۱۰,۰۰۰ تومان است',
            'transaction_id.required' => 'شناسه تراکنش الزامی است',
            'transaction_id.string' => 'شناسه تراکنش باید متن باشد',
        ];
    }
}
