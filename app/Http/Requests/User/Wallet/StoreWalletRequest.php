<?php

namespace App\Http\Requests\User\Wallet;

use App\Rules\User\TransactionRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreWalletRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'currency_id' => 'required|exists:currencies,id',
            'type' => 'required|in:buy,sell',
            'amount' => ['required',new TransactionRule($this->input('amount'), $this->input('type'), $this->input('currency_id'))],
        ];
    }
}
