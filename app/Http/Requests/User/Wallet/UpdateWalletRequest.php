<?php

namespace App\Http\Requests\User\Wallet;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateWalletRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'to' => 'required|string',
            'amount' => 'required|numeric',
            'currency_id' => 'required|exists:currencies,id',
            'type' => ['in:trx,trc20','required_if:currency_id,5'],
        ];
    }

    public function messages()
    {
        return [
            'type.required_if' => 'برای ارز ترون انتخاب نوع الزامی است',
        ];
    }
}
