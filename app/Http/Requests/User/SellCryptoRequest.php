<?php

namespace App\Http\Requests\User;

use App\Models\Coin;
use App\Rules\User\TransactionRule;
use Illuminate\Foundation\Http\FormRequest;

class SellCryptoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'currency_id' => 'required|exists:coins,id',
            'amount' => [
                'required',
                'numeric',
                'min:0.000001',
                new TransactionRule($this->amount, 'sell', $this->currency_id)
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'currency_id.required' => 'انتخاب ارز الزامی است',
            'currency_id.exists' => 'ارز انتخاب شده معتبر نیست',
            'amount.required' => 'مقدار فروش الزامی است',
            'amount.numeric' => 'مقدار فروش باید عدد باشد',
            'amount.min' => 'حداقل مقدار فروش 0.000001 است',
        ];
    }
}
