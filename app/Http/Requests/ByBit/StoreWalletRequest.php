<?php

namespace App\Http\Requests\ByBit;

use Illuminate\Foundation\Http\FormRequest;

class StoreWalletRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'coin' => 'required|string', //	Coin, uppercase only
            'chainType' => 'required|string', //	Please use the value of chain from coin-info endpoint
        ];
    }
}
