<?php

namespace App\Http\Requests\Kucoin;

use Illuminate\Foundation\Http\FormRequest;

class StoreSubAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'password' => 'string|required|min:7|max:24',
            'remarks' => 'nullable|string|min:1|max:24',
            'subName' => 'string|required|min:7|max:32',
            'access' => 'string|required',
        ];
    }
}
