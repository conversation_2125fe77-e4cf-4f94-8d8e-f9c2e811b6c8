<?php

namespace App\Http\Requests\Kucoin;

use Illuminate\Foundation\Http\FormRequest;

class StoreAccountApiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'subName' => 'string|required', // Sub-account name, create sub account name of API Key.
            'passphrase' => 'string|required|min:7|max:32', // Password(Must contain 7-32 characters. Cannot contain any spaces.)
            'remark' => 'string|required|min:1|max:24', //	Remarks(1~24 characters)
            'permission' => 'string', // Permissions(Only General、Spot、Futures、Margin、InnerTransfer(Flex Transfer) permissions can be set, such as "General, Trade". The default is "General")
            'ipWhitelist' => 'string', // IP whitelist(You may add up to 20 IPs. Use a halfwidth comma to each IP)
            'expire' => 'string', // API expiration time; Never expire(default)-1，30Day30，90Day90，180Day180，360Day360
        ];
    }
}
