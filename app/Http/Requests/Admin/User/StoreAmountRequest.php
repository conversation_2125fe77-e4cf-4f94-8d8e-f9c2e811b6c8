<?php

namespace App\Http\Requests\Admin\User;

use App\Rules\User\AmountRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreAmountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|integer|exists:users,id',
            'type' => 'required|string|in:increase,decrease',
            'amount' => ['required', 'numeric', new AmountRule($this->input('amount'), $this->input('user_id'), $this->input('type'))],
        ];
    }
}
