<?php

namespace App\Http\Requests\Admin\Accounting;

use Illuminate\Foundation\Http\FormRequest;

class StoreSettlementRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'withdrawSettlementActive' => 'required|boolean',
            'sellSettlementActive' => 'required|boolean',
            'settlementMonthlyProfit' => 'required|integer',
            'quickSettlementLoss' => 'required|integer',
            'settlementService' => 'required|in:1,2,3,4',
            'settlementMaxDaysProfit' => 'required|integer',
            'settlementTodayProfit' => 'required|boolean',
        ];
    }
}
