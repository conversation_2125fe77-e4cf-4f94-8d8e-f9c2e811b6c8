<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class TransactionFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => 'string|nullable',
            'status' => 'string|nullable',
            'user_id' => 'exists:users,id|nullable',
            'currency_id' => 'exists:coins,id|nullable',
            'wallet_id' => 'exists:wallets,id|nullable',
            'from_date' => 'date|nullable',
            'to_date' => 'date|nullable',
            'amount_min' => 'numeric|nullable',
            'amount_max' => 'numeric|nullable',
            'order_by' => 'string|nullable',
            'order_direction' => 'in:asc,desc|nullable',
            'per_page' => 'integer|nullable',
            'page' => 'integer|nullable',
        ];
    }
}
