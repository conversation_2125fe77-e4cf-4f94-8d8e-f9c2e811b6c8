<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class DepositRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'network' => 'required|exists:networks,id',
            'coin_network' => 'required|exists:coin_networks,id',
            'transaction_id' => 'required|string',
            'type' => 'required|string'
        ];
    }
}