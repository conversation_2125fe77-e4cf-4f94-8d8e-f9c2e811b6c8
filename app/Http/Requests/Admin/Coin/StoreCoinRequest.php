<?php

namespace App\Http\Requests\Admin\Coin;

use Illuminate\Foundation\Http\FormRequest;

class StoreCoinRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'coin_type' => 'required|string|max:50|unique:coins,coin_type',
            'currency_type' => 'required|string',
            'network' => 'required|string',
            'decimal' => 'required|integer',
            'status' => 'required|boolean',
            'is_withdrawal' => 'required|boolean',
            'is_deposit' => 'required|boolean',
            'is_buy' => 'required|boolean',
            'is_sell' => 'required|boolean',
            'coin_icon' => 'nullable|string',
            'withdrawal_fees' => 'required|numeric',
            'withdrawal_fees_type' => 'required|string',
            'minimum_withdrawal' => 'required|numeric',
            'maximum_withdrawal' => 'required|numeric',
            'minimum_buy_amount' => 'required|numeric',
            'minimum_sell_amount' => 'required|numeric',
            'maximum_buy_amount' => 'required|numeric',
            'maximum_sell_amount' => 'required|numeric',
        ];
    }
}