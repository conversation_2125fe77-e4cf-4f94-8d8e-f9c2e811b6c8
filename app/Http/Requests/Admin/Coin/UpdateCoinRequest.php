<?php

namespace App\Http\Requests\Admin\Coin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCoinRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'coin_type' => 'required|string|max:50',
            'currency_type' => 'nullable|in:1,2',  // فقط مقادیر 1 و 2 مجاز هستند
            'network' => 'nullable|string|max:255',
            'decimal' => 'nullable|integer',
            'status' => 'nullable|boolean',
            'is_withdrawal' => 'nullable|boolean',
            'is_deposit' => 'nullable|boolean',
            'is_buy' => 'nullable|boolean',
            'is_sell' => 'nullable|boolean',
            'coin_icon' => 'nullable|image|mimes:jpeg,png,svg,jpg,gif|max:2048',
            'is_base' => 'nullable',
            'is_currency' => 'nullable',
            'is_primary' => 'nullable',
            'is_wallet' => 'nullable',
            'is_demo_trade' => 'nullable',
            'is_transferable' => 'nullable',
            'is_virtual_amount' => 'nullable',
            'trade_status' => 'nullable',
            'sign' => 'nullable|string|max:10',
            'minimum_buy_amount' => 'nullable|numeric|min:0',
            'minimum_sell_amount' => 'nullable|numeric|min:0',
            'minimum_withdrawal' => 'nullable|numeric|min:0',
            'maximum_withdrawal' => 'nullable|numeric|min:0',
            'maximum_buy_amount' => 'nullable|numeric|min:0',
            'maximum_sell_amount' => 'nullable|numeric|min:0',
            'max_send_limit' => 'nullable|numeric|min:0',
            'withdrawal_fees' => 'nullable|numeric|min:0',
            'withdrawal_fees_type' => 'nullable|integer',
            'coin_price' => 'nullable|numeric|min:0',
            'admin_approval' => 'nullable|boolean',
            'ico_id' => 'nullable|integer',
            'is_listed' => 'nullable|boolean',
            'last_block_number' => 'nullable|string',
            'last_timestamp' => 'nullable|string',
            'sync_rate_status' => 'nullable|boolean',
            'convert_status' => 'nullable|boolean',
            'min_convert_amount' => 'nullable|numeric|min:0',
            'max_convert_amount' => 'nullable|numeric|min:0',
            'convert_fee_type' => 'nullable|integer',
            'convert_fee' => 'nullable|numeric|min:0',
            'market_cap' => 'nullable|numeric|min:0',
        ];
    }

    public function messages()
    {
        return [
            'coin_type.unique' => 'Coin type must be unique',
            'coin_icon.image' => 'Coin icon must be an image',
            'coin_icon.mimes' => 'Coin icon must be a file of type: jpeg, png, jpg, gif',
            'coin_icon.max' => 'Coin icon may not be greater than 2048 kilobytes.',
            'withdrawal_fees_type.in' => 'Withdrawal fees type must be 1 or 2',
            'convert_fee_type.in' => 'Convert fee type must be 1 or 2',

        ];
    }
}
