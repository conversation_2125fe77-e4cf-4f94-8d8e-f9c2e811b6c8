<?php

namespace App\Http\Requests\Admin;

use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'phone' => 'required|integer|max_digits:11|unique:users,phone',
            'email' => 'email',
            'password' => 'string',
            'firstname' => 'string',
            'lastname' => 'string',
            'national_id' => [
                Rule::unique('users'),
                fn (string $attribute, string $value, Closure $fail) => $this->check_national_id($value) ? true : $fail('کد ملی وارد شده معتبر نیست'),
            ],
            'gender' => 'in:male,female,undefined',
            'birth_date' => 'date',
            'level' => '1',
            'status' => 'pending',
        ];
    }

    public function check_national_id(string $id): bool
    {
        if (preg_match('/^\d{10}$/', $id) && $id != str_repeat($id[0], 10)) {
            for ($i = 0, $sum = 0; $i < 9; $i++) {
                $sum += (10 - $i) * $id[$i];
            }
            $ret = $sum % 11;
            $parity = $id[9];
            if (($ret < 2 && $ret == $parity) || ($ret >= 2 && $ret == 11 - $parity)) {
                return true;
            }
        }

        return false;
    }
}
