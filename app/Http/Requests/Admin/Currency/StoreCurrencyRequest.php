<?php

namespace App\Http\Requests\Admin\Currency;

use Illuminate\Foundation\Http\FormRequest;

class StoreCurrencyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required',
            'code' => 'required|unique:currencies,code',
            'crypto' => 'boolean',
            'apiKey' => 'required',
            'needs_approval' => 'nullable',
            'icon' => 'nullable',
            'provider' => 'nullable',
            'buy' => 'nullable',
            'sell' => 'nullable',
            'active' => 'nullable',
        ];
    }
}
