<?php

namespace App\Http\Requests\Admin\Setting;

use Illuminate\Foundation\Http\FormRequest;

class StoreSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            '*.code' => 'string',
            '*.value' => 'nullable',
            '*.status' => 'boolean',
            '*.title' => 'string|nullable',
            '*.type' => 'string',
            '*.group_id' => 'nullable|exists:admin_setting_groups,id',
        ];
    }
}
