<?php

namespace App\Http\Requests\Admin\Support;

use Illuminate\Foundation\Http\FormRequest;

class StoreSupportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'subject' => 'required',
            'unit_id' => 'required|exists:support_units,id',
            'level_id' => 'required|exists:support_levels,id',
            'user_id' => 'required|exists:users,id',
            'message' => 'required|string',
            'file' => 'nullable|file|mimes:jpeg,png,jpg,webp,mp4,mp3,pdf|max:4096',
        ];
    }
}
