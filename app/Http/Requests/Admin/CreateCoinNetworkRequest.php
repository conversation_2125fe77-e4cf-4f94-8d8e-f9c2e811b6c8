<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CreateCoinNetworkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'currency_id' => 'required|exists:coins,id',
            'network_id' => 'required|exists:networks,id',
            'type' => 'required|in:1,2',
            'contract_address' => 'nullable|string',
            'withdrawal_fees' => 'nullable|numeric',
            'withdrawal_fees_type' => 'nullable|in:1,2',
            'status' => 'nullable',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'currency_id.required' => __('The coin field is required.'),
            'currency_id.exists' => __('The selected coin is invalid.'),
            'network_id.required' => __('The network field is required.'),
            'network_id.exists' => __('The selected network is invalid.'),
            'type.required' => __('The type field is required.'),
            'type.in' => __('The selected type is invalid.'),
            'contract_address.string' => __('The contract address must be a string.'),
            'withdrawal_fees.numeric' => __('The withdrawal fees must be a number.'),
            'withdrawal_fees_type.in' => __('The selected withdrawal fees type is invalid.'),
        ];
    }
}
