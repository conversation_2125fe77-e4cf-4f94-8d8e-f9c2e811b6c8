<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CreateNetworkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'block_confirmation' => 'nullable|numeric',
            'base_type' => 'nullable|string',
            'rpc_url' => 'nullable|string',
            'wss_url' => 'nullable|string',
            'explorer_url' => 'nullable|string',
            'chain_id' => 'nullable|string',
            'status' => 'nullable',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => __('The name field is required.'),
            'name.string' => __('The name must be a string.'),
            'name.max' => __('The name may not be greater than :max characters.'),
            'slug.string' => __('The slug must be a string.'),
            'slug.max' => __('The slug may not be greater than :max characters.'),
            'description.string' => __('The description must be a string.'),
            'block_confirmation.numeric' => __('The block confirmation must be a number.'),
            'base_type.string' => __('The base type must be a string.'),
            'rpc_url.string' => __('The RPC URL must be a string.'),
            'wss_url.string' => __('The WSS URL must be a string.'),
            'explorer_url.string' => __('The explorer URL must be a string.'),
            'chain_id.string' => __('The chain ID must be a string.'),
            'logo.image' => __('The logo must be an image.'),
            'logo.mimes' => __('The logo must be a file of type: :values.'),
            'logo.max' => __('The logo may not be greater than :max kilobytes.'),
        ];
    }
}
