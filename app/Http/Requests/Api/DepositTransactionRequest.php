<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class DepositTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => 'nullable|string|in:pending,confirmed,failed,cancelled',
            'coin_type' => 'nullable|string|max:10',
            'coin_id' => 'nullable|integer|exists:coins,id',
            'network_id' => 'nullable|integer|exists:networks,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'per_page' => 'nullable|integer|min:1|max:100',
            'page' => 'nullable|integer|min:1',
            'sort_by' => 'nullable|string|in:created_at,amount,status,confirmations',
            'sort_direction' => 'nullable|string|in:asc,desc',
            'min_amount' => 'nullable|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0|gte:min_amount',
            'transaction_id' => 'nullable|string|max:255',
            'from_address' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'status.in' => 'وضعیت باید یکی از مقادیر pending, confirmed, failed, cancelled باشد',
            'coin_id.exists' => 'کوین انتخاب شده معتبر نیست',
            'network_id.exists' => 'شبکه انتخاب شده معتبر نیست',
            'end_date.after_or_equal' => 'تاریخ پایان باید بعد از تاریخ شروع باشد',
            'per_page.max' => 'حداکثر تعداد آیتم در هر صفحه 100 است',
            'sort_by.in' => 'فیلد مرتب‌سازی باید یکی از مقادیر created_at, amount, status, confirmations باشد',
            'sort_direction.in' => 'جهت مرتب‌سازی باید asc یا desc باشد',
            'max_amount.gte' => 'حداکثر مبلغ باید بزرگتر یا مساوی حداقل مبلغ باشد',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'status' => 'وضعیت',
            'coin_type' => 'نوع کوین',
            'coin_id' => 'کوین',
            'network_id' => 'شبکه',
            'start_date' => 'تاریخ شروع',
            'end_date' => 'تاریخ پایان',
            'per_page' => 'تعداد در صفحه',
            'page' => 'شماره صفحه',
            'sort_by' => 'مرتب‌سازی بر اساس',
            'sort_direction' => 'جهت مرتب‌سازی',
            'min_amount' => 'حداقل مبلغ',
            'max_amount' => 'حداکثر مبلغ',
            'transaction_id' => 'شناسه تراکنش',
            'from_address' => 'آدرس فرستنده',
            'address' => 'آدرس',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // تنظیم مقادیر پیش‌فرض
        $this->merge([
            'per_page' => $this->per_page ?? 15,
            'page' => $this->page ?? 1,
            'sort_by' => $this->sort_by ?? 'created_at',
            'sort_direction' => $this->sort_direction ?? 'desc',
        ]);
    }
}
