<?php

// namespace App\CurrencyProviders;

// use App\Contracts\CurrencyProvider;
// use App\Models\Transaction;
// use App\Models\UserWallet;
// use Illuminate\Support\Facades\Auth;
// use Mollsoft\LaravelTronModule\Api\TRC20Contract;
// use Mollsoft\LaravelTronModule\Facades\Tron;
// use Mollsoft\LaravelTronModule\Models\TronWallet;
// use Mollsoft\LaravelTronModule\Models\TronAddress;
// use Mollsoft\LaravelTronModule\Models\TronTRC20;
// use Illuminate\Support\Facades\DB;
// use Illuminate\Validation\ValidationException;

// class TronProviderOld implements CurrencyProvider
// {

//     // protected $walletId;

//     public function __construct(
//         // $walletId
//     ){
//         // $this->walletId = $walletId;
//     }

//     public function buy(array $data): array|false
//     {
//         // $userId = $data['user_id'];
//         // $wallet = $this->wallet($userId);

//         // $address = TronAddress::where('wallet_id',1)->first(); // website primary tron address

//         // $to = $this->address($wallet, $userId);

//         // $amount = $data['amount']; // TODO
//         // try {
//         //     DB::beginTransaction();

//         //     $this->wesite_transfer($data);

//         //     Tron::transfer($address, $to, $amount);

//         //     DB::commit();
//         // } catch (\Exception $e) {
//         //     report($e);

//         //     DB::rollBack();
//         // }
//         return [];
//     }

//     public function sell(array $data): array|false
//     {
//         // $userId = $data['user_id'];
//         // $wallet = $this->wallet($userId);

//         // $address = $this->address($wallet, $userId);

//         // $to = TronAddress::firstWhere('wallet_id',1); // website primary tron address

//         // $amount = $data['amount']; // TODO
//         // try {
//         //     DB::beginTransaction();

//         //     $this->wesite_transfer($data);

//         //     Tron::transfer($address, $to, $amount);

//         //     DB::commit();
//         // } catch (\Exception $e) {
//         //     report($e);

//         //     DB::rollBack();
//         // }
//         return [];
//     }

//     public function balance(): float
//     {
//         $user = Auth::user();
//         $wallet = $this->wallet($user->id);
//         return (float)$wallet->balance;
//     }

//     public function deposit(array $data): array|false
//     {
//         $userId = $data['user_id'];
//         $wallet = $this->wallet($userId);

//         $address = $this->address($wallet, $userId);

//         return $address->toArray();
//     }

//     public function withdraw(array $data): array|false
//     {
//         $userId = $data['user_id'];
//         $wallet = $this->wallet($userId);

//         $address = $this->address($wallet, $userId);

//         $to = $data['to'];
//         $amount = round((float)$data['amount'],0);
//         // if($amount < round((float)$address->balance,0)){
//             if($data['type'] == 'trc20'){
//                 $contractAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'; // Contract Address of Tether USDT
//                 // $contractAddress = $address->address; // Contract Address of Tether USDT
//                 $tronTRC20 = TronTRC20::where('address',operator: $contractAddress)->first();
//                 if(!$tronTRC20) {
//                     $tronTRC20 = Tron::createTRC20($contractAddress);
//                     $tronTRC20->save();
//                 }
//                 $transferTRC20 = Tron::transferTRC20(
//                     trc20: $tronTRC20,
//                     from: $address,
//                     to: $to,
//                     amount: $amount
//                 );
//                 // $transferTRC20 = Tron::transferTRC20All($tronTRC20,$address, $to);
//                 return ["TXID: {$transferTRC20->txid}"];
//             } else {
//                 $transfer = Tron::transfer($address, $to, $amount);
//                 return ["TXID: {$transfer->txid}"];
//             }
//         // } else {
//         //     throw ValidationException::withMessages(['Insufficient balance']);
//         // }

//     }

//     public function wallet($userId){
//         // $userWallet = UserWallet::where('user_id', $userId)->where('currency_id',5)->first();
//         // if(!$userWallet){
//         //     $mnemonic = Tron::mnemonicGenerate();
//         //     $wallet = Tron::createWallet("user-$userId-wallet_name", $mnemonic);
//         //     $contractAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'; // Contract Address of Tether USDT
//         //     $tronTRC20 = TronTRC20::where('address',operator: $contractAddress)->first();
//         //     if(!$tronTRC20) {
//         //         $tronTRC20 = Tron::createTRC20($contractAddress);
//         //         $tronTRC20->save();
//         //     }

//         //     UserWallet::create([
//         //         'user_id' => $userId,
//         //         'wallet_id' => $wallet->id,
//         //         'currency_id' => 5,
//         //     ]);
//         // } else {
//         //     $wallet = TronWallet::findOrFail($userWallet->wallet_id);
//         //     // $contractAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'; // Contract Address of Tether USDT
//         //     // $tronTRC20 = TronTRC20::where('address',$contractAddress)->first();
//         //     // if(!$tronTRC20) {
//         //     //     $tronTRC20 = Tron::createTRC20($contractAddress);
//         //     //     $tronTRC20->save();
//         //     // }

//         // }
//         // return $wallet;
//     }

//     public function address($wallet, $userId){
//         $address = TronAddress::where('wallet_id',$wallet->id)->first();
//         if(!$address){
//             $address = Tron::createAddress($wallet, "user-$userId-primary_address_name");
//         }
//         return $address;
//     }

//     private function wesite_transfer($data){
//         Transaction::create([
//             'user_id' => Auth::user()->id,
//             'amount' => $data['amount'], // TODO
//             'currency_id' => 5,
//             'type' => $data['type'],
//             'status' => 'pending', // TODO
//             'registrar' => null,
//         ]);
//         Transaction::create([
//             'user_id' => Auth::user()->id,
//             'amount' => $data['amount'],
//             'currency_id' => 3,
//             'type' => $data['type'] == 'buy' ? 'decrease' : 'increase',
//             'status' => $data['type'] == 'buy' ? 'done' : 'pending',
//             'registrar' => null,
//         ]);

//     }

// }
