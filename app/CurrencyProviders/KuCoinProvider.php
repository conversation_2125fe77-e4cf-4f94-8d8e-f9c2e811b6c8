<?php

namespace App\CurrencyProviders;

use App\Contracts\CurrencyProvider;
use App\Models\Currency;
use KuCoin\SDK\Auth;
use KuCoin\SDK\Exceptions\BusinessException;
use KuCoin\SDK\Exceptions\InvalidApiUriException;
use KuCoin\SDK\Http\GuzzleHttp;
use KuCoin\SDK\KuCoinApi;
use KuCoin\SDK\PrivateApi\Account;
use <PERSON><PERSON>oin\SDK\PrivateApi\Deposit;
use KuCoin\SDK\PrivateApi\Order;

class KuCoinProvider implements CurrencyProvider
{
    protected string $api_key;

    protected string $api_passphrase;

    protected string $api_secret;

    protected int $api_version;

    public function __construct(
        protected Deposit $deposit,
        protected Account $account,
    ) {
        KuCoinApi::setBaseUri('https://api.kucoin.com');
        KuCoinApi::setDebugMode(true);
        $this->api_key = $this->api_key ?? config('kucoin.api-key');
        $this->api_passphrase = $this->api_passphrase ?? config('kucoin.api-passphrase');
        $this->api_secret = $this->api_secret ?? config('kucoin.api-secret');
        $this->api_version = $this->api_version ?? config('kucoin.api-key-version');
    }

    public function auth(): Auth
    {
        return new Auth($this->api_key, $this->api_secret, $this->api_passphrase, $this->api_version);
    }

    public function balance(): float
    {
        // TODO: Implement it
        return 0.0;
    }

    protected function marketOrder(string $side, string $symbol, float $amount, string $comment = '', ?string $id = null): ?array
    {
        $api = new Order($this->auth(), new GuzzleHttp);
        $order = [
            'clientOid' => $id ?? uuid_create(),
            'size' => $amount,
            'symbol' => $symbol.'-USDT',
            'type' => 'market',
            'side' => $side,
            'remark' => $comment,
        ];
        try {
            return $api->create($order);
        } catch (InvalidApiUriException $e) {
            return null;
        }
    }

    public function buy(array $data): array
    {
        $currency = Currency::find($data['currency_id'])->code;

        return $this->marketOrder('buy', $currency, $data['amount'], 'ORDER#'.$data['wallet_id']);
    }

    public function sell(array $data): array
    {
        $currency = Currency::find($data['currency_id'])->code;

        return $this->marketOrder('sell', $currency, $data['amount'], 'ORDER#'.$data['wallet_id']);
    }

    public function addresses(array $data): array
    {
        $api = new Deposit($this->auth(), new GuzzleHttp);
        try {
            return $api->getAddresses('USDT');
        } catch (InvalidApiUriException $e) {
            return [$e];
        }

    }

    public function address(array $data): array
    {
        $api = new Deposit($this->auth(), new GuzzleHttp);
        try {
            return $api->getAddress('USDT', 'ERC20');
        } catch (InvalidApiUriException $e) {
            return [$e];
        }

    }

    public function deposit(array $data): array
    {
        $api = new Deposit($this->auth(), new GuzzleHttp);
        try {
            return $api->createAddress($data['currency'], $data['chain']);
        } catch (InvalidApiUriException $e) {
            return ['error' => $e->getMessage()];
        } catch (BusinessException $e) {
            return [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function withdraw(array $data): array
    {
        return [];
    }

    public function getSubAccounts()
    {
        $api = new Account($this->auth(), new GuzzleHttp);
        try {
            return $api->getSubUserV2();
        } catch (InvalidApiUriException $e) {
            return [$e];
        }
    }

    public function createSubAccount(array $params)
    {
        $api = new Account($this->auth(), new GuzzleHttp);
        try {
            return $api->createSubUserV2($params);
        } catch (InvalidApiUriException $e) {
            return [$e];
        }
    }

    public function createSubAccountApi(array $params)
    {
        $api = new Account($this->auth(), new GuzzleHttp);
        try {
            return $api->createSubUserApiKey($params);
        } catch (InvalidApiUriException $e) {
            return [$api];
        }
    }

    public function getSubAccountApi(array $params)
    {
        $api = new Account($this->auth(), new GuzzleHttp);
        try {
            return $api->getSubUserApiKey($params);
        } catch (InvalidApiUriException $e) {
            return [$e];
        }
    }
}
