<?php

namespace App\CurrencyProviders;

use GuzzleHttp\Client;
use Illuminate\Validation\ValidationException;

class ByBitProvider
{
    public function postRequest($body, $endpoint)
    {
        $client = new Client;
        $address = env('BYBIT_API_ADDRESS').$endpoint;

        $response = $client->post($address, [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-BAPI-SIGN' => $this->generateSignature(json_encode($body, true)),
                'X-BAPI-API-KEY' => env('BYBIT_API_KEY'),
                'X-BAPI-TIMESTAMP' => time() * 1000,
            ],
            'body' => json_encode($body),
        ]);
        $return = json_decode($response->getBody(), true);
        if ($return['retCode'] != 0) {
            throw ValidationException::withMessages([$return['retMsg']]);
        }

        return $return;
    }

    public function getRequest($body, $endpoint)
    {
        $client = new Client;
        $address = env('BYBIT_API_ADDRESS').$endpoint;
        $query = http_build_query($body);
        $response = $client->get($address.'?'.$query, [
            'headers' => [
                'Content-Type' => 'application/json',
                'X-BAPI-SIGN' => $this->generateSignature($query),
                'X-BAPI-API-KEY' => env('BYBIT_API_KEY'),
                'X-BAPI-TIMESTAMP' => time() * 1000,
            ],
        ]);
        $return = json_decode($response->getBody(), true);
        if ($return['retCode'] != 0) {
            throw ValidationException::withMessages([$return['retMsg']]);
        }

        return $return;
    }

    public function generateSignature($body)
    {
        $api_key = env('BYBIT_API_KEY');
        $api_secret = env('BYBIT_API_SECRET');
        $json = time() * 1000 .$api_key.$body;
        $hash = hash_hmac('sha256', $json, $api_secret);

        return $hash;
    }
}
