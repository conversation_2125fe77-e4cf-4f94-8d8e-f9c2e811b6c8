<?php

namespace App\CurrencyProviders;

use App\Contracts\CurrencyProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

use App\Models\UserWallet;
use Illuminate\Support\Facades\Log;
use PavloDotDev\LaravelTronModule\Api\Api;
use PavloDotDev\LaravelTronModule\Models\TronAddress;
use PavloDotDev\LaravelTronModule\Models\TronTRC20;
use PavloDotDev\LaravelTronModule\Models\TronWallet;
use PavloDotDev\LaravelTronModule\Tron;
use Illuminate\Validation\ValidationException;

class NowNodes implements CurrencyProvider
{

    protected $api_key;

    // protected $walletId;

    public function __construct(
        // $walletId
    ){
        $this->api_key = env(key: 'NOW_NODES_API');
    }

    public function buy(array $data): array|false
    {
        return [];
    }

    public function sell(array $data): array|false
    {
        return [];
    }

    public function balance(): float
    {
        return (float)1;
    }

    public function deposit(array $data): array|false
    {
        return [];
    }

    public function withdraw(array $data): array|false
    {
        return [];
    }

    public function wallet($node){
        $userWallet = UserWallet::where('currency_id',$node->id)
            ->where('user_id',Auth::user()->id)
            ->first();
        $address = $userWallet ? "createaccount" : "getaccount";

        $apiUrl = "https://{$node->code}.trongrid.io/v1/wallet/{$address}";

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$this->api_key}", // Authorization with Bearer token
            'Content-Type' => 'application/json', // If required
        ])->get($apiUrl);

        if ($response->successful()) {
            return $response->json();
            // Do something with the response data
        } else {
            // Handle error
            return $response->body();
        }

    }

}
