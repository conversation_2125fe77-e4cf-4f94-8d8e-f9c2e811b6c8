<?php

namespace App\CurrencyProviders;

use App\Contracts\CurrencyProvider;
use Exception;

class PerfectMoneyVoucher implements CurrencyProvider
{
    protected $account_id;

    /**
     * @var string
     */
    protected $passphrase;

    /**
     * @var string
     */
    protected $alt_passphrase;

    /**
     * @var string
     */
    protected $marchant_name;

    /**
     * @var string
     */
    protected $marchant_id;

    protected $payment_url;

    protected $payment_url_method;

    protected $nopayment_url;

    protected $nopayment_url_method;

    protected $status_url;

    protected $suggested_memo;

    protected $units;

    protected $url;

    /**
     * @var array
     */
    protected $ssl_fix = ['ssl' => ['verify_peer' => false, 'verify_peer_name' => false]];

    protected mixed $payee_account;

    public function __construct()
    {
        $this->account_id = config('perfectmoney.account_id');
        $this->payee_account = config('perfectmoney.payee_account');
        $this->passphrase = config('perfectmoney.passphrase');
        $this->alt_passphrase = config('perfectmoney.alternate_passphrase');
        $this->marchant_name = config('perfectmoney.marchant_name');
        $this->marchant_id = config('perfectmoney.marchant_id');
        $this->payment_url = config('perfectmoney.payment_url');
        $this->nopayment_url = config('perfectmoney.nopayment_url');
        $this->payment_url_method = config('perfectmoney.payment_url_method');
        $this->nopayment_url_method = config('perfectmoney.nopayment_url_method');
        $this->status_url = config('perfectmoney.status_url');
        $this->suggested_memo = config('perfectmoney.suggested_memo');
        $this->units = config('perfectmoney.units');
        $this->url = config('perfectmoney.url');
    }

    /**
     * Fetch the public name of another existing PerfectMoney account
     */
    public function account($account)
    {
        // trying to open URL to process PerfectMoney getAccountName request
        $data = file_get_contents("https://perfectmoney.com/acct/acc_name.asp?AccountID={$this->account_id}&PassPhrase={$this->passphrase}&Account={$account}", false, stream_context_create($this->ssl_fix)
        );

        if ($data == 'ERROR: Can not login with passed AccountID and PassPhrase') {

            throw new Exception('Invalid PerfectMoney Username or Password.', 500);
        } elseif ($data == 'ERROR: Invalid Account') {

            throw new Exception('Invalid PerfectMoney Account specified.', 500);
        }

        return $data;
    }

    /**
     * get the balance for the wallet or a specific account inside a wallet
     *
     * @throws Exception
     */
    public function balance(): float
    {
        $info = $this->request(
            'https://perfectmoney.com/acct/balance.asp',
            [
                'AccountID' => $this->account_id,
                'PassPhrase' => $this->passphrase,
            ]
        );

        return $info[$this->marchant_id] ?? 0;
    }

    /**
     * Transfer funds(currency) to another existing PerfectMoney account
     */
    public function transferFund($fromAccount, $toAccount, $amount, $paymentID = null, $memo = null)
    {
        $urlString = "https://perfectmoney.com/acct/confirm.asp?AccountID={$this->account_id}&PassPhrase={$this->passphrase}&Payer_Account={$fromAccount}&Payee_Account={$toAccount}&Amount={$amount}&PAY_IN=1";

        $urlString .= ($paymentID != null) ? "&PAYMENT_ID={$paymentID}" : '';

        $urlString .= ($paymentID != null) ? "&Memo={$memo}" : '';

        // trying to open URL to process PerfectMoney Balance request
        $data = file_get_contents($urlString, false, stream_context_create($this->ssl_fix));

        // searching for hidden fields
        if (! preg_match_all("/<input name='(.*)' type='hidden' value='(.*)'>/", $data, $result, PREG_SET_ORDER)) {
            return false;
        }

        // putting data to array
        $array = [];

        foreach ($result as $item) {
            $array[$item[1]] = $item[2];
        }

        return $array;
    }

    /**
     * Create new E-Voucher with your PerfectMoney account
     *
     * @throws Exception
     */
    public function buy(array $data): array
    {
        $result = $this->request(
            '/acct/ev_create.asp',
            [
                'AccountID' => $this->account_id,
                'PassPhrase' => $this->passphrase,
                'Payer_Account' => $this->payee_account,
                'Amount' => $data['amount'],
            ]
        );

        return $result;
    }

    public function sell(array $data): array
    {
        // trying to open URL to process PerfectMoney Balance request
        $result = $this->request(
            '/acct/ev_activate.asp',
            [
                'AccountID' => $this->account_id,
                'PassPhrase' => $this->passphrase,
                'Payee_Account' => $this->payee_account,
                'ev_number' => $data['ev_number'],
                'ev_code' => $data['ev_activation'],
            ]
        );
        if (isset($data['VOUCHER_AMOUNT'])) {
            $data['amount'] = $data['VOUCHER_AMOUNT'];

            return $data;
        } else {
            throw new Exception('Provided voucher is invalid.', 401);
        }
    }

    public function deposit(array $data): array
    {
        return [];
    }

    public function withdraw(array $data): array
    {
        return [];
    }

    /**
     * @throws Exception
     */
    protected function request(string $url, array $params = []): array
    {
        $query = http_build_query($params);
        $data = file_get_contents("https://perfectmoney.com$url?$query", false, stream_context_create($this->ssl_fix));
        if (! preg_match_all("/<input name='(.*)' type='hidden' value='(.*)'>/", $data, $result, PREG_SET_ORDER)) {
            throw new Exception('PerfectMoney is not available at the moment.', 504);
        }
        $info = [];
        foreach ($result as $item) {
            $info[$item[1]] = $item[2];
        }

        return $info;
    }
}
