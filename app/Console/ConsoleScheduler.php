<?php

namespace App\Console;

use <PERSON><PERSON>\ShortSchedule\ShortSchedule;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Artisan;

class ConsoleScheduler extends ConsoleKernel
{
    protected $commands = [
        Commands\CustomTokenDeposit::class,
        Commands\TokenDepositCommand::class,
        Commands\AdjustCustomTokenDeposit::class,
        Commands\BotOrderRemoveCommand::class,
        Commands\UpdateCoinUsdRate::class,
        Commands\StakingInvestmentReturn::class,
        \JoeDixon\Translation\Console\Commands\SynchroniseMissingTranslationKeys::class,
        Commands\ExistingWalletAddress::class,
        Commands\UpdateSystemWallet::class,
        Commands\ClearFailedJob::class,
        Commands\TokenBlockDepositCommand::class,
        Commands\SendDepositNotifications::class,
    ];

    protected function schedule(Schedule $schedule)
    {
        $setting = settings([
            'cron_coin_rate_status','cron_coin_rate',
            'cron_token_deposit_status','cron_token_deposit',
            'cron_token_deposit_adjust','cron_token_adjust_deposit_status',
        ]);

        $coinRate = $setting['cron_coin_rate'] ?? 10;
        $tokenDiposit = $setting['cron_token_deposit'] ?? 10;
        $tokenDepositAdjust = $setting['cron_token_deposit_adjust'] ?? 20;

        // Coin rate update schedule
        if(isset($setting['cron_coin_rate_status']) && $setting['cron_coin_rate_status'] == STATUS_ACTIVE) {
            // Using everyTenSeconds() instead of cron expression for more frequent updates
            $schedule->command('update-coin-usd-rate')->everyTenSeconds();
        }

        // Token deposit schedules
        if(isset($setting['cron_token_deposit_status']) && $setting['cron_token_deposit_status'] == STATUS_ACTIVE) {
            $schedule->command('token-block-deposit-command')->cron('*/' . $tokenDiposit . ' * * * *');
            $schedule->command('command:erc20token-deposit')->cron('*/' . $tokenDiposit . ' * * * *');
        }

        // Bot trading schedules
        if(allsetting('enable_bot_trade') == STATUS_ACTIVE) {
            $removeInterval = 10;
            $schedule->command('botOrder:remove')
                ->cron('*/'.$removeInterval. ' * * * *')
                ->onSuccess(function () {
                    // success handling
                })
                ->onFailure(function () {
                    // failure handling
                });
        }

        // Daily schedules
        $schedule->command('staking:give-payment')->dailyAt('23:00');
        $schedule->command('clear-failed-job')->daily();

        // Demo mode schedule
        if (env('APP_MODE') == 'demo') {
            $schedule->command('clear-big-order')->everyTenMinutes();
        }

        // Send deposit notifications
        $schedule->command('deposits:send-notifications')->everyMinute();
    }

    protected function commands()
    {
        $this->load(__DIR__.'/Commands');
        require base_path('routes/console.php');
    }
}
