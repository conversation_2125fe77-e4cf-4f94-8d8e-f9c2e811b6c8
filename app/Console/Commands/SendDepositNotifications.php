<?php

namespace App\Console\Commands;

use App\Models\DepositeTransaction;
use App\Models\User;
use App\Models\Wallet;
use Cryptommer\Smsir\Classes\Smsir;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Cryptommer\Smsir\Objects\Parameters;

class SendDepositNotifications extends Command
{
    protected $signature = 'deposits:send-notifications';
    protected $description = 'Send SMS notifications for new deposits';

    public function handle()
    {
        try {
            $pendingDeposits = DepositeTransaction::where('notification_status', 'pending')
                ->with(['senderWallet.user', 'receiverWallet.user'])
                ->get();

            foreach ($pendingDeposits as $deposit) {
                try {
                    // ارسال پیامک
               //     $message = "واریز جدید به مبلغ {$deposit->amount} {$deposit->coin_type} انجام شد.";
               
                    // اگر از پکیج Smsir استفاده می‌کنید
                    $sms = new Smsir;
                    $wallet = Wallet::find($deposit->receiver_wallet_id)->first();
                    $user = User::where('id',$wallet->user_id)->first();
                    if (!$user || !$user->phone) {
                        Log::error("User or phone number not found for deposit {$deposit->id}");
                        continue;
                    }
              //      Log::error("User or phone number  {$user->phone}");
//Log::error("user_id  {$wallet->user_id}");
               //     Log::error("User  {$deposit->coin_type}");

                    switch ($deposit->coin_type) {
                        case 'TRX':
                            $coin = 'ترون شما';
                            break;
                        case 'USDT':
                            $coin = 'تتر شما';
                            break;
                        case 'BTC':
                            $coin = 'بیت کوین شما';
                            break;
                        case 'ETH':
                            $coin = 'اتریوم شما';
                            break;
                        default:
                            $coin = 'ارز شما';
                            break;
                    }

                    $sms->Send()->Verify($user->phone, 622782, [new Parameters('COIN', $coin)]);
                    $deposit->update(['notification_status' => 'sent']);
                    
                   // $this->info("Notification sent for deposit ID: {$deposit->id}");
                    
                } catch (\Exception $e) {
                   Log::error("Failed to send notification for deposit {$deposit->id}: " . $e->getMessage());
                    continue;
                }
            }
        } catch (\Exception $e) {
            Log::error("Error in SendDepositNotifications command: " . $e->getMessage());
            $this->error($e->getMessage());
        }
    }
}