<?php

namespace App\Console\Commands;

use App\Http\Repositories\CustomTokenRepository;
use App\Services\Evm\EvmWalletService;
use Illuminate\Console\Command;

class TokenDepositCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:erc20token-deposit';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'TRC20 and MATIC token deposit command';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // it will search on block at a time 
        storeBotException('TokenDepositCommand called', date('Y-m-d H:i:s'));
        $response = (new EvmWalletService)->callDepositCommand();
        // $repo = new CustomTokenRepository();
        // $repo->depositCustomERC20Token();
    }
}
