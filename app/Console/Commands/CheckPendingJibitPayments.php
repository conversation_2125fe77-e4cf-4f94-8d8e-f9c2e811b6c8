<?php

namespace App\Console\Commands;

use App\Models\JibitPayment;
use App\Services\JibitPaymentService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckPendingJibitPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jibit:check-pending-payments 
                            {--limit=50 : Number of payments to check}
                            {--older-than=30 : Check payments older than X minutes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and update status of pending Jibit payments';

    protected $jibitService;

    public function __construct(JibitPaymentService $jibitService)
    {
        parent::__construct();
        $this->jibitService = $jibitService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $limit = $this->option('limit');
        $olderThanMinutes = $this->option('older-than');

        $this->info("Checking pending Jibit payments...");
        $this->info("Limit: {$limit}, Older than: {$olderThanMinutes} minutes");

        // Get pending payments older than specified minutes
        $pendingPayments = JibitPayment::where('status', 'pending')
            ->where('created_at', '<=', now()->subMinutes($olderThanMinutes))
            ->orderBy('created_at', 'asc')
            ->limit($limit)
            ->get();

        if ($pendingPayments->isEmpty()) {
            $this->info('No pending payments found to check.');
            return 0;
        }

        $this->info("Found {$pendingPayments->count()} pending payments to check.");

        $checkedCount = 0;
        $successfulCount = 0;
        $failedCount = 0;
        $expiredCount = 0;

        foreach ($pendingPayments as $payment) {
            try {
                $this->line("Checking payment: {$payment->payment_identifier}");

                $result = $this->jibitService->verifyPayment($payment->payment_identifier);

                if ($result['success']) {
                    $isSuccessful = $result['data']['is_successful'] ?? false;
                    
                    if ($isSuccessful) {
                        $successfulCount++;
                        $this->info("✓ Payment {$payment->payment_identifier} is successful");
                    } else {
                        // Check if payment should be marked as expired
                        $hoursOld = $payment->created_at->diffInHours(now());
                        if ($hoursOld >= 24) { // Expire payments older than 24 hours
                            $payment->markAsExpired();
                            $expiredCount++;
                            $this->warn("⏰ Payment {$payment->payment_identifier} marked as expired");
                        } else {
                            $this->line("⏳ Payment {$payment->payment_identifier} still pending");
                        }
                    }
                } else {
                    // If verification fails, check if we should mark as failed
                    $hoursOld = $payment->created_at->diffInHours(now());
                    if ($hoursOld >= 24) {
                        $payment->markAsFailed(['error' => 'Verification failed after 24 hours']);
                        $failedCount++;
                        $this->error("✗ Payment {$payment->payment_identifier} marked as failed");
                    } else {
                        $this->warn("⚠ Payment {$payment->payment_identifier} verification failed, will retry later");
                    }
                }

                $checkedCount++;

            } catch (\Exception $e) {
                $this->error("Error checking payment {$payment->payment_identifier}: " . $e->getMessage());
                Log::error('Error in CheckPendingJibitPayments command', [
                    'payment_id' => $payment->id,
                    'payment_identifier' => $payment->payment_identifier,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            // Add a small delay to avoid overwhelming the API
            usleep(500000); // 0.5 seconds
        }

        $this->newLine();
        $this->info("Summary:");
        $this->info("- Checked: {$checkedCount} payments");
        $this->info("- Successful: {$successfulCount} payments");
        $this->info("- Failed: {$failedCount} payments");
        $this->info("- Expired: {$expiredCount} payments");

        Log::info('CheckPendingJibitPayments command completed', [
            'checked' => $checkedCount,
            'successful' => $successfulCount,
            'failed' => $failedCount,
            'expired' => $expiredCount
        ]);

        return 0;
    }
}
