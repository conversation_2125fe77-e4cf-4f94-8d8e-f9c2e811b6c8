<?php

namespace App\Console\Commands;

use App\Models\AdminWalletKey;
use App\Models\CoinSetting;
use App\Models\Network;
use Illuminate\Console\Command;

class UpdateSystemWallet extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:updatesystemwallet';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $coinSettingList = CoinSetting::join('coins','coin_settings.coin_id','=','coins.id')
            ->where('coin_settings.check_encrypt', 1)
            ->select('coin_settings.*', 'coins.network as network_type')
            ->whereNotNull('coin_settings.wallet_address')
            ->get();
            if($coinSettingList->count()>0){
                foreach($coinSettingList as $coinSetting){
                    $networkDetails = Network::where([
                        'base_type' => getNetBaseType($coinSetting->network_type),
                        'chain_id' => $coinSetting->chain_id
                        ])->first();
                    if(isset($networkDetails)){
                        $key = decryptId($coinSetting->wallet_key);
                        $key = (gettype($key) == 'array') ? false : $key;
                        if(!$key) continue;
                        $decryptPV = $key;
                        $checkExist = AdminWalletKey::where(['network_id' => $networkDetails->id])->first();
                        if(empty($checkExist)) {
                            $newCoinNetwork = new AdminWalletKey();
                            $newCoinNetwork->uid = generateUID();
                            $newCoinNetwork->network_id = $networkDetails->id;
                            $newCoinNetwork->address = $coinSetting->wallet_address;
                            $newCoinNetwork->pv = custom_encrypt($decryptPV);
                            $newCoinNetwork->creation_type = 0;
                            $newCoinNetwork->status = STATUS_ACTIVE;
                            $newCoinNetwork->save();
                        }

                    }
                }
            }
        } catch (\Exception $e) {
            storeException('UpdateSystemWallet',$e->getMessage());
        }
    }
}
