<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class GenerateRSAKeys extends Command
{
    protected $signature = 'rsa:generate-keys {--force : Force regenerate keys if they exist}';
    protected $description = 'Generate RSA public and private keys for encryption';

    public function handle()
    {
        $force = $this->option('force');
        
        // بررسی وجود کلیدها
        if (!$force && Storage::exists('keys/private.pem') && Storage::exists('keys/public.pem')) {
            $this->error('RSA keys already exist. Use --force to regenerate.');
            return 1;
        }

        $this->info('Generating RSA key pair...');

        // تولید کلید RSA
        $config = [
            "digest_alg" => "sha512",
            "private_key_bits" => 2048,
            "private_key_type" => OPENSSL_KEYTYPE_RSA,
        ];

        // تولید کلید خصوصی
        $res = openssl_pkey_new($config);
        if (!$res) {
            $this->error('Failed to generate private key');
            return 1;
        }

        // استخراج کلید خصوصی
        openssl_pkey_export($res, $privateKey);

        // استخراج کلید عمومی
        $publicKeyDetails = openssl_pkey_get_details($res);
        $publicKey = $publicKeyDetails["key"];

        // ایجاد پوشه keys اگر وجود نداشته باشد
        if (!Storage::exists('keys')) {
            Storage::makeDirectory('keys');
        }

        // ذخیره کلیدها
        Storage::put('keys/private.pem', $privateKey);
        Storage::put('keys/public.pem', $publicKey);

        // تنظیم مجوزهای فایل
        $privateKeyPath = storage_path('app/keys/private.pem');
        $publicKeyPath = storage_path('app/keys/public.pem');
        
        if (file_exists($privateKeyPath)) {
            chmod($privateKeyPath, 0600); // فقط مالک بتواند بخواند/بنویسد
        }
        
        if (file_exists($publicKeyPath)) {
            chmod($publicKeyPath, 0644); // همه بتوانند بخوانند، فقط مالک بنویسد
        }

        $this->info('RSA keys generated successfully!');
        $this->line('Private key: storage/app/keys/private.pem');
        $this->line('Public key: storage/app/keys/public.pem');
        
        // نمایش کلید عمومی برای استفاده در frontend
        $this->newLine();
        $this->info('Public key for frontend:');
        $this->line($publicKey);

        return 0;
    }
}
