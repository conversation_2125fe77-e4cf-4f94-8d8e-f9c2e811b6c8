<?php

namespace App\Repositories\Kucoin;

use App\CurrencyProviders\KuCoinProvider;
use App\Repositories\RepositoriesContract;
use GuzzleHttp\Client;

class AccountApiRepoCopy implements RepositoriesContract
{
    protected string $key;

    protected string $passphrase;

    protected string $secret;

    protected int $version;

    public function __construct(
        protected KuCoinProvider $provider,
        $key, $secret, $passphrase
    ) {
        $this->key = $this->key ?? config('kucoin.api-key');
        $this->passphrase = $this->passphrase ?? config('kucoin.api-passphrase');
        $this->secret = $this->secret ?? config('kucoin.api-secret');
        $this->version = $this->version ?? config('kucoin.api-key-version');

    }

    public function getAllRecords()
    {
        // return $this->provider->
    }

    public function getRecordById($id) {}

    public function createRecord(array $data)
    {
        $client = new Client;
        $request_path = '/api/v1/sub/api-key';
        $response = $client->post(env('KUCOIN_API_BASE_URL').$request_path, [
            'headers' => [
                'KC-API-KEY' => config('kucoin.api-key'),
                'KC-API-SIGN' => $this->signature($request_path, $data, false, 'POST'),
                'KC-API-TIMESTAMP' => time() * 1000,
                'KC-API-PASSPHRASE' => $this->passphrase,
                'KC-API-KEY-VERSION' => $this->version,
            ],
            'form_params' => $data,
        ]);

        $body = $response->getBody();
        $resp = json_decode($body, true);

        dd($resp); // برای نمایش داده‌ها
        // $api = new Account($this->auth(), new GuzzleHttp);
        // try {
        //     return $api->createSubUserApiKey($params);
        // } catch (InvalidApiUriException $e) {
        //     return [$e];
        // }
        // return $this->provider->createSubAccountApi($data);
    }
    // protected function auth(): Auth
    // {
    // return new Auth($this->api_key, $this->api_secret, $this->api_passphrase, $this->api_version);
    // }

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}

    public function signature($request_path = '', $body = '', $timestamp = false, $method = 'GET')
    {

        $body = is_array($body) ? json_encode($body) : $body; // Body must be in json format

        $timestamp = $timestamp ? $timestamp : time() * 1000;

        $what = $timestamp.$method.$request_path.$body;

        return base64_encode(hash_hmac('sha256', $what, $this->secret, true));
    }
}
