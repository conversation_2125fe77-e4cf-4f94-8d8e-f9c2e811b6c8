<?php

namespace App\Repositories\Kucoin;

use App\CurrencyProviders\KuCoinProvider;
use App\Repositories\RepositoriesContract;
use GuzzleHttp\Client;

class AccountRepo implements RepositoriesContract
{
    protected $client;

    public function __construct(
        protected KuCoinProvider $provider,
    ) {
        $this->client = new Client([
            'base_uri' => 'https://api.kucoin.com',
        ]);
    }

    public function createRecord(array $data)
    {
        // Here, you will make the request to KuCoin API to create a wallet
        $endpoint = '/api/v1/accounts';  // Adjust endpoint as needed
        $method = 'POST';
        $body = json_encode([
            'userId' => 2,
            'currency' => 'USDT',
            'type' => 'main',
        ]);

        // Generate signature
        $headers = $this->generateSignature($method, $endpoint, $body);

        // Make the API request
        $response = $this->client->post($endpoint, [
            'headers' => array_merge($headers, [
                'Content-Type' => 'application/json',
                'KC-API-KEY' => env('KUCOIN_API_KEY'),
                'KC-API-SIGN' => $headers, // Generate a signature based on KuCoin's requirements
                'KC-API-PASSPHRASE' => env('KUCOIN_API_PASSPHRASE'),
                'KC-API-TIMESTAMP' => time() * 1000,
            ]),
            'body' => $body,
        ]);

        return json_decode($response->getBody(), true);
    }

    public function deleteRecordById($id) {}

    public function getAllRecords() {}

    public function getRecordById($id) {}

    public function updateRecord($id, array $data) {}

    protected function generateSignature($method, $endpoint, $body = '')
    {
        $timestamp = time() * 1000;
        $apiSecret = env('KUCOIN_API_SECRET');
        $apiKey = env('KUCOIN_API_KEY');
        $apiPassphrase = env('KUCOIN_API_PASSPHRASE');

        // Prepare the string to sign
        $stringToSign = $timestamp.strtoupper($method).$endpoint.$body;

        // Generate the signature
        $signature = hash_hmac('sha256', $stringToSign, $apiSecret, true);

        // Encode the signature with base64
        $signature = base64_encode($signature);

        return [
            'KC-API-SIGN' => $signature,
            'KC-API-TIMESTAMP' => $timestamp,
            'KC-API-KEY' => $apiKey,
            'KC-API-PASSPHRASE' => base64_encode(hash_hmac('sha256', $apiPassphrase, $apiSecret, true)),
        ];
    }
}
