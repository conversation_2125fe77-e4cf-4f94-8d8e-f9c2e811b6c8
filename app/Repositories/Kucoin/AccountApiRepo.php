<?php

namespace App\Repositories\Kucoin;

use App\CurrencyProviders\KuCoinProvider;
use App\Models\Kucoin\SubAccount;
use App\Models\Kucoin\SubAccountApi;
use App\Repositories\RepositoriesContract;
use Illuminate\Support\Facades\Auth;

class AccountApiRepo implements RepositoriesContract
{
    public function __construct(
        protected KuCoinProvider $provider,
    ) {}

    public function getAllRecords() {}

    public function getRecordById($id)
    {
        $subAccount = SubAccount::where('userId', $id)->first();

        return $this->provider->getSubAccountApi([$subAccount->subName]);
    }

    public function createRecord(array $data)
    {
        $result = $this->provider->createSubAccountApi($data);
        SubAccountApi::create([
            'userId' => Auth::user()->id,
            'subName' => $result['subName'],
            'remark' => $result['remark'],
            'apiKey' => $result['apiKey'],
            'apiSecret' => $result['apiSecret'],
            'apiVersion' => $result['apiVersion'],
            'passphrase' => $result['passphrase'],
            'permission' => $result['permission'],
            'createdAt' => $result['createdAt'],
        ]);

        return $result;
    }

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}
}
