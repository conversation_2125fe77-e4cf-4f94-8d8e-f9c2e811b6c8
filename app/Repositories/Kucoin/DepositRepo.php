<?php

namespace App\Repositories\Kucoin;

use App\CurrencyProviders\KuCoinProvider;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class DepositRepo extends Repository implements RepositoriesContract
{
    public function __construct(
        protected KuCoinProvider $provider,
    ) {}

    public function getAllRecords() {}

    public function getRecordById($id) {}

    public function createRecord(array $data)
    {
        return $this->provider->deposit($data);
    }

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}
}
