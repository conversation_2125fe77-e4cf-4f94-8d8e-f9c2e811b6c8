<?php

namespace App\Repositories;

use Illuminate\Support\Facades\Storage;

class FileDataRepo
{
    public function create($fileData, $model)
    {
        $prefix = class_basename($model);
        $fileName = $prefix.'-'.$model->id.'-'.time().'.'.$fileData->extension();
        $filePath = "files/$fileName";
        Storage::disk('public')->put($filePath, file_get_contents($fileData->getRealPath()));

        return $model->file()->create([
            'url' => $filePath,
        ]);
    }

    public function delete($model)
    {
        if (isset($model->file->url)) {
            Storage::delete($model->file->url);
        }

        return $model->file()->delete();
    }
}
