<?php

namespace App\Repositories\ByBit;

use App\CurrencyProviders\ByBitProvider;
use App\Models\ByBitSubUser;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use Illuminate\Support\Facades\Auth;

class WalletRepo extends Repository implements RepositoriesContract
{
    public function __construct(
        // protected ByBitProvider $byBitProvider
    ) {}

    public function getAllRecords($request)
    {
        // $endpoint = 'v5/asset/coin/query-info';
        // $request = $this->byBitProvider->getRequest([], $endpoint);

        // return $request;

    }

    public function getRecordById($id) {}

    public function createRecord(array $data)
    {
        // $endpoint = '/v5/asset/deposit/query-sub-member-address';
        // $byBitSubUser = ByBitSubUser::where('userId', Auth::user()->id)->firstOrFail();
        // $body = [
        //     'coin' => $data['coin'],
        //     'chainType' => $data['chainType'],
        //     'subMemberId' => $byBitSubUser->uid,
        // ];
        // $request = $this->byBitProvider->getRequest($body, $endpoint);

        // return $request;

    }

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}
}
