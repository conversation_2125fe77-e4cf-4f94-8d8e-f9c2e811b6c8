<?php

namespace App\Repositories\ByBit;

use App\CurrencyProviders\ByBitProvider;
use App\Models\ByBitSubUser;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use Illuminate\Support\Facades\Auth;

class UserRepo extends Repository implements RepositoriesContract
{
    public function __construct(
        protected ByBitProvider $byBitProvider

    ) {}

    public function getAllRecords()
    {
        $endpoint = '/v5/user/submembers';
        $request = $this->byBitProvider->getRequest([], $endpoint);

        return $request['result']['subMembers'];
    }

    public function getRecordById($id) {}

    public function createRecord(array $data)
    {
        $endpoint = '/v5/user/create-sub-member';
        $body = [
            'username' => $data['username'],
            'password' => $data['password'],
            'memberType' => 1,
            'switch' => 1,
            'note' => $data['remarks'] ?? 'General,Spot,Futures,Margin',
        ];

        $request = $this->byBitProvider->postRequest($body, $endpoint);

        $create = ByBitSubUser::create([
            'userId' => $data['user_id'] ?? Auth::user()->id,
            'uid' => $request['result']['uid'],
            'username' => $request['result']['username'],
            'password' => $data['password'],
            'memberType' => $request['result']['memberType'],
            'switch' => 1,
            'remark' => $request['result']['remark'],
            'status' => $request['result']['status'],
        ]);

        return $create;

    }

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}
}
