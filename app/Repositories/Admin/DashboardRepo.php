<?php

namespace App\Repositories\Admin;

use App\Models\AdminSetting;
use App\Models\Support;
use App\Models\Transaction;
use App\Models\User;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class DashboardRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        $transactions = Transaction::query();
        $adminSetting = AdminSetting::query();

        return [
            'users_amount' => [
                'status' => true,
                'value' => 0,
            ],
            'users_count' => [
                'status' => true,
                'value' => User::count(),
            ],
            'buy_count' => [
                'status' => true,
                'value' => $transactions->where('type', 'buy')->count(),
            ],
            'sell_count' => [
                'status' => true,
                'value' => $transactions->where('type', 'sell')->count(),
            ],
            'orders_count' => [
                'status' => true,
                'value' => 0,
            ],
            'sum_buy' => [
                'status' => true,
                'value' => $transactions->where('type', 'buy')->sum('amount'),
            ],
            'sum_sell' => [
                'status' => true,
                'value' => $transactions->where('type', 'sell')->sum('amount'),
            ],
            'sum_orders' => [
                'status' => true,
                'value' => 0,
            ],
            'web_money_amount' => [
                'status' => true,
                'value' => 0,
            ],
            'web_money_irt_amount' => [
                'status' => true,
                'value' => 0,
            ],
            'perfect_money_amount' => [
                'status' => true,
                'value' => 0,
            ],
            'perfect_money_irt_amount' => [
                'status' => true,
                'value' => 0,
            ],
            'ps_voucher_usd_amount' => [
                'status' => true,
                'value' => 0,
            ],
            'ps_voucher_irt_amount' => [
                'status' => true,
                'value' => 0,
            ],
            'tickets' => [
                'status' => true,
                'value' => Support::count(),
            ],
            'fast_configs' => [
                'exchange_info' => [
                    'status' => true,
                    'value' => 'http://address.com',
                ],
                'contact_info' => [
                    'status' => true,
                    'value' => 'http://address.com',
                ],
                'limits' => [
                    'status' => true,
                    'value' => 'http://address.com',
                ],
                'activate_gateway' => [
                    'status' => true,
                    'value' => 'http://address.com',
                ],
                'activate_sms_panel' => [
                    'status' => true,
                    'value' => 'http://address.com',
                ],
                'activate_telegram_bot' => [
                    'status' => true,
                    'value' => 'http://address.com',
                ],
            ],
            'guidance' => [
                'terms' => [
                    'status' => true,
                    'value' => 'http://address.com',
                ],
                'faq' => [
                    'status' => true,
                    'value' => 'http://address.com',
                ],
                'academy' => [
                    'status' => true,
                    'value' => 'http://address.com',
                ],
            ],
            'other_versions_info' => [
                'can_read_files_in_host' => [
                    'status' => true,
                    'value' => true,
                ],
                'max_send_size' => [
                    'status' => true,
                    'value' => '10M',
                ],
                'max_upload_size' => [
                    'status' => true,
                    'value' => '10M',
                ],
                'system_type' => [
                    'status' => true,
                    'value' => 'manual',
                ],
                'address' => [
                    'status' => true,
                    'value' => 'https://site.com',
                ],
                'last_exchange_cron' => [
                    'status' => true,
                    'value' => '2022-09-01',
                ],
                'last_public_cron' => [
                    'status' => true,
                    'value' => '2022-09-01',
                ],
            ],
            'fast_access' => [
                // 'voucher_sell_rate' => [
                //     'status' => true,
                //     'value' => $adminSetting->where('code', 'voucher_sell_rate')->first()->value ?? 0,
                // ],
                // 'voucher_buy_rate' => [
                //     'status' => true,
                //     'value' => $adminSetting->where('code', 'voucher_buy_rate')->first()->value ?? 0,
                // ],
                // 'perfect_money_sell_rate' => [
                //     'status' => true,
                //     'value' => $adminSetting->where('code', 'perfect_money_sell_rate')->first()->value ?? 0,
                // ],
                // 'perfect_money_buy_rate' => [
                //     'status' => true,
                //     'value' => $adminSetting->where('code', 'perfect_money_buy_rate')->first()->value ?? 0,
                // ],
                // 'pay_pal_sell_rate' => [
                //     'status' => true,
                //     'value' => $adminSetting->where('code', 'pay_pal_sell_rate')->first()->value ?? 0,
                // ],
                // 'pay_pal_buy_rate' => [
                //     'status' => true,
                //     'value' => $adminSetting->where('code', 'pay_pal_buy_rate')->first()->value ?? 0,
                // ],
            ],
        ];
    }

    public function createRecord(array $data) {}

    public function getRecordById($id) {}

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}
}
