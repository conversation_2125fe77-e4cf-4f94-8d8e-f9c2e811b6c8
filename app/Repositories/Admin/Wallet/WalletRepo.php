<?php

namespace App\Repositories\Admin\Wallet;

use App\Models\Wallet;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class WalletRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        $query = Wallet::query();
        if ($request->query('user_id')) {
            $query->where('user_id', $request->query('user_id'));
        }

        return $query->with('user')->paginate(
            perPage: $request->input('per_page', 30),
            page: $request->input('page', 1)
        );
    }

    public function createRecord(array $data) {}

    public function getRecordById($id) {}

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}
}
