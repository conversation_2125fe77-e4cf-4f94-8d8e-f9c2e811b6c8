<?php

namespace App\Repositories\Admin;

use App\Models\Support;
use App\Models\SupportTicket;
use Illuminate\Support\Facades\Auth;

class SupportRepo
{
    public function getAllRecords($request)
    {
        $query = Support::query();
        if ($request->query('user_id')) {
            $query->where('user_id', $request->query('user_id'));
        }
        if ($request->query('status')) {
            $query->where('status', $request->query('status'));
        }

        return $query->with('user', 'unit', 'level')->orderBy('updated_at')->paginate(
            perPage: $request->input('per_page', 30),
            page: $request->input('page', 1),
        );
    }

    public function newRecord($userId)
    {
        return $userId ? User::findOrFail($userId) : [];
    }

    public function createRecord(array $data)
    {
        $support = Support::create([
            'subject' => $data['subject'],
            'unit_id' => $data['unit_id'],
            'level_id' => $data['level_id'],
            'user_id' => $data['user_id'],
            'status' => 'admin_response',
        ]);
        $ticket = SupportTicket::create([
            'support_id' => $support->id,
            'user_id' => Auth::id(), // Fix: Use admin's ID instead of unit_id
            'message' => $data['message'],
        ]);

        return $ticket;
    }

    public function getRecordById($id)
    {
        return Support::with('tickets')->findOrFail($id);
    }

    public function updateRecord($id, array $data)
    {
        $support = Support::findOrFail($id);
        $ticket = SupportTicket::create([
            'support_id' => $support->id,
            'user_id' => Auth::user()->id,
            'message' => $data['message'],
        ]);

        return $ticket;
    }

    public function deleteRecordById($id)
    {
        return Support::findOrFail($id)->update([
            'status' => 'deactive',
        ]);
    }
}
