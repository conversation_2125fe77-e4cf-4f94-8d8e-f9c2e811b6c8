<?php

namespace App\Repositories\Admin;

use App\Models\AdminSetting;
use App\Models\AdminSettingGroup;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use App\Services\FileDataService;

class AdminSettingRepo extends Repository implements RepositoriesContract
{
    public function __construct(
        protected FileDataService $fileDataService
    ) {}

    public function getAllRecords($request)
    {
        if ($request->query('type') == 'parents') {
            return AdminSettingGroup::get();
        } else {
            return AdminSettingGroup::with('items')->get();
        }
    }

    public function createRecord(array $data)
    {
        foreach ($data as $row) {
            AdminSetting::updateOrCreate(
                [
                    'code' => $row['code']
                ],
                [
                    "code" => $row['code'],
                    "value" => $row['value'],
                    "status" => $row['status'] ?? 1,
                    "title" => $row['title'] ?? null,
                    "type" => $row['type'],
                    "group_id" => $row['group_id'] ?? null,
                ]
            );
        }

        return [];
    }

    public function getRecordById($id)
    {
        $adminSetting = AdminSetting::with('file')->findOrFail(id: $id);

        return $adminSetting;
    }

    public function updateRecord($id, array $data)
    {
        $adminSetting = AdminSetting::findOrFail(id: $id);
        if ($data['type'] == 'file') {
            $this->fileDataService->createFile($data['value'], $adminSetting);
        }
        $adminSetting->update([
            'value' => $data['value'],
            'group_id' => $data['group_id'] ?? $adminSetting->group_id,
        ]);

    }

    public function deleteRecordById($id) {}
}
