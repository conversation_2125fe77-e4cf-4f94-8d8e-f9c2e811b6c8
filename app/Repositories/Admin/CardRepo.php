<?php

namespace App\Repositories\Admin;

use App\Models\Card;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use App\Services\FileDataService;

class CardRepo extends Repository implements RepositoriesContract
{
    public function __construct(
        protected FileDataService $fileDataService
    ) {}

    public function getAllRecords($request)
    {
        $docs = Card::query();
        if ($request->query('user_id')) {
            $docs = $docs->where('user_id', $request->query('user_id'));
        }
        if ($request->query('status')) {
            $docs = $docs->whereIn('status', explode(',', $request->query('status')));
        }

        return $docs->with('bank', 'user')->paginate(
            perPage: $request->input('per_page', 30),
            page: $request->input('page', 1)
        );

    }

    public function createRecord(array $data) {}

    public function getRecordById($id)
    {
        return Card::findOrFail($id);
    }

    public function updateRecord($id, array $data)
    {
        $card = Card::findOrFail($id);
        $card->status = $data['status'];
        $card->number = $data['number'] ?? $card->number;
        $card->sheba = $data['sheba'] ?? $card->sheba;
        $card->save();

        return $card;

    }

    public function deleteRecordById($id)
    {
        $card = Card::findOrFail($id);
        $this->fileDataService->deleteFile($card);
        $card->delete();
    }
}
