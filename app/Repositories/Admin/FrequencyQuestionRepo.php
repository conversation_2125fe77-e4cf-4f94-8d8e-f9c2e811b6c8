<?php

namespace App\Repositories\Admin;

use App\Models\FrequencyQuestion;
use App\Models\FrequencyQuestionGroup;
use App\Models\FrequencyQuestionlevel;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class FrequencyQuestionRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request){
        return FrequencyQuestionGroup::with('items')->get();
    }

    public function createRecord(array $data){
        return FrequencyQuestion::create([
            'question' => $data['question'],
            'answer' => $data['answer'],
            'parent_id' => $data['parent_id'],
        ]);
    }

    public function getRecordById($id){
        return FrequencyQuestion::findOrFail($id);
    }

    public function updateRecord($id, array $data){
        FrequencyQuestion::findOrFail($id)->update([
            'question' => $data['question'],
            'answer' => $data['answer'],
        ]);
    }

    public function deleteRecordById($id){
        $frequencyQuestion = FrequencyQuestion::findOrFail($id);
        $frequencyQuestion->delete();
    }
}
