<?php

namespace App\Repositories\Admin\Accounting;

use App\Models\AdminSetting;
use App\Models\Transaction;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class SettlementRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        return Transaction::where('type', 'withdraw')->get();
    }

    public function createRecord(array $data)
    {
    }

    public function getRecordById($id) {}

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}
}
