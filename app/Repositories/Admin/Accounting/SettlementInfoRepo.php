<?php

namespace App\Repositories\Admin\Accounting;

use App\Models\AdminSetting;
use App\Models\Transaction;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class SettlementInfoRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        $dataInfo = [
            'withdrawSettlementActive',
            'sellSettlementActive',
            'settlementMonthlyProfit',
            'quickSettlementLoss',
            'settlementService',
            'settlementMaxDaysProfit',
            'settlementTodayProfit',
        ];
        $settlementInfo = AdminSetting::whereIn('code', $dataInfo)->get();
        $formattedData = [];
        foreach ($settlementInfo as $item) {
            $value = $item->value;
            if($item->type == 'boolean') {
                $value = $item->value == 1 ? true : false;
            } elseif($item->type == 'integer') {
                $value = (int)$item->value;
            }
            $formattedData[$item->code] = $value;
        }

        $settlements = Transaction::query();

        $formattedData = array_merge(
            [
                'pending' => $settlements->where('status','pending')->count(),
                'waiting' => $settlements->where('status','waiting')->count(),
                'approvedAuto' => $settlements->where('status','approved')->where('registrar',null)->count(),
                'approvedManual' => $settlements->where('status','approved')->whereNot('registrar',null)->count(),
                'declined' => $settlements->where('status','declined')->count(),
            ],
            $formattedData,
        );
        return $formattedData;
    }

    public function createRecord(array $data){
        foreach ($data as $setting) {
            AdminSetting::updateOrCreate(
                ['code' => $setting['code']],
                [
                    'value' => $setting['value'],
                    'type' => $setting['type'],
                ]
            );
        }

        return [];

    }

    public function getRecordById($id){

    }

    public function updateRecord($id, array $data){

    }

    public function deleteRecordById($id){

    }
    //

}
