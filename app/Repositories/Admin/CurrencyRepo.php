<?php

namespace App\Repositories\Admin;

use App\Models\Currency;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class CurrencyRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        return Currency::get();
    }

    public function createRecord(array $data)
    {
        $create = Currency::create($data);

        return $create;
    }

    public function getRecordById($id)
    {
        return Currency::findOrFail($id);
    }

    public function updateRecord($id, array $data)
    {
        $currency = Currency::findOrFail($id);
        $currency->update($data);
    }

    public function deleteRecordById($id)
    {
        Currency::destroy($id);
    }
}
