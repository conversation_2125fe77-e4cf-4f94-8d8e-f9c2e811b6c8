<?php

namespace App\Repositories\Admin\User;

use App\Models\User;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use Illuminate\Support\Facades\Hash;

class UserRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        $queryUsers = User::query();
        if ($request->input('filter')) {
            $filter = $request->input('filter');
            switch ($filter) {
                case 'balance':
                    $queryUsers->whereHas('transactions', function ($query) {
                        $query->where('currency_id', 3);
                    })->get();
                    break;
                case 'crypto':
                    $queryUsers->whereHas('transactions', function ($query) {
                        $query->whereNot('currency_id', 3);
                    })->get();
                    break;
                case 'lastMonth':
                    $queryUsers->whereMonth('created_at', date('m', strtotime(date('Y-m-d').'-1 month')));
                    break;
                case 'pending':
                    $queryUsers->where('status', 'pending');
                    break;
                default:
                    $queryUsers->whereAny(['phone', 'email', 'firstname', 'lastname', 'id', 'national_id'], 'LIKE', "%$filter%");
                    break;
            }
        }

        return $queryUsers->with('roles', 'transactions','alerts')
            ->paginate(
                perPage: $request->input('per_page', 30), // تعداد آیتم‌ها در هر صفحه، به طور پیش‌فرض ۳۰
                page: $request->input('page', 1) // شماره صفحه، به طور پیش‌فرض ۱
            )
            ->through(function ($user) {
                $user->balance = $user->balance; // محاسبه خودکار موجودی

                return $user;
            });

    }

    public function createRecord(array $data)
    {
        $user = User::create([
            'email' => $data['email'] ?? null,
            'phone' => $data['phone'],
            'password' => isset($data['password']) ? Hash::make($data['password']) : null,
            'firstname' => $data['firstname'] ?? null,
            'lastname' => $data['lastname'] ?? null,
            'national_id' => $data['national_id'] ?? null,
            'gender' => $data['gender'] ?? 'undefined',
            'birth_date' => $data['birth_date'] ?? null,
            'level' => $data['level'] ?? 1,
            'status' => $data['status'] ?? 'pending',
        ]);
    
        create_coin_wallet($user->id);
    
        return $user;
    }

    public function getRecordById($id)
    {
        $user = User::findOrFail($id);
        $user['info'] = [
            'count_buy' => $user->transactions->where('type', 'buy')->count(),
            'count_sell' => $user->transactions->where('type', 'sell')->count(),
            'sum_buy' => $user->transactions->where('type', 'buy')->where('currency_id', '!=', 3)->sum('amount'),
            'sum_sell' => $user->transactions->where('type', 'sell')->where('currency_id', '!=', 3)->sum('amount'),
            'count_buy_toman' => $user->transactions->where('type', 'buy')->where('currency_id', 3)->count(),
            'count_sell_toman' => $user->transactions->where('type', 'sell')->where('currency_id', 3)->count(),
            'sum_buy_toman' => $user->transactions->where('type', 'buy')->where('currency_id', 3)->sum('amount'),
            'sum_sell_toman' => $user->transactions->where('type', 'sell')->where('currency_id', 3)->sum('amount'),
        ];
        $user->load(['wallets', 'cards', 'documents','alerts']);

        return $user;
    }

    public function updateRecord($id, array $data)
    {
        $user = User::findOrFail($id);
        $user->update([
            'email' => $data['email'] ?? $user->email,
            'phone' => $data['phone'] ?? $user->phone,
            'password' => isset($data['password']) ? Hash::make($data['password']) : $user->password,
            'firstname' => $data['firstname'] ?? $user->firstname,
            'lastname' => $data['lastname'] ?? $user->lastname,
            'national_id' => $data['national_id'] ?? $user->national_id,
            'gender' => $data['gender'] ?? $user->gender,
            'birth_date' => $data['birth_date'] ?? $user->birth_date,
            'level' => $data['level'] ?? $user->level,
            'status' => $data['status'] ?? $user->status,
        ]);

    }

    public function deleteRecordById($id)
    {
        $user = User::findOrFail($id);
        $user->destroy();
    }
}
