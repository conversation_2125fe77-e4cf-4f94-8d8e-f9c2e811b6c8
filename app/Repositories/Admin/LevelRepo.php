<?php

namespace App\Repositories\Admin;

use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use App\Models\Level;
use App\Models\LevelItem;
use App\Models\LevelType;
use App\Models\LevelTypeValue;
use Illuminate\Support\Facades\DB;

class LevelRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request){
        $levels = Level::get();
        $levelsArray = [];
        foreach ($levels as $level) {
            $itemsArray = [];
            $levelItems = LevelItem::get();
            foreach($levelItems as $levelItem){
                $levelTypes = LevelType::get();
                $typesArray = [];
                foreach($levelTypes as $levelType){
                    $levelTypeValue = LevelTypeValue::
                        where('level_id', $level->id)
                        ->where('level_item_id', $levelItem->id)
                        ->where('level_type_id', $levelType->id)
                        ->value('value');
                    $levelType['value'] = $levelTypeValue;
                    $typesArray[] = $levelType;
                }
                $levelItem['types'] = $typesArray;
                $itemsArray[] = $levelItem;
            }
            $level['items'] = $itemsArray;
            $levelsArray[] = $level;
        }
        return $levelsArray;
    }

    public function createRecord(array $data){
        DB::transaction(function () use ($data) {
            foreach ($data['levels'] as $levelData) {
                $level = Level::find($levelData['id']);

                foreach ($levelData['items'] as $itemData) {
                    $item = LevelItem::find($itemData['id']);
                    foreach ($itemData['types'] as $typeData) {
                        LevelTypeValue::updateOrCreate(
                            [
                                'level_id' => $level->id,
                                'level_item_id' => $item->id,
                                'level_type_id' => $typeData['id'],
                            ],
                            ['value' => $typeData['value']]
                        );
                    }
                }
            }
        });
        return response()->json([
            'status' => 'success',
            'message' => 'Levels updated successfully'
        ]);
    }

    public function getRecordById($id){

    }

    public function updateRecord($id, array $data){

    }

    public function deleteRecordById($id){

    }
}

