<?php

namespace App\Repositories\User\Accounting;

use App\Models\Transaction;
use App\Models\User;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use Illuminate\Support\Facades\Auth;

class WithdrawRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        $user = User::findOrFail(Auth::user()->id);

        return $user->transactions()->where('type', 'withdraw')->orderBy('id', 'desc')->get();
    }

    public function createRecord(array $data)
    {
        return Transaction::create([
            'amount' => $data['amount'],
            'user_id' => Auth::user()->id,
            'type' => 'withdraw',
            'currency_id' => 3,
            'card_id' => $data['card_id'],
        ]);
    }

    public function getRecordById($id)
    {
        return Transaction::with('card.bank')->findOrFail($id);
    }

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}
}
