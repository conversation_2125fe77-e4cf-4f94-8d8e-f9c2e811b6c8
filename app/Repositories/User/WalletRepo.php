<?php

namespace App\Repositories\User;

use App\CurrencyProviders\NowNodes;
use App\CurrencyProviders\TronProvider;
use App\Models\UserWallet;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use Auth;
use Illuminate\Support\Facades\DB;

class WalletRepo extends Repository implements RepositoriesContract
{

    public function __construct(
        // protected TronProvider $tronProvider,
        protected NowNodes $provider,
    ){}

    public function getAllRecords($request){
        $userWallets = UserWallet::where('user_id', $request->user()->id)->with('currency')->get();
        $array = [];
        foreach($userWallets as $userWallet){
            $balance = 0;
            $amount = 0;
            $wallet = null;
            // if($userWallet->currency->code == 'trx'){
            //     $wallet = $this->provider->wallet($userWallet->currency);
            //     // $balance = $this->tronProvider->balance();
            //     // $wallet = $this->tronProvider->wallet(Auth::user()->id);
            //     // $amount = $userWallet->currency->buy * $balance;
            // }
            $array[] = [
                'id' => $userWallet->id,
                'currency' => $userWallet->currency,
                'wallet' => $wallet,
                'balance' => $balance,
                'amount' => $amount
            ];
        }
        return $array;

    }

    public function createRecord(array $data){
        // try {
        //     DB::beginTransaction(); // <= Starting the transaction

        //     if($data['currency_id'] == 5){
        //         $this->tronProvider->buy($data['amount']);
        //     }

        //     DB::commit(); // <= Commit the changes
        // } catch (\Exception $e) {
        //     report($e);

        //     DB::rollBack(); // <= Rollback in case of an exception
        // }
        // return [];
    }

    public function getRecordById($id){
        $userWallet = UserWallet::where('id',$id)->where('user_id',Auth::user()->id)->firstOrFail();
        $balance = 0;
        $wallet = [];
        if($userWallet->currency_id == 5){
            $wallet = $this->tronProvider->wallet(Auth::user()->id);
            $balance = $wallet->balance;
            $amount = $userWallet->currency->buy * $balance;
        }
        $array[] = [
            'id' => $userWallet->id,
            'currency' => $userWallet->currency,
            'balance' => $balance,
            'wallet' => $wallet,
            'amount' => $amount,
        ];
        return $array;

    }

    public function updateRecord($id, array $data){
        $data = array_merge(['user_id' => Auth::user()->id],$data);
        if($data['currency_id'] == 5){

            return $this->tronProvider->transfer($data);
        }
    }

    public function deleteRecordById($id){

    }

}
