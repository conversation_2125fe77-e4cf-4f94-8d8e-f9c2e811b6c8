<?php

namespace App\Repositories\User;

use App\Jobs\VerifyUserProfile;
use App\Models\User;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class MainUserRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request){

    }

    public function createRecord(array $data){

    }

    public function getRecordById($id){
        return User::findOrFail($id);
    }

    public function updateRecord($id, array $data){
        $user = User::findOrFail($id);
        $nationalIdChanged = false;
        $updateData = [];

        // Check if we're only updating the national_id (partial update)
        $isPartialUpdate = isset($data['national_id']) && count($data) === 1;

        // If it's a partial update with only national_id and user is rejected
        if ($isPartialUpdate && $user->status === 'rejected') {
            $updateData['national_id'] = $data['national_id'];

            // Check if national_id has changed
            if ($data['national_id'] !== $user->national_id) {
                $nationalIdChanged = true;
                $updateData['status'] = 'pending'; // Set status back to pending
            }
        }
        // If it's not a partial update, handle as before
        else {
            // If user status is approved, they can only update their email
            if ($user->status === 'approved') {
                if (isset($data['email'])) {
                    $updateData['email'] = $data['email'];
                }
            }
            // If user status is rejected, they can update all fields including national_id
            else if ($user->status === 'rejected') {
                // Only include fields that are provided in the request
                if (isset($data['firstname'])) $updateData['firstname'] = $data['firstname'];
                if (isset($data['lastname'])) $updateData['lastname'] = $data['lastname'];
                if (isset($data['gender'])) $updateData['gender'] = $data['gender'];
                if (isset($data['birth_date'])) $updateData['birth_date'] = $data['birth_date'];
                if (isset($data['email'])) $updateData['email'] = $data['email'];
                if (isset($data['national_id'])) {
                    $updateData['national_id'] = $data['national_id'];

                    // Check if national_id has changed
                    if ($data['national_id'] !== $user->national_id) {
                        $nationalIdChanged = true;
                        $updateData['status'] = 'pending'; // Set status back to pending
                    }
                }
            }
            // For pending or other statuses, they can update all fields except national_id
            // unless they don't have a national_id yet
            else {
                // Only include fields that are provided in the request
                if (isset($data['firstname'])) $updateData['firstname'] = $data['firstname'];
                if (isset($data['lastname'])) $updateData['lastname'] = $data['lastname'];
                if (isset($data['gender'])) $updateData['gender'] = $data['gender'];
                if (isset($data['birth_date'])) $updateData['birth_date'] = $data['birth_date'];
                if (isset($data['email'])) $updateData['email'] = $data['email'];

                // Allow setting national_id only if it's not set yet
                if (isset($data['national_id']) && (!isset($user->national_id) || empty($user->national_id))) {
                    $updateData['national_id'] = $data['national_id'];
                    $nationalIdChanged = true;
                }
            }
        }

        // Only proceed with update if there's data to update
        if (!empty($updateData)) {
            $updated = $user->update($updateData);

            // If national_id changed and update was successful, dispatch verification job
            if ($nationalIdChanged) {
                VerifyUserProfile::dispatch($user->id);
            }

            return $updated;
        }

        return false;
    }

    public function deleteRecordById($id){

    }
}
