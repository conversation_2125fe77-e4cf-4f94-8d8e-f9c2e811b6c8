<?php

namespace App\Repositories\User;

use App\Models\TomanWithdrawal;
use App\Models\User;
use App\Repositories\Repository;
use App\Repositories\RepositoriesContract;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TomanWithdrawalRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        $user = User::findOrFail(Auth::user()->id);
        return TomanWithdrawal::where('user_id', $user->id)
            ->with('card.bank')
            ->orderBy('id', 'desc')
            ->get();
    }

    public function createRecord(array $data)
    {
        DB::beginTransaction();
        try {
            $user = User::findOrFail(Auth::user()->id);

            // Calculate fee (if needed)
            $fee = 0; // You can implement fee calculation logic here

            // Check if user has enough balance
            if ($user->toman_balance < $data['amount'] + $fee) {
                return [
                    'success' => false,
                    'message' => 'موجودی کافی نیست'
                ];
            }

            // Create withdrawal record
            $withdrawal = TomanWithdrawal::create([
                'user_id' => $user->id,
                'card_id' => $data['card_id'],
                'amount' => $data['amount'],
                'fee' => $fee,
                'status' => 'pending',
                'ip' => request()->ip() // Capture IP address
            ]);

            // Store initial balance before deduction
            $balanceBefore = $user->toman_balance;

            // Deduct amount from user's toman balance
            $user->toman_balance -= ($data['amount'] + $fee);
            $user->save();

            // Create transaction record
            $transaction = $user->transactions()->create([
                'type' => 'withdraw',
                'amount' => $data['amount'],
                'currency_id' => 6, // Assuming 6 is the ID for Toman/IRR
                'card_id' => $data['card_id'],
                'status' => 'pending',
                'description' => 'برداشت تومانی',
                'balance_before' => $balanceBefore,
                'balance_after' => $user->toman_balance,
            ]);

            DB::commit();

            return [
                'success' => true,
                'data' => $withdrawal,
                'message' => 'درخواست برداشت با موفقیت ثبت شد'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'خطا در ثبت درخواست برداشت: ' . $e->getMessage()
            ];
        }
    }

    public function getRecordById($id)
    {
        return TomanWithdrawal::where('id', $id)
            ->where('user_id', Auth::user()->id)
            ->with('card.bank')
            ->firstOrFail();
    }

    public function updateRecord($id, array $data)
    {
        // This method is not needed for user withdrawal as they can't update withdrawals
    }

    public function deleteRecordById($id)
    {
        // This method is not needed as users shouldn't be able to delete withdrawals
    }
}
