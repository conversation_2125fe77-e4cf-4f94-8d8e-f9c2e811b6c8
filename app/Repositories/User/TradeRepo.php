<?php

namespace App\Repositories\User;

use App\CurrencyProviders\NowNodes;
use App\CurrencyProviders\TronProvider;
use App\Models\Currency;
use App\Models\Transaction;
use App\Models\UserWallet;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Mollsoft\LaravelTronModule\Facades\Tron;
use Mollsoft\LaravelTronModule\Models\TronAddress;
use Mollsoft\LaravelTronModule\Models\TronWallet;


class TradeRepo extends Repository implements RepositoriesContract
{

    public function __construct(
        // protected TronProvider $tronProvider,
        protected NowNodes $provider,
    ){}

    public function getAllRecords($request){

    }

    public function createRecord(array $data){
        $data = [
            'type' => $data['type'],
            'amount' => $data['amount'],
            'user_id' => Auth::user()->id,
        ];
        // if($data['currency_id'] == 5){
        //     $this->tronProvider->buy($data);
        // }

        return [];
    }

    public function getRecordById($id){
        $currency = Currency::findOrFail($id);
        $userId = Auth::user()->id;
        if($currency->id == 5){
            // return $this->tronProvider->deposit(data: ['user_id' => $userId]);
            // return $this->provider->deposit($currency);
            return $this->provider->wallet($currency);
        }

    }

    public function updateRecord($id, array $data){

    }

    public function deleteRecordById($id){

    }
}
