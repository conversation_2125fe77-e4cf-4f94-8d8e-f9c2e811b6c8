<?php

namespace App\Repositories\User;

use App\Jobs\Zibal\CheckCardWithNationalCode;
use App\Models\Bank;
use App\Models\Card;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use Illuminate\Support\Facades\Auth;

class CardRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        return Card::where('user_id', Auth::user()->id)->with('bank')->get();
    }

    public function createRecord(array $data)
    {
        $user = Auth::user();
        $card = Card::create([
            'user_id' => $user->id,
            'bank_id' => $this->detectBank($data['number']),
            'number' => $data['number'],
            'sheba' => null,
            'iban' => null,
            'status' => 'pending',
        ]);
        CheckCardWithNationalCode::dispatch($user->id, $card->id)->delay(now()->addMinutes(1));

        return $card;
    }

    public function getRecordById($id) {
        return Card::where('id', $id)->where('user_id', Auth::user()->id)->first();
    }

    public function updateRecord($id, array $data) {

    }

    public function deleteRecordById($id) {

    }

    protected function detectBank(string $number): int
    {
        $bank = Bank::where('prefix', substr($number, 0, 6))->first('id');

        return $bank->id;
    }
}
