<?php

namespace App\Repositories\User;

use App\Models\Transaction;
use App\Providers\CryptoTransactionServiceProvider;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;
use Illuminate\Support\Facades\Auth;

class DepositRepo extends Repository implements RepositoriesContract
{
    public function __construct(
        // protected CryptoTransactionServiceProvider $cryptoTransaProvider
    ) {}

    public function getAllRecords($request = null)
    {
        return Transaction::where('user_id', Auth::user()->id)->get();
    }

    public function createRecord(array $data)
    {
        // $transactionId = $data['transactionId'];
        $network = $data['network'] ?? null;
        Transaction::create([
            'type' => 'deposit',
            'amount' => $data['amount'],
            'wallet_id' => $data['wallet_id'],
            'wallet_address' => $data['wallet_address'],
            'currency_id' => $data['currency_id'],
            'user_id' => Auth::user()->id,
            'registrar' => $network,
            'network' => $network,
            'status' => 'pending',
            'description' => $data['description'] ?? '',
            'details' => $data['details'] ?? null,
        ]);

        // return $this->cryptoTransaProvider->checkTransaction($currency, $transactionId, $network);

    }

    public function getRecordById($id) {}

    public function updateRecord($id, array $data) {}

    public function deleteRecordById($id) {}
}
