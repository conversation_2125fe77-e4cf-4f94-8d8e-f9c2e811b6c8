<?php

namespace App\Observers;

use App\Models\Transaction;
use Illuminate\Support\Facades\DB;

class TransactionObserver
{
    public function created(Transaction $transaction)
    {
        DB::beginTransaction();

        try {
            // Check if this is a Toman transaction (assuming currency_id for Toman is different from others)
            // You need to replace TOMAN_CURRENCY_ID with the actual ID for Toman currency
            $isTomanTransaction = $transaction->currency_id == 3; // Assuming 3 is the ID for Toman/IRR

            if ($isTomanTransaction) {
                // موجودی تومانی فعلی کاربر قبل از تراکنش
                $userTomanBalanceBeforeTransaction = $transaction->user->toman_balance;

                // موجودی تومانی بعد از تراکنش
                $userTomanBalanceAfterTransaction = ($transaction->type == 'increase') ?
                    $userTomanBalanceBeforeTransaction + $transaction->amount :
                    $userTomanBalanceBeforeTransaction - $transaction->amount;

                // ذخیره مقادیر در تراکنش
                $transaction->balance_before = $userTomanBalanceBeforeTransaction;
                $transaction->balance_after = $userTomanBalanceAfterTransaction;

                // بروز رسانی موجودی تومانی کاربر
                $transaction->user->toman_balance = $userTomanBalanceAfterTransaction;
            } else {
                // موجودی فعلی کاربر قبل از تراکنش
                $userBalanceBeforeTransaction = $transaction->user->balance;

                // موجودی بعد از تراکنش
                $userBalanceAfterTransaction = ($transaction->type == 'increase') ?
                    $userBalanceBeforeTransaction + $transaction->amount :
                    $userBalanceBeforeTransaction - $transaction->amount;

                // ذخیره مقادیر در تراکنش
                $transaction->balance_before = $userBalanceBeforeTransaction;
                $transaction->balance_after = $userBalanceAfterTransaction;

                // بروز رسانی موجودی کاربر
                $transaction->user->balance = $userBalanceAfterTransaction;
            }

            $transaction->user->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
        }
    }

    public function updated(Transaction $transaction)
    {
        DB::beginTransaction();

        try {
            // Check if this is a Toman transaction
            $isTomanTransaction = $transaction->currency_id == 3; // Assuming 3 is the ID for Toman/IRR

            if ($isTomanTransaction) {
                // موجودی تومانی فعلی کاربر قبل از تراکنش
                $userTomanBalanceBeforeTransaction = $transaction->user->toman_balance;

                // if($transaction->status == 'declined'){
                $userTomanBalanceAfterTransaction = $userTomanBalanceBeforeTransaction + $transaction->amount;
                // }
                // موجودی تومانی بعد از تراکنش

                // ذخیره مقادیر در تراکنش
                $transaction->balance_before = $userTomanBalanceBeforeTransaction;
                $transaction->balance_after = $userTomanBalanceAfterTransaction;

                // بروز رسانی موجودی تومانی کاربر
                $transaction->user->toman_balance = $userTomanBalanceAfterTransaction;
            } else {
                // موجودی فعلی کاربر قبل از تراکنش
                $userBalanceBeforeTransaction = $transaction->user->balance;

                // if($transaction->status == 'declined'){
                $userBalanceAfterTransaction = $userBalanceBeforeTransaction + $transaction->amount;
                // }
                // موجودی بعد از تراکنش

                // ذخیره مقادیر در تراکنش
                $transaction->balance_before = $userBalanceBeforeTransaction;
                $transaction->balance_after = $userBalanceAfterTransaction;

                // بروز رسانی موجودی کاربر
                $transaction->user->balance = $userBalanceAfterTransaction;
            }

            $transaction->user->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
        }
    }
}
