<?php

namespace App\Jobs\Zibal;

use App\Models\Card;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;

class CardToShebaJob implements ShouldQueue
{
    use Queueable;

    public $cardNumber;

    /**
     * Create a new job instance.
     */
    public function __construct($cardNumber)
    {
        $this->cardNumber = $cardNumber;

        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.env('ZIBAL_API'),
        ])->post('https://api.zibal.ir/v1/facility/cardToIban/', [
            'cardNumber' => $this->cardNumber,
        ]);

        $card = Card::where('number', $this->cardNumber);
        if ($response['result'] == 1) {
            $card->update([
                'sheba' => $response['data']['IBAN'],
            ]);
        }

    }
}
