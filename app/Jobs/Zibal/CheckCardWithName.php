<?php

namespace App\Jobs\Zibal;

use App\Models\Card;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class CheckCardWithName implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $cardNumber;

    public $name;

    public function __construct($cardNumber, $name)
    {
        $this->cardNumber = $cardNumber;
        $this->name = $name;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Logic to check if card number and national id match
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.env('ZIBAL_API'),
        ])->post('https://api.zibal.ir/v1/facility/checkCardWithName', [
            'name' => $this->name,
            'cardNumber' => $this->cardNumber,
        ]);
        $card = Card::where('number', $this->cardNumber);
        $response = json_decode($response->body(), true);
        if ($response['data']['matched'] == 'true') {
            $card->update([
                'status' => 'approved',
            ]);
            // send sms that its approved
        } else {
            $card->delete();
            // send sms that is rejected
        }

    }
}
