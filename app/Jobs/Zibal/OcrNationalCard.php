<?php

namespace App\Jobs\Zibal;

use App\Models\Document;
use App\Models\User;
// use CURLFile;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class OcrNationalCard implements ShouldQueue
    // , ShouldBeUnique
{
    use Queueable;

    /**
     * The user instance.
     *
     * @var \App\Models\User
     */
    // public $user;

    protected $frontDocument;

    protected $backDocument;

    /**
     * Create a new job instance.
     */
    public function __construct($frontDocument, $backDocument)
    {
        $this->frontDocument = $frontDocument;
        $this->backDocument = $backDocument;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $frontDocument = Document::with('file')->find($this->frontDocument['id']);
        $backDocument = Document::with('file')->find($this->backDocument['id']);
        $frontPath = storage_path('app/public/'.$frontDocument->file->url);
        $backPath = storage_path('app/public/'.$backDocument->file->url);

        $endpoint = 'https://api.zibal.ir/v1/facility/nationalCardOcr';
        $apiKey = env('ZIBAL_API');

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => [
                'nationalCardFront' => new \CURLFile($frontPath, 'image/jpeg', 'front.jpg'),
                'nationalCardBack' => new \CURLFile($backPath, 'image/jpeg', 'back.jpg'),
            ],
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer $apiKey",
                'Content-Type: multipart/form-data',
            ],
        ]);

        $response = curl_exec($curl);
        $content = json_decode($response, true);
        
        if ($content['result'] === 21) {
            $frontDocument->update(['status' => 'rejected']);
            $backDocument->update(['status' => 'rejected']);
            
            // Create alert for user
            \App\Models\Alert::create([
                'user_id' => $frontDocument->user_id,
                'type' => 'error',
                'message' => 'در تصاویر ارسالی کارت ملی شناسایی نشد. لطفا مجددا با کیفیت بهتر ارسال کنید.'
            ]);
            
            return;
        }
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            OcrNationalCard::dispatch($frontDocument, $backDocument)->delay(now()->addMinutes(2));
            Log::error('cURL Error #:'.$err);
        } else {
            $content = json_decode($response, true);
            Log::debug($content);
            if ($content['result'] == '1') {
                $user = User::find($frontDocument->user_id);
                $user->update([
                    'firstname' => $content['data']['front']['firstName'],
                    'lastname' => $content['data']['front']['lastName'],
                    'fatherName' => $content['data']['front']['fatherName'],
                    'birth_date' => $content['data']['front']['birthDate'],
                    'serialCard' => $content['data']['back']['serialCard'],
                    'national_id' => $content['data']['front']['nationalCode'],
                    'level' => 2,
                ]);
                $frontDocument->update([
                    'status' => 'approved',
                ]);
                $backDocument->update([
                    'status' => 'approved',
                ]);
            }
        }
    }

    // public function uniqueId(): string
    // {
    //     return $this->user->id;
    // }
}
