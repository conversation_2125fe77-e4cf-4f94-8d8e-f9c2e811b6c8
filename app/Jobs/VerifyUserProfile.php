<?php

namespace App\Jobs;

use App\Models\Alert;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class VerifyUserProfile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 5;

    protected $userId;

    public function __construct($userId)
    {
        $this->userId = $userId;
    }

    public function handle()
    {
        $user = User::find($this->userId);
        if (! $user) {
            return;
        }

        $apiResponse = $this->verifyWithExternalApi($user);
        // Log::info($apiRespons->message);
        if ($apiResponse->status == 'verified') {
            Alert::firstOrCreate([
                'message' => 'نتیجه بررسی کد ملی با موفقیت انجام شد و به سطح ۲ ارتقاء یافتید.',
            ],[
                'type' => 'success',
                'user_id' => $user->id,
            ]);
            $user->update([
                'level' => 2,
            ]);
        } else {
            $user->update([
                'status' => 'rejected',
            ]);
            Alert::firstOrCreate([
                'message' => 'نتیجه بررسی تطابق کد ملی با شماره موبایل ناموفق بود , لطفا کد ملی خود را اصلاح کنید',
            ],[
                'type' => 'error',
                'user_id' => $user->id,
            ]);
        }
    }

    private function verifyWithExternalApi($user)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.env('ZIBAL_API'),
        ])->post('https://api.zibal.ir/v1/facility/shahkarInquiry', [
            'mobile' => $user->phone,
            'nationalCode' => $user->national_id,
        ]);
        $response = json_decode($response->body(), true);
        if ($response['result'] == 1 && isset($response['data']['matched']) && $response['data']['matched'] === true) {
            return (object) ['status' => 'verified'];
        } else {
            return (object) ['status' => 'failed'];
        }
    }

}
