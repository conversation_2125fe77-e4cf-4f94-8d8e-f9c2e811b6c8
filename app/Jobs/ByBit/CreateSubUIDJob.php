<?php

namespace App\Jobs\ByBit;

use App\Models\RandomString;
use App\Repositories\ByBit\UserRepo;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Str;

class CreateSub<PERSON><PERSON><PERSON>ob implements ShouldQueue
{
    use Queueable;

    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(
        $userId
    ) {
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(UserRepo $createUserRepo): void
    {
        $params = [
            'user_id' => $this->userId,
            'username' => 'laracoin'.$this->userId,
            'password' => $this->randomString(),
            'remarks' => 'Spot,Futures,Margin',
        ];
        $createUserRepo->createRecord($params);

    }

    protected function randomString($length = 10): string
    {
        $string = Str::random($length).rand(0, 9);
        $existRandomString = RandomString::where('string', $string)->exists();
        if ($existRandomString) {
            $this->randomString();
        }
        RandomString::create(['string' => $string]);

        return $string;
    }
}
