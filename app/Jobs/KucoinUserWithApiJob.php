<?php

namespace App\Jobs;

use App\Models\RandomString;
use App\Repositories\Kucoin\AccountApiRepo;
use App\Repositories\Kucoin\AccountRepo;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Str;

class KucoinUser<PERSON>ith<PERSON><PERSON>Job implements ShouldQueue
{
    use Queueable;

    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(
        $userId
    ) {
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(AccountRepo $accountRepo, AccountApiRepo $accountApiRepo): void
    {
        $params = [
            'user_id' => $this->userId,
            'password' => $this->randomString(10),
            'remarks' => null,
            'subName' => $this->randomString(10),
            'access' => 'Spot,Futures,Margin',
        ];
        $account = $accountRepo->createRecord($params);
        $accountApi = $accountApiRepo->createRecord($account);

    }

    protected function randomString($length = 10): string
    {
        $string = Str::random($length).rand(0, 9);
        $existRandomString = RandomString::where('string', $string)->exists();
        if ($existRandomString) {
            $this->randomString();
        }
        RandomString::create(['string' => $string]);

        return $string;
    }
}
