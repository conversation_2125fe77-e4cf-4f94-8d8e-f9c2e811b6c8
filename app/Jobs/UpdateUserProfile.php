<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class UpdateUserProfile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userId;

    protected $data;

    protected $jobId; // متغیر برای ذخیره شناسه شغل

    public function __construct($userId, $data)
    {
        $this->userId = $userId;
        $this->data = $data;
    }

    public function handle()
    {
        $this->jobId = $this->job->getJobId();
        $user = User::find($this->userId);
        if ($user) {
            $user->update($this->data);
        }
    }

    public static function deletePreviousJob($userId)
    {
        $jobKey = "user_profile_verification:$userId";

        $previousJob = DB::table('jobs')
            ->where('queue', 'default') // تنظیم براساس نام صف شما
            ->where('payload', 'LIKE', '%"userId":'.$userId.'%') // بررسی ساختار payload
            ->orderBy('created_at', 'desc') // دریافت جدیدترین شغل
            ->first();
        $previousJob = DB::table('jobs')->where('id', 95)->first();
        $decode = json_decode($previousJob->payload, false)->data->command;
        if ($previousJob) {
            // حذف شغل قبلی از جدول jobs
            DB::table('jobs')->where('id', $previousJob->id)->delete();
        }
        Redis::delete($jobKey);
    }

    // متد برای دریافت شناسه شغل
    public function getJobId()
    {
        return $this->jobId;
    }
}
