<?php

namespace App\Services\User;

use App\Repositories\User\DepositRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class DepositService extends Service implements ServicesContract
{
    public function __construct(
        protected DepositRepo $repo,
    ) {}

    public function getAllDeposits()
    {
        return $this->repo->getAllRecords();
    }

    public function createDeposit($validated)
    {
        return $this->repo->createRecord($validated);
    }

    public function getDeposit($id)
    {
        return $this->repo->getRecordById($id);
    }
}
