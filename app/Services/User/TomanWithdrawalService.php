<?php

namespace App\Services\User;

use App\Repositories\User\TomanWithdrawalRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class TomanWithdrawalService extends Service implements ServicesContract
{
    public function __construct(
        protected TomanWithdrawalRepo $repo,
    ) {}

    public function withdrawalList($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function createWithdrawal($request)
    {
        return $this->repo->createRecord($request);
    }

    public function showWithdrawal($id)
    {
        return $this->repo->getRecordById($id);
    }
}
