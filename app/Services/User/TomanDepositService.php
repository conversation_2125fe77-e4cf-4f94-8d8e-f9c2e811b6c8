<?php

namespace App\Services\User;

use App\Models\Transaction;
use App\Models\User;
use App\Services\Service;
use App\Services\ServicesContract;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TomanDepositService extends Service implements ServicesContract
{
    /**
     * Get all deposits for the authenticated user
     *
     * @param $request
     * @return mixed
     */
    public function depositList($request)
    {
        return Transaction::where('user_id', Auth::id())
            ->where('type', 'deposit')
            ->where('currency_id', 6) // Toman/IRR currency ID
            ->orderBy('id', 'desc')
            ->get();
    }

    /**
     * Create a new deposit request
     *
     * @param array $data
     * @return array
     */
    public function createDeposit(array $data)
    {
        DB::beginTransaction();
        try {
            $user = User::findOrFail(Auth::id());
            
            // Store initial balance before addition
            $balanceBefore = $user->toman_balance;
            
            // Add amount to user's toman balance
            $user->toman_balance += $data['amount'];
            $user->save();
            
            // Create transaction record
            $transaction = $user->transactions()->create([
                'type' => 'deposit',
                'amount' => $data['amount'],
                'currency_id' => 6, // Toman/IRR currency ID
                'status' => 'pending', // Admin needs to approve
                'description' => $data['description'] ?? 'واریز تومانی',
                'transaction_id' => $data['transaction_id'],
                'balance_before' => $balanceBefore,
                'balance_after' => $user->toman_balance,
                'ip' => request()->ip() // Capture IP address
            ]);
            
            DB::commit();
            
            return [
                'success' => true,
                'data' => $transaction,
                'message' => 'درخواست واریز با موفقیت ثبت شد و در انتظار تایید است'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'خطا در ثبت درخواست واریز: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get a specific deposit by ID
     *
     * @param int $id
     * @return mixed
     */
    public function showDeposit($id)
    {
        return Transaction::where('id', $id)
            ->where('user_id', Auth::id())
            ->where('type', 'deposit')
            ->where('currency_id', 6) // Toman/IRR currency ID
            ->firstOrFail();
    }
}
