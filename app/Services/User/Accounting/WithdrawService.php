<?php

namespace App\Services\User\Accounting;

use App\Repositories\User\Accounting\WithdrawRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class WithdrawService extends Service implements ServicesContract
{
    public function __construct(
        protected WithdrawRepo $repo,
    ) {}

    public function withdrawList($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function createWithdraw($request)
    {
        $create = $this->repo->createRecord($request);

        return $this->repo->getRecordById($create->id);
    }

    public function showWithdraw($id)
    {
        return $this->repo->getRecordById($id);
    }
}
