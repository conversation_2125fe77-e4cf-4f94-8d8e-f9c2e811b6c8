<?php

namespace App\Services\User;

use App\Repositories\User\CardRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class CardService extends Service implements ServicesContract
{
    public function __construct(
        protected CardRepo $repo
    ) {}

    public function listCards($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function createCard($validated)
    {
        return $this->repo->createRecord($validated);
    }

    public function showCard($id)
    {
        return $this->repo->getRecordById($id);
    }
}
