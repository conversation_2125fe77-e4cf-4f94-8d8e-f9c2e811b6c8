<?php

namespace App\Services\User;

use App\Models\Document;
use App\Services\FileDataService;
use App\Services\Service;
use App\Services\ServicesContract;
use Illuminate\Support\Facades\Auth;
use Spatie\Image\Enums\Fit;
use Spatie\Image\Enums\ImageDriver;
use Spatie\Image\Image;

class DocumentService extends Service implements ServicesContract
{
    public function __construct(
        protected FileDataService $fileDataService,
    ) {}

    public function storeDocument($validated)
    {
        $user = request()->user();
        $oldDocument = $user->documents()->where('user_id', Auth::user()->id)->where('name', $validated['name'])->first();
        if (isset($oldDocument)) {
            $oldDocument->delete();
            // $this->fileDataService->deleteFile($oldDocument->);
        }
        $file = $validated['file'];
        $extension = $file->extension();
        $type = match ($validated['name']) {
            'id' => 'image',
            'id_back' => 'image',
            'consent' => $extension === 'pdf' ? 'document' : 'image',
            'selfie' => 'video',
        };
        switch ($extension) {
            case 'jpeg':
            case 'jpg':
            case 'png':
            case 'webp':
                if ($type != 'image') {
                    break;
                }
                Image::useImageDriver(ImageDriver::Gd)->loadFile($file->getRealPath())
                    ->format('webp')
                    ->quality(90)
                    ->fit(Fit::Contain, 1024, 768)
                    ->save();
                $extension = 'webp';
                break;
            case 'mp4':
                break;
            case 'pdf':
                // PDF files are stored as-is without processing
                break;
        }
        $document = $user->documents()->create([
            'name' => $validated['name'],
            'type' => $type,
        ]);
        $this->fileDataService->createFile($file, $document);

        // return $document;
        return Document::with('file')->findOrFail($document->id);

    }
}
