<?php

namespace App\Services\User;

use App\Repositories\User\WalletRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class WalletService extends Service implements ServicesContract
{
    public function __construct(
        protected WalletRepo $repo,
    ){}

    public function getAllWallets($request){
        return $this->repo->getAllRecords($request);
    }

    public function storeWallet($request){
        return $this->repo->createRecord($request);
    }

    public function showWallet($id){
        return $this->repo->getRecordById($id);
    }

    public function updateWallet($validated, $id){
        return $this->repo->updateRecord($validated, $id);
    }

}
