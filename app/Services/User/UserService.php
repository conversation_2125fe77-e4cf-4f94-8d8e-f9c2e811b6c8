<?php

namespace App\Services\User;

use App\Models\User;
use App\Repositories\User\MainUserRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class UserService extends Service implements ServicesContract
{

    public function __construct(
        protected MainUserRepo $repo,
    ) {}
    public function show($id)
    {
        return $this->repo->getRecordById($id);
    }

    public function update($id, $data)
    {
        $this->repo->updateRecord($id, $data);
        return $this->repo->getRecordById($id);
    }
}
