<?php

namespace App\Services\User;

use App\Repositories\User\TradeRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class TradeService extends Service implements ServicesContract
{
    public function __construct(
        protected TradeRepo $repo,
    ){

    }
    public function storeTrade($validated){
        return $this->repo->createRecord($validated);
    }

    public function showTradeData($id){
        return $this->repo->getRecordById($id);
    }
}
