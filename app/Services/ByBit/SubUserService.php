<?php

namespace App\Services\ByBit;

use App\Repositories\ByBit\UserRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class SubUserService extends Service implements ServicesContract
{
    public function __construct(
        protected UserRepo $repo,
    ) {}

    public function indexAllSubUsers()
    {
        return $this->repo->getAllRecords();
    }

    public function createSubUser($validated)
    {
        return $this->repo->createRecord($validated);
    }
}
