<?php

namespace App\Services\ByBit;

use App\Repositories\ByBit\WalletRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class WalletService extends Service implements ServicesContract
{
    public function __construct(
        protected WalletRepo $repo,
    ) {}

    public function getAllWallets($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function createWallet($validated)
    {
        return $this->repo->createRecord($validated);
    }
}
