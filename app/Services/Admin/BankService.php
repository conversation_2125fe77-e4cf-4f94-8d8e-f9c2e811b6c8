<?php

namespace App\Services\Admin;

use App\Models\Bank;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class BankService
{
    /**
     * Get list of all banks
     */
    public function listBanks()
    {
        return Bank::paginate(15);
    }

    /**
     * Create a new bank
     */
    public function createBank($request)
    {
        try {
            DB::beginTransaction();
            
            $bank = new Bank();
            $bank->name = $request->name;
            $bank->prefix = $request->prefix;
            
            // Handle logo upload
            if ($request->hasFile('icon')) {
                $file = $request->file('icon');
                $fileName = 'bank_' . time() . '.' . $file->getClientOriginalExtension();
                $file->storeAs('public/bankicons', $fileName);
                $bank->icon = "bankicons/$fileName";
            }
            
            $bank->save();
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => 'بانک با موفقیت ایجاد شد',
                'data' => $bank
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'خطا در ایجاد بانک: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update an existing bank
     */
    public function updateBank($id, $request)
    {
        try {
            DB::beginTransaction();
            
            $bank = Bank::findOrFail($id);
            $bank->name = $request->name;
            $bank->prefix = $request->prefix;
            
            // Handle logo upload
            if ($request->hasFile('icon')) {
                // Delete old icon if exists
                if ($bank->icon) {
                    Storage::disk('public')->delete($bank->icon);
                }
                
                $file = $request->file('icon');
                $fileName = 'bank_' . time() . '.' . $file->getClientOriginalExtension();
                $file->storeAs('public/bankicons', $fileName);
                $bank->icon = "bankicons/$fileName";
            }
            
            $bank->save();
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => 'بانک با موفقیت بروزرسانی شد',
                'data' => $bank
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'خطا در بروزرسانی بانک: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete a bank
     */
    public function deleteBank($id)
    {
        try {
            $bank = Bank::findOrFail($id);
            
            // Delete bank icon if exists
            if ($bank->icon) {
                Storage::disk('public')->delete($bank->icon);
            }
            
            $bank->delete();
            
            return [
                'success' => true,
                'message' => 'بانک با موفقیت حذف شد'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'خطا در حذف بانک: ' . $e->getMessage()
            ];
        }
    }
}
