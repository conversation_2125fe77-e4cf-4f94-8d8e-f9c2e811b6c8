<?php

namespace App\Services\Admin;

use App\Repositories\Admin\AdminSettingRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class AdminSettingService extends Service implements ServicesContract
{
    public function __construct(
        protected AdminSettingRepo $repo,
    ) {}

    public function getAllSettings($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function storeSetting($validated)
    {
        return $this->repo->createRecord($validated);
    }

    public function getSetting($id)
    {
        return $this->repo->getRecordById($id);
    }

    public function updateSetting($id, $value)
    {
        $this->repo->updateRecord($id, $value);

        return $this->repo->getRecordById($id);
    }
}
