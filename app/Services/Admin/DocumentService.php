<?php

namespace App\Services\Admin;

use App\Repositories\Admin\DocumentRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class DocumentService extends Service implements ServicesContract
{
    public function __construct(
        protected DocumentRepo $repo,
    ) {}

    public function listDocuments($validated)
    {
        return $this->repo->getAllRecords($validated);
    }

    public function showDocument($id)
    {
        return $this->repo->getRecordById($id);
    }

    public function updateDocument($id, $validated)
    {
        $this->repo->updateRecord($id, $validated);

        return $this->repo->getRecordById($id);
    }

    public function bulkApproveUserDocuments($userId)
    {
        return $this->repo->bulkUpdateUserDocuments($userId, 'approved');
    }

    public function bulkRejectUserDocuments($userId, $description)
    {
        return $this->repo->bulkUpdateUserDocuments($userId, 'rejected', $description);
    }
}
