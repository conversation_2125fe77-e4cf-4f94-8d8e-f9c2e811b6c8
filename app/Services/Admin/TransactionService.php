<?php

namespace App\Services\Admin;

use App\Repositories\Admin\TransactionRepo;

class TransactionService
{
    public function __construct(
        protected TransactionRepo $repo,
    ) {}

    public function show($id)
    {
        return $this->repo->getRecordById($id);
    }

    public function update($id, array $data)
    {
        $this->repo->updateRecord($id, $data);

        return $this->repo->getRecordById($id);
    }

    public function index(array $filter, $userWallet = null)
    {
        return $this->repo->getAllRecords($filter, $userWallet);
    }

    /**
     * Get all transactions for export
     *
     * @param array $filter
     * @param mixed $userWallet
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllForExport(array $filter, $userWallet = null)
    {
        return $this->repo->getAllRecordsForExport($filter, $userWallet);
    }
}
