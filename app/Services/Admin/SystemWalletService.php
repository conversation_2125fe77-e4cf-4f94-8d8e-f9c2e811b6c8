<?php

namespace App\Services\Admin;

use App\Models\AdminWalletKey;
use App\Services\Service;
use Illuminate\Support\Facades\Log;
use PavloDotDev\LaravelTronModule\Facades\Tron;

class SystemWalletService extends Service
{
    public function createWallet(array $data)
    {
        try {
            // Generate mnemonic
            $mnemonic = Tron::mnemonicGenerate();
            
            // Create wallet name based on network
            $name = "system_wallet_" . $data['network_id'] . "_" . time();
            
            // Generate a secure password
            $password = bin2hex(random_bytes(16));
            
            // Create the Tron wallet
            $tronWallet = Tron::createWallet($name, $password, $mnemonic);
            $tronWallet->save();
            
            // Generate address
            $tronWallet->encrypted()->unlock($password);
            $tronAddress = Tron::createAddress($tronWallet);
            $tronAddress->save();

            // Save in admin wallet keys
            AdminWalletKey::create([
                'network_id' => $data['network_id'],
                'address' => $tronAddress->address,
                'private_key' => encrypt($tronAddress->private_key),
                'status' => $data['status'] ?? 1
            ]);

            return [
                'success' => true,
                'message' => 'کیف پول با موفقیت ایجاد شد'
            ];
        } catch (\Exception $e) {
            // Log the error
            Log::error('System Wallet Creation Error: ' . $e->getMessage(), [
                'exception' => $e,
                'data' => $data
            ]);

            return [
                'success' => false,
                'message' => 'خطا در ایجاد کیف پول: ' . $e->getMessage()
            ];
        }
    }

    public function updateWallet($id, array $data)
    {
        try {
            $wallet = AdminWalletKey::findOrFail($id);
            
            $wallet->update([
                'network_id' => $data['network_id'],
                'status' => $data['status'] ?? $wallet->status
            ]);

            return [
                'success' => true,
                'message' => 'کیف پول با موفقیت بروزرسانی شد'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'خطا در بروزرسانی کیف پول: ' . $e->getMessage()
            ];
        }
    }
}
