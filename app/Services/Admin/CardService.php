<?php

namespace App\Services\Admin;

use App\Repositories\Admin\CardRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class CardService extends Service implements ServicesContract
{
    public function __construct(
        protected CardRepo $repo
    ) {}

    public function listCards($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function showCard($id)
    {
        return $this->repo->getRecordById($id);
    }

    public function updateCard($id, $validated)
    {
        return $this->repo->updateRecord($id, $validated);
    }

    public function deleteCard($id)
    {
        return $this->repo->deleteRecordById($id);
    }
}
