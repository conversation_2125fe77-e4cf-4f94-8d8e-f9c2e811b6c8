<?php

namespace App\Services\Admin;

use App\Services\Service;
use App\Services\ServicesContract;
use App\Repositories\Admin\LevelRepo;

class LevelService extends Service implements ServicesContract
{
    
    public function __construct(
        protected LevelRepo $repo
    ){}

    public function levelsList($request){
        return $this->repo->getAllRecords($request);
    }

    public function storeLevels($data){
        
        $this->repo->createRecord($data);
        return $this->repo->getAllRecords([]); 
    }

}
