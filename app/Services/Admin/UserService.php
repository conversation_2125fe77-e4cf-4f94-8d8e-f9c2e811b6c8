<?php

namespace App\Services\Admin;

use App\Repositories\Admin\User\UserRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class UserService extends Service implements ServicesContract
{
    public function __construct(
        protected UserRepo $repo,
    ) {}

    public function index($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function show($id)
    {
        return $this->repo->getRecordById($id);
    }

    public function createUser($validated)
    {
        return $this->repo->createRecord($validated);
    }

    public function update($id, array $data)
    {
        $this->repo->updateRecord($id, $data);

        return $this->repo->getRecordById($id);
    }
}
