<?php

namespace App\Services\Admin;

use App\Repositories\Admin\CurrencyRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class CurrencyService extends Service implements ServicesContract
{
    public function __construct(
        protected CurrencyRepo $repo,
    ) {}

    public function allCurrencies($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function createCurrency(array $data)
    {
        return $this->repo->createRecord($data);
    }

    public function getCurrency($id)
    {
        return $this->repo->getRecordById($id);
    }

    public function updateCurrency($id, array $data)
    {
        $this->repo->updateRecord($id, $data);

        return $this->repo->getRecordById($id);
    }

    public function deleteCurrency($id)
    {
        return $this->repo->deleteRecordById($id);
    }
}
