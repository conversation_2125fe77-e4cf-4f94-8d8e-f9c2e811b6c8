<?php

namespace App\Services\Admin;

use App\Repositories\Admin\SupportRepo;
use App\Services\FileDataService;
use App\Services\Service;
use App\Services\ServicesContract;

class SupportService extends Service implements ServicesContract
{
    public function __construct(
        protected SupportRepo $repo,
        protected FileDataService $fileDataService,

    ) {}

    public function allTickets($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function newTicket($userId)
    {
        return $this->repo->newRecord($userId);
    }

    public function createTicket($validated)
    {
        $ticket = $this->repo->createRecord($validated);
        if (isset($validated['file'])) {
            $this->fileDataService->createFile($validated['file'], $ticket);
        }

        return $this->repo->getRecordById($ticket->support_id);
    }

    public function getSupportRequest($id)
    {
        return $this->repo->getRecordById($id);
    }

    public function updateTicket($id, $validated)
    {
        $this->repo->updateRecord($id, $validated);

        return $this->repo->getRecordById($id);
    }

    public function deleteTicket($id)
    {
        return $this->repo->deleteRecordById($id);
    }
}
