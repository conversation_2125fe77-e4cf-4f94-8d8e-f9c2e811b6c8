<?php

namespace App\Services\Admin;

use App\Repositories\Admin\FrequencyQuestionRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class FrequencyQuestionService extends Service implements ServicesContract
{
    public function __construct(
        protected FrequencyQuestionRepo $repo
    ){}

    public function allQuestions($request){
        return $this->repo->getAllRecords($request);
    }

    public function createQuestion($validated){
        $create = $this->repo->createRecord($validated);
        return $this->repo->getRecordById($create->id);
    }

    public function showQuestion($id){
        return $this->repo->getRecordById($id);
    }

    public function updateQuestion($id, $data){
        $this->repo->updateRecord($id, $data);
        return $this->repo->getRecordById($id);
    }

    public function deleteQuestion($id){
        return $this->repo->deleteRecordById($id);
    }

}
