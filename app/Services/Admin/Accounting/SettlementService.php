<?php

namespace App\Services\Admin\Accounting;

use App\Models\Transaction;
use App\Repositories\Admin\Accounting\SettlementRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class SettlementService extends Service implements ServicesContract
{
    public function __construct(
        protected SettlementRepo $repo
    ) {}

    public function settlementList($request)
    {
        return $this->repo->getAllRecords($request);
    }

    public function storeSettlement($validated)
    {

    }

    public function showSettlement($id) {
        return Transaction::with(['wallet', 'user', 'registrar'])->findOrFail($id);
    }

    public function updateSettlement($id, $validated) {
        return [];
    }

    public function destroySettlement($id) {

    }
}
