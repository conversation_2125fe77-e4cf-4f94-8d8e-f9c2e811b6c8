<?php

namespace App\Services\Admin\Accounting;

use App\Repositories\Admin\Accounting\SettlementInfoRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class SettlementInfoService extends Service implements ServicesContract
{
    public function __construct(
        protected SettlementInfoRepo $repo
    ) {}

    public function settlementList($request){
        return $this->repo->getAllRecords($request);
    }

    public function storeSettlement($validated){
        $settings = [
            [
                'code' => 'withdrawSettlementActive',
                'value' => $validated['withdrawSettlementActive'],
                'type' => 'boolean',
            ],
            [
                'code' => 'sellSettlementActive',
                'value' => $validated['sellSettlementActive'],
                'type' => 'boolean',
            ],
            [
                'code' => 'settlementMonthlyProfit',
                'value' => $validated['settlementMonthlyProfit'],
                'type' => 'integer',
            ],
            [
                'code' => 'quickSettlementLoss',
                'value' => $validated['quickSettlementLoss'],
                'type' => 'integer',
            ],
            [
                'code' => 'settlementService',
                'value' => $validated['settlementService'],
                'type' => 'multi',
            ],
            [
                'code' => 'settlementMaxDaysProfit',
                'value' => $validated['settlementMaxDaysProfit'],
                'type' => 'integer',
            ],
            [
                'code' => 'settlementTodayProfit',
                'value' => $validated['settlementTodayProfit'],
                'type' => 'boolean',
            ],
        ];

        $this->repo->createRecord($settings);
        return $this->repo->getAllRecords([]);
    }
}
