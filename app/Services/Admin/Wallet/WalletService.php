<?php

namespace App\Services\Admin\Wallet;

use App\Repositories\Admin\Wallet\WalletRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class WalletService extends Service implements ServicesContract
{
    public function __construct(
        protected WalletRepo $repo
    ) {}

    public function listWallets($request)
    {
        return $this->repo->getAllRecords($request);
    }
}
