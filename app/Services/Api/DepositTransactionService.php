<?php

namespace App\Services\Api;

use App\Models\DepositeTransaction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DepositTransactionService
{
    /**
     * Get user deposit transactions with filters and pagination
     *
     * @param array $filters
     * @return array
     */
    public function getUserDepositTransactions(array $filters): array
    {
        $user = Auth::user();
        
        $query = DepositeTransaction::query()
            ->forUser($user->id)
            ->with(['coin', 'network', 'senderWallet', 'receiverWallet', 'user']);

        // Apply filters
        $this->applyFilters($query, $filters);

        // Apply sorting
        $this->applySorting($query, $filters);

        // Get paginated results
        $transactions = $query->paginate(
            $filters['per_page'],
            ['*'],
            'page',
            $filters['page']
        );

        // Get summary statistics
        $summary = $this->getTransactionsSummary($user->id, $filters);

        return [
            'transactions' => $transactions->items(),
            'pagination' => [
                'current_page' => $transactions->currentPage(),
                'last_page' => $transactions->lastPage(),
                'per_page' => $transactions->perPage(),
                'total' => $transactions->total(),
                'from' => $transactions->firstItem(),
                'to' => $transactions->lastItem(),
                'has_more_pages' => $transactions->hasMorePages(),
            ],
            'summary' => $summary,
        ];
    }

    /**
     * Apply filters to the query
     *
     * @param Builder $query
     * @param array $filters
     * @return void
     */
    private function applyFilters(Builder $query, array $filters): void
    {
        // Filter by status
        if (!empty($filters['status'])) {
            $query->byStatus($filters['status']);
        }

        // Filter by coin type
        if (!empty($filters['coin_type'])) {
            $query->byCoinType($filters['coin_type']);
        }

        // Filter by coin ID
        if (!empty($filters['coin_id'])) {
            $query->where('coin_id', $filters['coin_id']);
        }

        // Filter by network ID
        if (!empty($filters['network_id'])) {
            $query->where('network_id', $filters['network_id']);
        }

        // Filter by date range
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->byDateRange($filters['start_date'], $filters['end_date']);
        } elseif (!empty($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        } elseif (!empty($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        // Filter by amount range
        if (!empty($filters['min_amount'])) {
            $query->where('amount', '>=', $filters['min_amount']);
        }

        if (!empty($filters['max_amount'])) {
            $query->where('amount', '<=', $filters['max_amount']);
        }

        // Filter by transaction ID
        if (!empty($filters['transaction_id'])) {
            $query->where('transaction_id', 'like', '%' . $filters['transaction_id'] . '%');
        }

        // Filter by from address
        if (!empty($filters['from_address'])) {
            $query->where('from_address', 'like', '%' . $filters['from_address'] . '%');
        }

        // Filter by address
        if (!empty($filters['address'])) {
            $query->where('address', 'like', '%' . $filters['address'] . '%');
        }
    }

    /**
     * Apply sorting to the query
     *
     * @param Builder $query
     * @param array $filters
     * @return void
     */
    private function applySorting(Builder $query, array $filters): void
    {
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';

        $query->orderBy($sortBy, $sortDirection);

        // Secondary sort by ID for consistency
        if ($sortBy !== 'id') {
            $query->orderBy('id', 'desc');
        }
    }

    /**
     * Get transactions summary statistics
     *
     * @param int $userId
     * @param array $filters
     * @return array
     */
    private function getTransactionsSummary(int $userId, array $filters): array
    {
        $baseQuery = DepositeTransaction::forUser($userId);

        // Apply same filters for summary (except pagination)
        $summaryFilters = $filters;
        unset($summaryFilters['per_page'], $summaryFilters['page'], $summaryFilters['sort_by'], $summaryFilters['sort_direction']);
        
        $summaryQuery = clone $baseQuery;
        $this->applyFilters($summaryQuery, $summaryFilters);

        // Get summary data
        $summary = $summaryQuery->select([
            DB::raw('COUNT(*) as total_transactions'),
            DB::raw('SUM(CASE WHEN status = "confirmed" THEN 1 ELSE 0 END) as confirmed_transactions'),
            DB::raw('SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_transactions'),
            DB::raw('SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_transactions'),
            DB::raw('SUM(amount) as total_amount'),
            DB::raw('SUM(CASE WHEN status = "confirmed" THEN amount ELSE 0 END) as confirmed_amount'),
            DB::raw('SUM(fees) as total_fees'),
            DB::raw('SUM(doller) as total_usd_value'),
            DB::raw('AVG(amount) as average_amount'),
            DB::raw('MAX(amount) as max_amount'),
            DB::raw('MIN(amount) as min_amount'),
        ])->first();

        // Get status breakdown
        $statusBreakdown = $baseQuery->select([
            'status',
            DB::raw('COUNT(*) as count'),
            DB::raw('SUM(amount) as total_amount'),
        ])
        ->groupBy('status')
        ->get()
        ->keyBy('status');

        // Get coin breakdown
        $coinBreakdown = $baseQuery->with('coin')
            ->select([
                'coin_id',
                'coin_type',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as total_amount'),
            ])
            ->groupBy('coin_id', 'coin_type')
            ->get();

        return [
            'overview' => [
                'total_transactions' => (int) $summary->total_transactions,
                'confirmed_transactions' => (int) $summary->confirmed_transactions,
                'pending_transactions' => (int) $summary->pending_transactions,
                'failed_transactions' => (int) $summary->failed_transactions,
                'total_amount' => (float) $summary->total_amount,
                'confirmed_amount' => (float) $summary->confirmed_amount,
                'total_fees' => (float) $summary->total_fees,
                'total_usd_value' => (float) $summary->total_usd_value,
                'average_amount' => (float) $summary->average_amount,
                'max_amount' => (float) $summary->max_amount,
                'min_amount' => (float) $summary->min_amount,
            ],
            'status_breakdown' => $statusBreakdown->map(function ($item) {
                return [
                    'status' => $item->status,
                    'count' => (int) $item->count,
                    'total_amount' => (float) $item->total_amount,
                    'label' => $this->getStatusLabel($item->status),
                ];
            })->values(),
            'coin_breakdown' => $coinBreakdown->map(function ($item) {
                return [
                    'coin_id' => $item->coin_id,
                    'coin_type' => $item->coin_type,
                    'coin_name' => $item->coin->name ?? $item->coin_type,
                    'count' => (int) $item->count,
                    'total_amount' => (float) $item->total_amount,
                ];
            }),
        ];
    }

    /**
     * Get status label in Persian
     */
    private function getStatusLabel(string $status): string
    {
        return match ($status) {
            'pending' => 'در انتظار',
            'confirmed' => 'تأیید شده',
            'failed' => 'ناموفق',
            'cancelled' => 'لغو شده',
            'processing' => 'در حال پردازش',
            default => 'نامشخص',
        };
    }
}
