<?php

namespace App\Services;

use App\Models\JibitPayment;
use App\Models\User;
use App\Models\Transaction;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class JibitPaymentService
{
    protected $baseUrl;
    protected $apiKey;
    protected $secretKey;
    protected $callbackUrl;
    protected $accessToken;

    public function __construct()
    {
        $this->baseUrl = config('payment.drivers.jibit_pip.base_url');
        $this->apiKey = config('payment.drivers.jibit_pip.api_key');
        $this->secretKey = config('payment.drivers.jibit_pip.secret_key');
        $this->callbackUrl = config('payment.drivers.jibit_pip.callback_url');
    }

    /**
     * Get access token from Jibit
     */
    protected function getAccessToken(): string
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        try {
            $response = Http::timeout(30)->post("{$this->baseUrl}/v1/tokens/generate", [
                'apiKey' => $this->apiKey,
                'secretKey' => $this->secretKey
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $this->accessToken = $data['accessToken'];
                return $this->accessToken;
            }

            throw new \Exception('Failed to get access token: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('Jibit Token Generation Error', [
                'error' => $e->getMessage()
            ]);
            throw new \Exception('خطا در دریافت توکن دسترسی');
        }
    }

    /**
     * Create a new payment identifier
     */
    public function createPaymentIdentifier(User $user, string $description = null): array
    {
        try {
            DB::beginTransaction();

            // Get user's approved IBANs from cards table
            $userIbans = $user->cards()
                ->where('status', 'approved')
                ->whereNotNull('iban')
                ->pluck('iban')
                ->filter()
                ->values()
                ->toArray();

            if (empty($userIbans)) {
                throw new \Exception('کاربر هیچ شماره شبا (IBAN) تایید شده‌ای ندارد');
            }

            // Generate unique merchant reference number
            $merchantReferenceNumber = $this->generateMerchantReferenceNumber();

            // Prepare request data based on Jibit API
            $requestData = [
                'merchantReferenceNumber' => $merchantReferenceNumber,
                'userFullName' => "{$user->firstname} {$user->lastname}" ?? 'کاربر',
                'userMobile' =>  "0$user->phone" ?? '',
               // 'userIban' => $userIbans[0], // Primary IBAN (first approved one)
                'userIbans' => $userIbans, // All approved IBANs
                'callbackUrl' => $this->callbackUrl,
                'userRedirectUrl' => config('app.url') . '/payment/result'
            ];

            // Get access token
            $accessToken = $this->getAccessToken();
// dd($accessToken);
            // Send request to Jibit API
            $response = $this->sendApiRequest('/v1/paymentIds', $requestData, 'POST',  $accessToken);
dd($response);
            if (!$response['success']) {
                throw new \Exception($response['message'] ?? 'خطا در ایجاد شناسه پرداخت');
            }

            $responseData = $response['data'];

            // Create payment record (amount will be updated when payment is made)
            $payment = JibitPayment::create([
                'user_id' => $user->id,
                'payment_identifier' => $responseData['payId'] ?? $merchantReferenceNumber,
                'psp_switching_url' => null, // Jibit PIP doesn't provide direct payment URL
                'amount' => 0, // Amount will be set when payment is actually made
                'currency' => config('payment.drivers.jibit_pip.currency', 'T'),
                'description' => $description ?? config('payment.drivers.jibit_pip.description'),
                'status' => 'pending',
                'request_data' => $requestData,
                'response_data' => $responseData,
                'ip_address' => request()->ip(),
            ]);

            DB::commit();

            return [
                'success' => true,
                'data' => [
                    'payment_id' => $payment->id,
                    'payment_identifier' => $responseData['payId'] ?? $merchantReferenceNumber,
                    'merchant_reference_number' => $merchantReferenceNumber,
                    'user_token' => $responseData['userToken'] ?? null,
                    'registry_status' => $responseData['registryStatus'] ?? 'WAITING_FOR_USER',
                    'currency' => config('payment.drivers.jibit_pip.currency', 'T'),
                    'user_ibans' => $userIbans,
                ],
                'message' => 'شناسه پرداخت با موفقیت ایجاد شد. کاربر می‌تواند با هر مبلغی پرداخت کند.'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Jibit Payment Creation Error', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'خطا در ایجاد شناسه پرداخت: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get payment identifier status
     */
    public function getPaymentIdStatus(string $merchantReferenceNumber): array
    {
        try {
            $accessToken = $this->getAccessToken();

            // Get payment ID info from Jibit API
            $response = $this->sendApiRequest("/v1/paymentIds/{$merchantReferenceNumber}", [], 'GET', $accessToken);

            if (!$response['success']) {
                return [
                    'success' => false,
                    'message' => $response['message'] ?? 'خطا در دریافت وضعیت شناسه پرداخت'
                ];
            }

            return [
                'success' => true,
                'data' => $response['data']
            ];

        } catch (\Exception $e) {
            Log::error('Jibit Payment ID Status Error', [
                'merchant_reference_number' => $merchantReferenceNumber,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'خطا در دریافت وضعیت: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get payments for a merchant reference number
     */
    public function getPayments(string $merchantReferenceNumber): array
    {
        try {
            $accessToken = $this->getAccessToken();

            // Get payments from Jibit API
            $fromDate = now()->subDays(30)->format('Y-m-d\TH:i:s');
            $toDate = now()->format('Y-m-d\TH:i:s');

            $response = $this->sendApiRequest('/v1/payments/list', [
                'fromDate' => $fromDate,
                'toDate' => $toDate,
                'merchantReferenceNumber' => $merchantReferenceNumber,
                'pageNumber' => 0,
                'pageSize' => 50
            ], 'GET', $accessToken);

            if (!$response['success']) {
                return [
                    'success' => false,
                    'message' => $response['message'] ?? 'خطا در دریافت لیست پرداخت‌ها'
                ];
            }

            $payments = $response['data']['content'] ?? [];

            // Process successful payments
            foreach ($payments as $paymentData) {
                if ($paymentData['status'] === 'SUCCESSFUL') {
                    $this->processJibitPayment($paymentData);
                }
            }

            return [
                'success' => true,
                'data' => $payments
            ];

        } catch (\Exception $e) {
            Log::error('Jibit Get Payments Error', [
                'merchant_reference_number' => $merchantReferenceNumber,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'خطا در دریافت پرداخت‌ها: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Verify a specific payment
     */
    public function verifyPayment(string $externalReferenceNumber): array
    {
        try {
            $accessToken = $this->getAccessToken();

            // Verify payment with Jibit API
            $response = $this->sendApiRequest("/v1/payments/{$externalReferenceNumber}/verify", [], 'GET', $accessToken);

            if (!$response['success']) {
                return [
                    'success' => false,
                    'message' => $response['message'] ?? 'خطا در تایید پرداخت'
                ];
            }

            $paymentData = $response['data'];
            $isSuccessful = $paymentData['status'] === 'SUCCESSFUL';

            // Process payment if successful
            if ($isSuccessful) {
                $this->processJibitPayment($paymentData);
            }

            return [
                'success' => true,
                'data' => [
                    'status' => $paymentData['status'],
                    'amount' => $paymentData['amount'],
                    'is_successful' => $isSuccessful,
                    'bank_reference_number' => $paymentData['bankReferenceNumber'] ?? null,
                    'external_reference_number' => $paymentData['externalReferenceNumber'] ?? null,
                ],
                'message' => $isSuccessful ? 'پرداخت با موفقیت انجام شد' : 'پرداخت ناموفق بود'
            ];

        } catch (\Exception $e) {
            Log::error('Jibit Payment Verification Error', [
                'external_reference_number' => $externalReferenceNumber,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'خطا در تایید پرداخت: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process Jibit payment data and charge user balance
     */
    protected function processJibitPayment(array $paymentData): void
    {
        try {
            $merchantReferenceNumber = $paymentData['merchantReferenceNumber'] ?? null;
            $externalReferenceNumber = $paymentData['externalReferenceNumber'] ?? null;
            $amount = $paymentData['amount'] ?? 0;

            if (!$merchantReferenceNumber || !$amount) {
                Log::warning('Invalid Jibit payment data', $paymentData);
                return;
            }

            // Find payment record by merchant reference number
            $payment = JibitPayment::where('payment_identifier', $merchantReferenceNumber)->first();

            if (!$payment) {
                Log::warning('Jibit payment record not found', [
                    'merchant_reference_number' => $merchantReferenceNumber,
                    'external_reference_number' => $externalReferenceNumber
                ]);
                return;
            }

            // Skip if already processed
            if ($payment->isPaid()) {
                return;
            }

            DB::beginTransaction();

            // Update payment with actual amount and mark as paid
            $payment->update([
                'amount' => $amount, // Set the actual amount paid by user
                'status' => 'paid',
                'paid_at' => now(),
                'callback_data' => $paymentData,
                'reference_number' => $paymentData['bankReferenceNumber'] ?? null,
                'trace_number' => $externalReferenceNumber,
            ]);

            // Get user
            $user = $payment->user;

            // Store balance before transaction
            $balanceBefore = $user->toman_balance ?? 0;

            // Convert amount from Rial to Toman if needed
            // Jibit API returns amount in Rial, we need to convert to Toman
            $tomanAmount = $amount / 10; // Convert Rial to Toman

            // Add amount to user's toman balance
            $user->toman_balance = $balanceBefore + $tomanAmount;
            $user->save();

            // Create transaction record
            $user->transactions()->create([
                'type' => 'deposit',
                'amount' => $tomanAmount,
                'currency_id' => 3, // Toman/IRR currency ID
                'status' => 'done',
                'description' => "شارژ کیف پول تومانی از طریق جیبیت - {$payment->description}",
                'transaction_id' => $externalReferenceNumber ?? $payment->payment_identifier,
                'balance_before' => $balanceBefore,
                'balance_after' => $user->toman_balance,
                'ip' => $payment->ip_address,
                'details' => [
                    'jibit_payment_id' => $payment->id,
                    'bank_reference_number' => $paymentData['bankReferenceNumber'] ?? null,
                    'external_reference_number' => $externalReferenceNumber,
                    'merchant_reference_number' => $merchantReferenceNumber,
                    'bank' => $paymentData['bank'] ?? null,
                    'original_amount_rial' => $amount,
                    'converted_amount_toman' => $tomanAmount,
                ]
            ]);

            DB::commit();

            Log::info('Jibit Payment Processed Successfully', [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'amount_rial' => $amount,
                'amount_toman' => $tomanAmount,
                'new_balance' => $user->toman_balance,
                'external_reference_number' => $externalReferenceNumber
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error Processing Jibit Payment', [
                'payment_data' => $paymentData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Send API request to Jibit
     */
    protected function sendApiRequest(string $endpoint, array $data = [], string $method = 'POST', string $accessToken = null): array
    {
        try {
            $url = "{$this->baseUrl}{$endpoint}";

            $headers = [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => "Bearer {$accessToken}"
            ];

            // Add authorization header if access token is provided
            // if ($accessToken) {
            //     $headers['Authorization'] = "Bearer {$accessToken}";
            // }

            $response = Http::withHeaders($headers)->timeout(30);

            if ($method === 'POST') {
                $response = $response->post($url, $data);
            } elseif ($method === 'PUT') {
                $response = $response->put($url, $data);
            } else {
                // For GET requests, send data as query parameters
                $response = $response->get($url, $data);
            }

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            Log::error('Jibit API Error Response', [
                'endpoint' => $endpoint,
                'method' => $method,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [
                'success' => false,
                'message' => 'خطا در ارتباط با سرویس پرداخت',
                'error_code' => $response->status(),
                'error_data' => $response->json()
            ];

        } catch (\Exception $e) {
            Log::error('Jibit API Request Error', [
                'endpoint' => $endpoint,
                'method' => $method,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => "خطا در ارتباط با سرویس پرداخت: {$e->getMessage()}"
            ];
        }
    }

    /**
     * Generate unique merchant reference number
     */
    protected function generateMerchantReferenceNumber(): string
    {
        do {
            $identifier = 'MRN_' . time() . '_' . Str::random(8);
        } while (JibitPayment::where('payment_identifier', $identifier)->exists());

        return $identifier;
    }

    /**
     * Generate unique payment identifier (legacy method)
     */
    protected function generatePaymentIdentifier(): string
    {
        return $this->generateMerchantReferenceNumber();
    }
}
