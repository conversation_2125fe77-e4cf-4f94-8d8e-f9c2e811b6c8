<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;

class RSAService
{
    protected string $privateKey;
    protected string $publicKey;

    public function __construct()
    {
        $this->privateKey = Storage::get('keys/private.pem');
        $this->publicKey = Storage::get('keys/public.pem');
    }

    public function decrypt(string $encryptedBase64): array
    {
        $encrypted = base64_decode($encryptedBase64);
        $privateKeyResource = openssl_pkey_get_private($this->privateKey);

        if (!$privateKeyResource) {
            throw new \Exception("Invalid private key");
        }

        openssl_private_decrypt($encrypted, $decrypted, $privateKeyResource, OPENSSL_PKCS1_OAEP_PADDING);

        return json_decode($decrypted, true);
    }

    public function encrypt(array $data): string
    {
        $publicKeyResource = openssl_pkey_get_public($this->publicKey);

        if (!$publicKeyResource) {
            throw new \Exception("Invalid public key");
        }

        $json = json_encode($data);
        openssl_public_encrypt($json, $encrypted, $publicKeyResource, OPENSSL_PKCS1_OAEP_PADDING);

        return base64_encode($encrypted);
    }
}