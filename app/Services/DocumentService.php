<?php

namespace App\Services;

use App\Models\User;
use App\Models\Wallet;
use Closure;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class DocumentService
{
    public function show(User $user)
    {
        $user->load(['cards', 'documents']);
        $user->wallets = $user->wallets()->get()->keyBy(fn (Wallet $wallet, int $key) => strtoupper($wallet->currency->pluck('code')[0] ?? 'NULL'))->all();

        return $user;
    }

    public function update(User $user, array $data)
    {
        $data = Validator::make($data, [
            'national_id' => [
                Rule::unique('users')->ignore($user->id),
                fn (string $attribute, string $value, Closure $fail) => $this->check_national_id($value) ? true : $fail('کد ملی وارد شده معتبر نیست'),
            ],
            'gender' => 'in:male,female,undefined',
            'firstname' => 'between:3,64',
            'lastname' => 'between:3,64',
            'email' => ['email', Rule::unique('users')->ignore($user->id)],
        ]);
        $user->update($data->validated());

        return $user;
    }

    public function check_national_id(string $id): bool
    {
        if (preg_match('/^\d{10}$/', $id) && $id != str_repeat($id[0], 10)) {
            for ($i = 0, $sum = 0; $i < 9; $i++) {
                $sum += (10 - $i) * $id[$i];
            }
            $ret = $sum % 11;
            $parity = $id[9];
            if (($ret < 2 && $ret == $parity) || ($ret >= 2 && $ret == 11 - $parity)) {
                return true;
            }
        }

        return false;
    }

    public function index(string $filter = '')
    {
        $users = User::query();
        if ($filter) {
            $users->whereAny(['phone', 'email', 'firstname', 'lastname'], 'LIKE', "%$filter%");
        }

        return $users->paginate();
    }
}
