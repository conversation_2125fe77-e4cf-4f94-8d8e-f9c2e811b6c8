<?php

namespace App\Services\Kucoin;

use App\Repositories\Kucoin\AccountApiRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class AccountApiService extends Service implements ServicesContract
{
    public function __construct(
        protected AccountApiRepo $repo,
    ) {}

    public function listSubAccountApi()
    {
        return $this->repo->getAllRecords();
    }

    public function createSubAccountApi($validated)
    {
        return $this->repo->createRecord($validated);
    }
}
