<?php

namespace App\Services\Kucoin;

use App\Repositories\Kucoin\AccountRepo;
use App\Services\Service;
use App\Services\ServicesContract;

class AccountService extends Service implements ServicesContract
{
    public function __construct(
        protected AccountRepo $accountRepo,
    ) {}

    public function createAccount($validated)
    {
        return $this->accountRepo->createRecord($validated);
    }
}
