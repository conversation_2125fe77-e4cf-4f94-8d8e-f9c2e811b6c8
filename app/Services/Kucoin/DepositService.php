<?php

namespace App\Services\Kucoin;

use App\CurrencyProviders\KuCoinProvider;
use App\Services\Service;
use App\Services\ServicesContract;

class DepositService extends Service implements ServicesContract
{
    public function __construct(
        protected KuCoinProvider $provider,
    ) {}

    public function createDeposit($validated)
    {
        $params = [
            'currency' => $validated['currency'],
            'chain' => $validated['chain'] ?? null,
        ];

        return $this->provider->deposit($params);
    }
}
