<?php

namespace App\Providers;

use App\CurrencyProviders\PerfectMoneyVoucher;
use App\Models\Transaction;
use App\Observers\TransactionObserver;
use App\Observers\TronAddressObserver;
use App\Services\Admin\UserService;
use Illuminate\Support\ServiceProvider;
// use Mollsoft\LaravelTronModule\Models\TronAddress;
use Illuminate\Foundation\AliasLoader;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind('currencies.pmv', fn () => new PerfectMoneyVoucher);
        // $this->app->bind(UserService::class, fn() => new UserService());

        // $loader = AliasLoader::getInstance();
        // $loader->alias(alias: 'Tron', class: \Mollsoft\LaravelTronModule\Facades\Tron::class);

    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // TronAddress::observe(classes: TronAddressObserver::class);
        // Transaction::observe(classes: TransactionObserver::class);
    }
}
