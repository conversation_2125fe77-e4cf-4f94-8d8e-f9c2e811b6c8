<?php

namespace App\Providers;

use App\Http\Controllers\Admin\DashboardController;
use App\Policies\DashboardPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        DashboardController::class => DashboardPolicy::class,
    ];

    public function boot(): void
    {
        $this->registerPolicies();
    }
}