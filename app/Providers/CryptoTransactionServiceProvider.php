<?php

namespace App\Providers;

use App\Models\Currency;
use App\Models\Transaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;

class CryptoTransactionServiceProvider extends ServiceProvider
{
    public function register() {}

    public function boot() {}

    public function checkTransaction($currency, $transactionId, $network = null)
    {
        $currency = strtoupper($currency);
        $network = strtoupper($network);

        $existingTransaction = Transaction::where('transaction_id', $transactionId)->first();

        if ($existingTransaction) {
            return [
                'currency' => $currency,
                'network' => $network,
                'status' => 'duplicate',
                'message' => 'این تراکنش قبلاً ثبت شده است.',
            ];
        }

        $result = $this->handleCheckTransaction($currency, $transactionId, $network);

        if ($result['status'] === 'success') {
            $currency = Currency::where('name', $currency)->firstOrFail();

            Transaction::create([
                'transaction_id' => $transactionId,
                'currency_id' => $currency->id,
                'wallet_address' => $result['wallet_address'] ?? null,
                'network' => $network,
                'user_id' => Auth::user()->id,
            ]);
        }

        return $result;
    }

    private function handleCheckTransaction($currency, $transactionId, $network)
    {
        switch ($currency) {
            case 'BTC':
                return $this->checkBitcoinTransaction($transactionId);
            case 'ETH':
                return $this->checkEthereumTransaction($transactionId);
            case 'LTC':
                return $this->checkLitecoinTransaction($transactionId);
            case 'XRP':
                return $this->checkRippleTransaction($transactionId);
            case 'BCH':
                return $this->checkBitcoinCashTransaction($transactionId);
            case 'ADA':
                return $this->checkCardanoTransaction($transactionId);
            case 'DOT':
                return $this->checkPolkadotTransaction($transactionId);
            case 'BNB':
                return $this->checkBinanceCoinTransaction($transactionId);
            case 'LINK':
                return $this->checkChainlinkTransaction($transactionId);
            case 'XLM':
                return $this->checkStellarTransaction($transactionId);
            case 'USDT':
                if ($network === 'TRC20') {
                    return $this->checkTetherTRC20Transaction($transactionId);
                } else {
                    return $this->checkTetherERC20Transaction($transactionId);
                }
            case 'TRX':
                return $this->checkTronTransaction($transactionId);
            case 'DOGE':
                return $this->checkDogecoinTransaction($transactionId);
            case 'XMR':
                return $this->checkMoneroTransaction($transactionId);
            case 'DASH':
                return $this->checkDashTransaction($transactionId);
            case 'ZEC':
                return $this->checkZcashTransaction($transactionId);
            default:
                return [
                    'currency' => $currency,
                    'network' => $network,
                    'status' => 'unsupported',
                    'message' => 'ارز یا شبکه پشتیبانی نمی‌شود.',
                ];
        }
    }

    private function checkBitcoinTransaction($transactionId)
    {
        $url = "https://api.blockcypher.com/v1/btc/main/txs/{$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['total'] / 1e8;

                return [
                    'currency' => 'BTC',
                    'amount' => $amount,
                    'wallet_address' => $data['outputs'][0]['addresses'][0],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'BTC',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkEthereumTransaction($transactionId)
    {
        $apiKey = env('ETHERSCAN_API_KEY');
        $url = "https://api.etherscan.io/api?module=proxy&action=eth_getTransactionByHash&txhash={$transactionId}&apikey={$apiKey}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data['result']) {
                $amount = hexdec($data['result']['value']) / 1e18;

                return [
                    'currency' => 'ETH',
                    'amount' => $amount,
                    'wallet_address' => $data['result']['to'],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'ETH',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkLitecoinTransaction($transactionId)
    {
        $url = "https://api.blockcypher.com/v1/ltc/main/txs/{$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['total'] / 1e8;

                return [
                    'currency' => 'LTC',
                    'amount' => $amount,
                    'wallet_address' => $data['outputs'][0]['addresses'][0],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'LTC',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkRippleTransaction($transactionId)
    {
        $url = "https://data.ripple.com/v2/transactions/{$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data['result']) {
                $amount = $data['amount'];

                return [
                    'currency' => 'XRP',
                    'amount' => $amount,
                    'wallet_address' => $data['destination'],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'XRP',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkBitcoinCashTransaction($transactionId)
    {
        $url = "https://blockchair.com/bitcoin-cash/raw/transaction/{$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['data']['outputs'][0]['value'] / 1e8;

                return [
                    'currency' => 'BCH',
                    'amount' => $amount,
                    'wallet_address' => $data['data']['outputs'][0]['recipient'],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'BCH',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkCardanoTransaction($transactionId)
    {
        $url = "https://explorer.cardano.org/api/txs/summary/{$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['Right']['ctAmount']['getCoin'] / 1e6;

                return [
                    'currency' => 'ADA',
                    'amount' => $amount,
                    'wallet_address' => $data['Right']['ctOutputs'][0]['getAddress'],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'ADA',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkPolkadotTransaction($transactionId)
    {
        $url = "https://polkadot.api.subscan.io/api/scan/extrinsic/{$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['data']['extrinsic']['payout']['amount'];

                return [
                    'currency' => 'DOT',
                    'amount' => $amount,
                    'wallet_address' => $data['data']['extrinsic']['payout']['address'],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'DOT',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkBinanceCoinTransaction($transactionId)
    {
        $apiKey = env('BSCSCAN_API_KEY');
        $url = "https://api.bscscan.com/api?module=transaction&action=gettxreceiptstatus&txhash={$transactionId}&apikey={$apiKey}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data['status'] == '1') {
                return [
                    'currency' => 'BNB',
                    'amount' => 'Unknown',
                    'wallet_address' => 'Not available in API',
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'BNB',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkChainlinkTransaction($transactionId)
    {
        return $this->checkEthereumTransaction($transactionId);
    }

    private function checkStellarTransaction($transactionId)
    {
        $url = "https://horizon.stellar.org/transactions/{$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['amount'];

                return [
                    'currency' => 'XLM',
                    'amount' => $amount,
                    'wallet_address' => $data['to'],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'XLM',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkTetherERC20Transaction($transactionId)
    {
        $apiKey = env('ETHERSCAN_API_KEY');
        $url = "https://api.etherscan.io/api?module=proxy&action=eth_getTransactionByHash&txhash={$transactionId}&apikey={$apiKey}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data['result']) {
                $amount = hexdec($data['result']['value']) / 1e18;

                return [
                    'currency' => 'USDT',
                    'network' => 'ERC20',
                    'amount' => $amount,
                    'wallet_address' => $data['result']['to'],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'USDT',
            'network' => 'ERC20',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkTetherTRC20Transaction($transactionId)
    {
        $url = "https://apilist.tronscan.org/api/transaction-info?hash={$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['amount'] / 1e6;

                return [
                    'currency' => 'USDT',
                    'network' => 'TRC20',
                    'amount' => $amount,
                    'wallet_address' => $data['to'],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'USDT',
            'network' => 'TRC20',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkTronTransaction($transactionId)
    {
        $url = "https://apilist.tronscan.org/api/transaction-info?hash={$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['amount'] / 1e6;

                return [
                    'currency' => 'TRX',
                    'amount' => $amount,
                    'wallet_address' => $data['to'],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'TRX',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkDogecoinTransaction($transactionId)
    {
        $url = "https://api.blockcypher.com/v1/doge/main/txs/{$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['total'] / 1e8;

                return [
                    'currency' => 'DOGE',
                    'amount' => $amount,
                    'wallet_address' => $data['outputs'][0]['addresses'][0],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'DOGE',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkMoneroTransaction($transactionId)
    {
        return [
            'currency' => 'XMR',
            'amount' => 'Unknown',
            'status' => 'pending',
            'message' => 'Monero API integration نیاز به تنظیمات بیشتر دارد',
        ];
    }

    private function checkDashTransaction($transactionId)
    {
        $url = "https://insight.dashevo.org/insight-api/tx/{$transactionId}";
        $response = Http::get($url);

        if ($response->successful()) {
            $data = $response->json();
            if ($data) {
                $amount = $data['vout'][0]['value'];

                return [
                    'currency' => 'DASH',
                    'amount' => $amount,
                    'wallet_address' => $data['vout'][0]['scriptPubKey']['addresses'][0],
                    'status' => 'success',
                ];
            }
        }

        return [
            'currency' => 'DASH',
            'amount' => 0,
            'status' => 'failed',
            'message' => 'تراکنش ناموفق بود.',
        ];
    }

    private function checkZcashTransaction($transactionId)
    {
        return [
            'currency' => 'ZEC',
            'amount' => 'Unknown',
            'status' => 'pending',
            'message' => 'Zcash API integration نیاز به تنظیمات بیشتر دارد',
        ];
    }
}
