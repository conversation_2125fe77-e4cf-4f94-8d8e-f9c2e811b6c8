name: Project Deployment

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  Deploy-To-Dev:
    name: Build and Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Run Deployment
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST_IP }}
          username: root
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cd /var/www/api.exchangim.com/html/laracoin-ex
            git reset --hard
            git pull origin main
            composer install --prefer-dist --no-dev -o
            composer dump-autoload
            chmod -R 775 storage
            php artisan config:cache
            php artisan config:clear
