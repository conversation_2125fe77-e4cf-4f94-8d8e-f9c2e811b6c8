/* Estilos para la página de documentos */

/* Estilos para las tarjetas de estadísticas */
.stat-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
    background: linear-gradient(135deg, #5a67d8 0%, #4c51bf 100%);
    color: white;
}

.stat-card.warning {
    background: linear-gradient(135deg, #ecc94b 0%, #d69e2e 100%);
    color: white;
}

.stat-card.success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.stat-card.danger {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    color: white;
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 1.5rem;
    opacity: 0.8;
}

.stat-content {
    flex-grow: 1;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Estilos para la tabla de documentos */
.table > :not(caption) > * > * {
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(90, 103, 216, 0.05);
}

/* Estilos para las insignias de estado */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    border-radius: 6px;
}

.bg-success-subtle {
    background-color: rgba(72, 187, 120, 0.15);
}

.bg-warning-subtle {
    background-color: rgba(236, 201, 75, 0.15);
}

.bg-danger-subtle {
    background-color: rgba(229, 62, 62, 0.15);
}

.text-success {
    color: #38a169 !important;
}

.text-warning {
    color: #d69e2e !important;
}

.text-danger {
    color: #c53030 !important;
}

/* Estilos para las miniaturas de documentos */
.img-thumbnail {
    cursor: pointer;
    transition: transform 0.2s ease;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.img-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Estilos para los botones de acción */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 6px;
}

/* Estilos para el modal de documentos */
.modal-content {
    border-radius: 15px;
    overflow: hidden;
}

.modal-header {
    background-color: #f8fafc;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-footer {
    background-color: #f8fafc;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Animaciones para los elementos de la página */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    animation: fadeIn 0.5s ease-in-out;
}

.card:nth-child(2) {
    animation-delay: 0.1s;
}

.card:nth-child(3) {
    animation-delay: 0.2s;
}

.card:nth-child(4) {
    animation-delay: 0.3s;
}

/* Estilos para los filtros */
.dropdown-menu {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: 6px;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(90, 103, 216, 0.1);
}

/* Estilos para la barra de búsqueda */
.input-group {
    border-radius: 10px;
    overflow: hidden;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* Estilos para la paginación */
.pagination-container .pagination {
    margin-bottom: 0;
}

.pagination-container .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination-container .page-link {
    color: var(--primary-color);
    border-radius: 5px;
    margin: 0 2px;
}

.pagination-container .page-link:hover {
    background-color: rgba(90, 103, 216, 0.1);
}

/* Estilos para los mensajes de alerta */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.alert-success {
    background-color: rgba(72, 187, 120, 0.15);
    color: #38a169;
}

.alert-warning {
    background-color: rgba(236, 201, 75, 0.15);
    color: #d69e2e;
}

.alert-danger {
    background-color: rgba(229, 62, 62, 0.15);
    color: #c53030;
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .stat-icon {
        font-size: 2rem;
        margin-right: 1rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
}
