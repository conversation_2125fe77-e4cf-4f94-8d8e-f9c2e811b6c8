<?xml version="1.0" encoding="UTF-8"?>
<svg width="48px" height="48px" viewBox="0 0 48 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 56.2 (81672) - https://sketch.com -->
    <title>Bank/Color/Kosar</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <radialGradient cx="39.0714864%" cy="33.6465459%" fx="39.0714864%" fy="33.6465459%" r="63.6014155%" gradientTransform="translate(0.390715,0.336465),scale(1.000000,0.885508),rotate(-35.430156),translate(-0.390715,-0.336465)" id="radialGradient-1">
            <stop stop-color="#EC008C" stop-opacity="0.407843" offset="0%"></stop>
            <stop stop-color="#621A4F" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="80.2627389%" cy="52.8379425%" fx="80.2627389%" fy="52.8379425%" r="110.527088%" gradientTransform="translate(0.802627,0.528379),scale(1.000000,0.427929),rotate(-95.430019),translate(-0.802627,-0.528379)" id="radialGradient-2">
            <stop stop-color="#EC008C" stop-opacity="0.407843" offset="0%"></stop>
            <stop stop-color="#621A4F" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="40.6820104%" cy="84.5680701%" fx="40.6820104%" fy="84.5680701%" r="95.7214062%" gradientTransform="translate(0.406820,0.845681),scale(0.465873,1.000000),rotate(24.569930),translate(-0.406820,-0.845681)" id="radialGradient-3">
            <stop stop-color="#EC008C" stop-opacity="0.407843" offset="0%"></stop>
            <stop stop-color="#621A4F" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="72.8219499%" cy="23.8021376%" fx="72.8219499%" fy="23.8021376%" r="73.2794249%" id="radialGradient-4">
            <stop stop-color="#FFE293" offset="0%"></stop>
            <stop stop-color="#FAA61A" offset="100%"></stop>
        </radialGradient>
    </defs>
    <g id="Ready-For-Export" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Artboard" transform="translate(-1744.000000, -80.000000)">
            <g id="Banks_(Color)" transform="translate(80.000000, 80.000000)">
                <g id="Bank-/-Kosar-/-Color" transform="translate(1664.000000, 0.000000)">
                    <path d="M37.1744,37.7056681 C33.8627,40.8918681 29.4415,42.6734681 24.8459,42.6734681 C17.5069,42.6734681 10.8889,38.1298681 8.2519,31.2810681 C8.2018,31.1457681 8.0859,31.0449681 7.945,31.0142681 C7.8051,30.9788681 7.6564,31.0171681 7.5512,31.1158681 L0.1423,37.8771681 C0.0544,37.9552681 0.003,38.0665681 0.0004,38.1840681 C-0.0048,38.3013681 0.0389,38.4156681 0.1211,38.4994681 C4.8808,43.5251681 11.506,46.3755681 18.4279,46.3755681 C26.0847,46.3755681 33.339,42.8877681 38.1206,36.9075681 C38.1301,36.8963681 38.1353,36.8820681 38.1353,36.8673681 C38.1353,36.8331681 38.1072,36.8050681 38.073,36.8050681 C38.0547,36.8050681 38.0372,36.8131681 38.0254,36.8271681 C37.753,37.1235681 37.4693,37.4163681 37.1744,37.7056681 Z M27.7481,14.2128681 C35.3329,16.4054681 40.5915,23.3995681 40.5915,31.2949681 C40.5915,35.3624681 39.1958,39.3094681 36.6388,42.4727681 C36.5461,42.5838681 36.5165,42.7350681 36.5605,42.8728681 C36.5999,43.0118681 36.7079,43.1214681 36.8463,43.1628681 L46.406,46.1963681 C46.4483,46.2095681 46.4924,46.2162681 46.5367,46.2162681 C46.7296,46.2162681 46.9008,46.0889681 46.9564,45.9041681 C47.6485,43.5736681 48,41.1552681 48,38.7241681 C48,26.3165681 38.8436,15.6599681 26.5775,13.7916681 C26.5463,13.7861681 26.5158,13.8055681 26.5076,13.8360681 C26.5061,13.8414681 26.5054,13.8470681 26.5054,13.8526681 C26.5054,13.8819681 26.5257,13.9075681 26.5542,13.9143681 C26.955,13.9990681 27.3529,14.0985681 27.7481,14.2128681 Z M12.1173,34.1239681 C11.7725,32.7284681 11.5982,31.2962681 11.5982,29.8587681 C11.5982,20.1041681 19.625,12.0772681 29.3797,12.0772681 C30.3042,12.0772681 31.2273,12.1493681 32.1405,12.2928681 C32.1618,12.2960681 32.1833,12.2975681 32.2047,12.2975681 C32.4463,12.2975681 32.645,12.0988681 32.645,11.8572681 C32.645,11.8272681 32.642,11.7973681 32.6359,11.7679681 L30.483,1.97326813 C30.459,1.85856813 30.3885,1.75886813 30.2883,1.69806813 C30.1894,1.63396813 30.0681,1.61396813 29.9538,1.64306813 C18.6456,4.34576813 10.6028,14.5372681 10.6028,26.1640681 C10.6028,29.3047681 11.1897,32.4180681 12.3332,35.3432681 C12.3449,35.3724681 12.3767,35.3888681 12.4073,35.3813681 C12.4343,35.3743681 12.4533,35.3498681 12.4533,35.3219681 C12.4533,35.3147681 12.4521,35.3076681 12.4496,35.3009681 C12.3268,34.9156681 12.2146,34.5197681 12.1173,34.1239681 Z" id="Shape" fill="#832D6A" fill-rule="evenodd"></path>
                    <path d="M31.8124,11.2111681 L29.9221,2.61676813 C29.9086,2.55046813 29.8679,2.49286813 29.8099,2.45806813 C29.7526,2.42176813 29.6824,2.41166813 29.6172,2.43046813 C19.0726,5.18176813 11.5667,14.6459681 11.2895502,25.5401681 C11.2883,25.5723681 11.3126,25.6003681 11.3446,25.6036681 C11.3759,25.6087681 11.406,25.5880681 11.4124,25.5570681 C13.5872,16.4987681 22.2871,10.4285681 31.5393,11.5138681 C31.5491,11.5150681 31.5589,11.5156681 31.5687,11.5156681 C31.7057,11.5156681 31.8185,11.4028681 31.8185,11.2658681 C31.8185,11.2474681 31.8164,11.2291681 31.8124,11.2111681 Z" id="Shape" fill="url(#radialGradient-1)" fill-rule="nonzero"></path>
                    <path d="M37.7417,42.7268681 L46.1287,45.3876681 C46.153,45.3953681 46.1784,45.3991681 46.2039,45.3991681 C46.3161,45.3991681 46.4155,45.3241681 46.4462,45.2162681 C49.329,34.7107681 44.8863,23.4844681 35.5952,17.7966681 C35.586,17.7917681 35.5758,17.7891681 35.5653,17.7891681 C35.5305,17.7891681 35.5018,17.8178681 35.5018,17.8527681 C35.5018,17.8688681 35.508,17.8844681 35.519,17.8961681 C42.2742,24.3097681 43.1808,34.8774681 37.6168,42.3478681 C37.5685,42.4128681 37.5551,42.4976681 37.5808,42.5743681 C37.6072,42.6478681 37.667,42.7044681 37.7417,42.7268681 Z" id="Shape" fill="url(#radialGradient-2)" fill-rule="nonzero"></path>
                    <path d="M7.4856,32.1044681 L0.9869,38.0316681 C0.9352,38.0763681 0.9052,38.1411681 0.90425349,38.2094681 C0.903,38.2772681 0.929,38.3428681 0.9763,38.3915681 C8.6331,46.1454681 20.5811,47.9128681 30.1549,42.7077681 C30.184,42.6927681 30.1962,42.6569681 30.1824,42.6273681 C30.1707,42.5982681 30.1385,42.5825681 30.1084,42.5913681 C21.1759,45.2370681 11.5686,40.7381681 7.8815,32.1827681 C7.8509,32.1083681 7.7847,32.0541681 7.7058,32.0388681 C7.626,32.0204681 7.5423,32.0454681 7.4856,32.1044681 Z" id="Shape" fill="url(#radialGradient-3)" fill-rule="nonzero"></path>
                    <path d="M24.2645,20.0034681 C29.0545,19.2225681 33.5773,22.4773681 34.3583,27.2673681 C35.1392,32.0572681 31.8844,36.5801681 27.0945,37.3610681 C22.3045,38.1420681 17.7816,34.8872681 17.0007,30.0972681 C16.2197,25.3073681 19.4745,20.7844681 24.2645,20.0034681 Z" id="Shape" fill="#FAA61A" fill-rule="evenodd"></path>
                    <path d="M18.0606,25.8626681 C19.614,21.6588681 24.2881,19.5069681 28.492,21.0603681 C32.6959,22.6137681 34.8477,27.2879681 33.2943,31.4917681 C31.7409,35.6956681 27.0668,37.8474681 22.8629,36.2940681 C18.6591,34.7406681 16.5072,30.0665681 18.0606,25.8626681 Z" id="Shape" fill="url(#radialGradient-4)" fill-rule="evenodd" transform="translate(25.677456, 28.677224) scale(-1, 1) translate(-25.677456, -28.677224) "></path>
                </g>
            </g>
        </g>
    </g>
</svg>